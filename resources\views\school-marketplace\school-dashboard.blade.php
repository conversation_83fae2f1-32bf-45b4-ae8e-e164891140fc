@extends('school-marketplace.layouts.masterDashboard')

@section('content')

@include('school-marketplace.layouts.instructor-public-profile')

<div class="container mt-4">
    <!-- <div class="d-flex justify-content-between align-items-center"> -->

    <div class="" style="padding:3rem 0rem">
        <h4 class="text-primary admin">Hi, {{ auth()->user()->full_name }}! 👋</h4>
        <p class="tit mb-5">What would you like to do today?</p>
    </div>
    <style>
        .hover_white:hover {
            color: #fff !important;
        }

        .post-arequirement-btn {
            display: inline-block;
            font-weight: 500;
            font-size: 17px;
            background-color: #0056d2;
            color: white;
            padding: 4px 18px;
            border-radius: 35px;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .req-heading {
            font-size: 18px;
            color: #004AAD;
            font-weight: 600;
        }

        .searc-box {
            padding: 10px;
        }

        .border-container {
            border: 1px solid rgba(217, 217, 217, 1);
            border-radius: 13.5px;
        }

        .main-cards {
            padding: 30px;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 1);
            border: 1px solid rgba(233, 233, 233, 1);
            transition: box-shadow 0.3s ease, transform 0.3s ease;
        }

        .main-cards:hover {
            box-shadow: 0 0 15px 5px rgba(39, 38, 38, 0.4);
            /* Evenly spread shadow */
            transform: translateY(-5px);
            /* Slight lift effect */
        }

        .status-badge {
            height: 30px;
            border-radius: 15px;
            padding: 5px 10px;
            gap: 10px;
            font-weight: 400;
            font-size: 14px;
            line-height: 19.5px;
            letter-spacing: 0px;
            text-transform: capitalize;
        }

        .open {
            background: rgba(0, 122, 0, 1);
        }

        .filled {
            background: rgba(29, 155, 240, 1);
        }

        .draft {
            background: rgba(255, 127, 0, 1);
        }

        .archived {
            background: rgba(207, 19, 19, 1);
        }

        .row-gap {
            row-gap: 10px;
        }

        .padding {
            padding: 40px;
        }

        .common-button {
            border-radius: 60px;
            padding: 6px 11px;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 76, 189, 1);
            border: 1px solid rgba(0, 76, 189, 1);
        }

        .applicants-badge-count {
            /* width: 191px; */
            /* height: 41px; */
            padding: 8px;
            font-size: 14px;
            border-radius: 8px;
            color: rgba(0, 0, 0, 1);
            background: rgba(73, 171, 216, 0.12);
        }

        .card-label {
            font-weight: 400;
            font-size: 16px;
            line-height: 22px;
            color: rgba(0, 0, 0, 1);
        }

        .card-description {
            font-size: 14px;
            color: rgba(0, 0, 0, 1);
        }

        .txt-gray {
            color: rgba(217, 217, 217, 1);
        }

        .btn-group .btn {
            outline: none !important;
        }

        .btn-link:hover {
            color: var(--bs-btn-hover-color) !important;
            text-decoration: underline;
        }

        .btn-link:focus {
            color: var(--bs-btn-hover-color) !important;
            text-decoration: underline;
        }

        .req-search-box .fa {
            margin-left: 6px;
        }

        .discover_card {
            transition: box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
            cursor: pointer;
        }

        .discover_card:hover {
            box-shadow: 0px 4px 7px #d4caca;
            transform: translateY(-5px);
        }

        .heading_text {

            color: #004CBD;
            font-size: 18px !important;

        }

        .new_card_text {
            font-size: 14px;
        }


        .bold {
            font-weight: bold;
        }

        input.instructional_days::placeholder {
            color: #C7C8C9;
        }

        .custom-select-wrapper {
            position: relative;
            display: inline-block;
            width: 200px;
        }


        .custom-select-arrow {
            position: absolute;
            top: 50%;
            right: 31px;
            transform: translateY(-50%);
            pointer-events: none;
        }

        select.delivery_mode {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border-radius: 32px !important;
            border: 1px solid black;
            background-color: white;
            font-size: 16px;
            cursor: pointer;

            /* Fully hide native dropdown arrow across all major browsers */
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: none;
        }

        .budget_management_input {
            width: 149% !important;
            margin-left: -55px;
        }

        .input_absolute {
            top: 8px;
            left: 75px;
            right: 25px;
            width: max-content;
            background-color: white;
            border: none;
        }

        .input_absolute_students {
            top: 8px;
            left: 50px;
            right: 25px;
            width: max-content;
            background-color: white;
            border: none;
        }

        .select2-container--bootstrap4 .select2-selection:hover {
            outline: none !important;
            box-shadow: none !important;
            border-color: black !important;
            /* or any neutral color you want */
        }
    </style>


    <!-- </div> -->

    <div class="recordvideo-container">
        <button class="btn btn-primary video_record" id="recordVideo" type="button">Record Video</button>
    </div>

    @include('web.onboarding-new.modals.video-record')
    @include('school-marketplace.modals.stopRecording')
    <ul id="chat-bot-chat-history">

    </ul>
    <div class="row mt-4">
        <div class="col-md-8 order-2 order-md-1">
            <div class="row g-3 ">
                <div class="col-md-4">
                    <div data-bs-toggle="tooltip" data-bs-placemen="top" title="Start a new class requirement" class="card d-flex flex-column h-100 bg-transparent" style="border:none;">
                        <a class="card card-custom shadow-sm post-requirement-btn" href="{{ route('post-requirements') }}">
                            <div class="d-flex card-title">
                                <span><svg width="28" height="28" viewBox="0 0 28 28" fill="none"
                                        xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <rect width="28" height="28" fill="url(#pattern0_10168_2177)" />
                                        <defs>
                                            <pattern id="pattern0_10168_2177" patternContentUnits="objectBoundingBox"
                                                width="1" height="1">
                                                <use xlink:href="#image0_10168_2177" transform="scale(0.00195312)" />
                                            </pattern>
                                            <image id="image0_10168_2177" width="512" height="512"
                                                preserveAspectRatio="none"
                                                xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAACXBIWXMAAHYcAAB2HAGnwnjqAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAIABJREFUeJzt3XmUXVWd6PFvJWQiEGYIghKQICJzgNYGm0EEJICCMog4tVOj8tB2AG3b1tZ2lqXtst9TQX0OLSKg0IiAAqJMAspkcAAhyhQIYUzInHp/7KpnUVaq6t579v2dc/f3s9ZvZa2WPue3971371+dYW+QJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEnD9UUnoHDTgG0HYgtgU2AzYJNhMXngv99o4N/JwPSuZio1wyrgqYF4GlgCPAE8Atw1JO4EFgTlKFkAFGIS8DxgV2BnYDtg1kBsEZaVpMXArcAvB+IaUrEgZWcB0HumAXsBf0ea8HcBduKvf8FLqq/VwO3AL4CLgCsG/m9S5SwAmm8LYB9gDrAvsB8wNTQjSVV5FPgx8APgJ6TbC1IlLACaZwrwYuAw4GWkv+4l9b77ge8DXwbuDs5FPcACoBm2Bo4kTfgH4cN3UslWAz8EPg9cH5yLGswCoL6eBbwKOA54ETAhNh1JNXQtcAapIFgTnIsaxgKgXjYFjidN+vvhpC9pfH4D/DNwVXQiag4LgHqYA7wVeC3pKX5JasdFwLtJ6wxIo7IAiDMTeAPwJmD72FQk9ZAVwH8CH8c1BTQKC4Du2wN4D+ky/6TgXCT1rr8Arwd+HpyHasoCoHv2A04D5mK/S+qOfuBLwPuB5cG5qGaciPJah3Rf/734vr6kOLcBJ5FWGZQAC4BcJgKvBv4N7+9LqodlwCnAmdGJqB4mRifQY/pIC/acDZwMbBybjiT9f+sAR5HGpctItwdUMK8AVOcw4NOkDXgkqc4uAF5D2qpYhbIA6Nxs4D+AY6MTkaQW3Ea6YvmX6EQUwwKgfRsCHwbeia/zSWqm+4FDgDuiE1H3WQC0rg94M/AJ0tK9ktRki0gbjd0YnYi6ywKgNc8FvgK8JDoRSarQEuBo4KfRiah73GxmfNYBTgVuxclfUu+ZDvwPcEx0IuoerwCMbQ5wFrBbdCKSlNkq4I3Ad6ITUX6uA7B2E4D/RXqnf6vgXCSpGyaQbgU8CVwfnIsyswAY2XNI78m+DftIUln6gENJW5P/LDgXZeTk9reOBX4M7BidiCQF2o/0ptOluGpgT7IA+KvpwDeAj5EqX0kq3T6kK6IXYRHQc3wIMNkeOB/YJToRSaqhC4HjSRsKqUf4GiAcDtyAk78krc1RwMXA+tGJqDolFwB9wGmkd183Cs5FkuruQOByYJPoRFSNUm8BTAe+R9oIQ5I0freR3hJYEJ2IOlNiAbAl6a/+OdGJSFJD3UPaROiu6ETUvtIKgBeQXvHbJjoRSWq4BaQi4PboRNSekgqAg4FzgQ2iE6mpJcB8UmV/D2mb0IXAo6TdwhYBS4HHB/77FQP/P1LJSn81zp0EG6yUAuBE4JvApOA86mApMI+0sdHtpPt5vyVN9pJaU3oBAPAU8HLgyuhEpOHeAqwm/VBLjAdJzzycRlrZa0pn3SlpiOjfd11iGe4kqJp5O+VN/ktJe3qfhg86SrlF/97rFIM7CUrhPkj8D6Jb8RhpGeO5wLpVdJ6kcYn+7dctVgPv7KhHpQ59jPgfQu54irRn95F4WV+KEj0O1DX+tZNOldr1L8R/+XPGPNLl/Y2r6jBJbYseD+ocX6Ls1WbVZacS/6XPEU8DXwH2rK6rJFUgemyoe3wdd51VF7wBWEP8F77KeBj4FPCs6rpJUoWix4gmxAXA1HY7WBrLifTW0/7zgbfhj0aqu+ixoilxGWkPFqlSBwDLif+CVxEPke7vO/FLzRA9ZjQpbsCdBFWhnUlL00Z/sTuNRcC7gWnVdo+kzKLHjqbFrcDMtnpaGmJL0qXy6C90J7GS9HDfZtV2jaQuiR5Dmhh3A89tp7MlSPeSbiL+i9xJXArsVHXHSOqq6HHk2hrk0E7ch+Of2jABuJD4L3C78RBwQuW9IilC9HgynfTHRHQe7cRCYO/Wu1wl+3fiv7jtxjnAptV3iaQg0WMKwGTS2BKdSzuxGHhpa12uUh1BM1/3uw84NEN/SIoVPbYMmgicVYN82omlwFHj73KVaDZp05voL2urcT6++iL1qujxZag+4LM1yKmdcCdBrdV04Hbiv6StxNOkpYkl9a7ocWYkp9Ugr3ZiDel1aOkZvkP8l7OVuAPYMUtPSKqT6LFmbd5BM2+X9gMfGqVdKsxJxH8hW4kLgA2y9ISkuokeb0ZzIrCiBjm2E+4kKGbRnJX+VpEuvfXl6AhJtRQ97ozlSNJDdtF5thNn4U6CxZoAXEn8l3A8sZj0Q5NUluixZzz+AXiiBrm2E+4kWKgPEf/lG088AMzJ1AeS6i16/BmvOaTtxaPzbSeuANZvoa1quDk0497V7cBzMvWBpPqLHoNasRNpTZLonNuJa4GNWmyvGmgd4NfEf+HGihvx/X6pdNHjUKu2Af5Yg7zbiXnAVm20WQ3yQeK/aGPFVcCMXB0gqTGix6J2bAHcXIPc2wl3EuxhO5AW0In+ko0WPwGm5eoASY0SPR61a0Pg6hrk3048AOzSQdtVQ33Az4n/co01+U/J1H5JzRM9JnXCnQRVG28h/ks1Wvwc//KX9EzR41KnpgDn1qAd7cSTwAEV9IGCbUiq6KK/UGuL6/E1FEl/K3psqsJE4Gs1aEs7sQw4pqJ+UJAziP8irS1ux9dPJI0senyqSh/w+Rq0p51YSVoyXg20I/V95/8B0mszkjSS6DGqau4kqK66mPgvz0ixBNgnY7slNV/0OJXDyTR3J8FPZegPZXIY8V+YkWIVcETGdkvqDdFjVS4nkS6tR7evnfg8bspWe33ALcR/WUaK92dst6TeET1W5XQE9V+XZW3xbdKqsqqp44j/kowUP8LqUdL4RI9Xue2POwmqYhOBO4j/ggyP3+MSv5LGL3rM6gZ3ElSlTiL+izE8niK9kSBJ4xU9bnWLOwmqEhNJf2lHfymGx5tyNlpST4oet7ppG9xJUB16I/FfhuFxXtYWS+pV0WNXt80Ebu0g38i4E5hVeY9o3PpIlVj0F2Fo3A9skrPRknpW9PgVwZ0E1ZbDif8CDI9Ds7ZYUi+LHr+iuJOgWnYF8R/+0Phm1tZK6nXRY1ikycA5xPdBO7EYeGn1XaK12Y20XnP0Bz8YC4HNsrZYUq+LHseiuZOgxuW7xH/gQ+OEvM2VVIDocawO+oDPEt8X7cQq0oPpymgm9drx79K8zZVUiOixrE4+RHx/tBOrgXdk6A8N+ADxH/JgrAR2zttcSYWIHs/qxp0E9Qx9pPcvoz/cwfhi3uZKKsgqYsezyfmb2LITqdcV31biS8CE6rukXC8h/kMdjEfxnX9J1YneLW/j/E1sS5N3EvwW7iRYme8R/4EOxrszt1VSWR4jdkzbJn8T23YA8CTx4347cS4wpfIeKcympFctoj/MftJGFtPyNldSYe4ndlybk7+JHdmL9Mp19PjfTlxGWvCoZ+W+13EC9amiPgEsjU5CUk95NPj8OwSffyw3AS8m/QHWNC8FrsTbxm27ivgqrh/4M/UpRCT1jiuJHds+kr2F1dgGdxIsyrOoz+sgb8vcVkll+gGxY9u5+ZtYGXcSLMipxH9o/cBDeO9fUh6fJ35868veyuq4k2CN5HwG4LiMx27Fl/Hev6Q85geff3PgBcE5tOJx4BCauRrrlqTb2i+MTqTutqYel/+fxg1/JOVzBPHjXBNfb55Cun0R3XftxJOkVxy1FicT/yH1A1/J3VBJRduO+HHuxuytzGMicBbx/ddOLAWOqr5LesOFxH9A/cCeuRsqqWh9wBPEj3VN3d/EnQR7zBTgKeI/nJtyN1SSgGuIH+8+m72VebmTYI84mPgPpR94a+6GShLwBeLHuyeBjXI3NDN3EuwB0a/F9AOLgRm5GypJwLHEj3n9wIdzN7QL3Emw4eYR/0F8J3srJSnZivgxr5+0LHEvvPV0FOkhu+j+bCfOIj3cWKQtiP8A+kmv5khSt/yB+HFvcALqBftTj4cr24kLgKnVd0n9HUN85z+G6/5L6q46PAfQD6wB9s3c1m7Zm+buJHgpPb6T4EjOIL7jv569lZL0TIcSP/YNxl3ABnmb2zU7AvcS36ftxA0UtpPgdcR3+tzsrZSkZ5pCWuY2evwbjHPyNrerZpE244nu03biVtImSD1vGrCc2M5eCqybu6GSNIL/S/yEMzT+OW9zu8qdBGvuH4jv6Euyt1KSRjaX+DFwaKwB3pCzwV3mToI19l7iO/ld2VspSSObBDxI/Dg4NFYAh+dsdJdNBy4jvl/biYXAbtV3ST18m/gOfl72VkrS2n2K+HFweKwE/jFno7tsMukZh+h+bSceA/apvkvi3UJsx96fv4mSNKrtSZfeoyea4bGGdJW2VzR5J8FHgd2r75I4k4h/APD72VspSWP7MfGTzNrihzR/z4BBTd5J8GF66Ir1zsR3qPf/JdXBQcSPh6PFn4AXZ2t99zV1J8E/AVtm6I+uO5H4ztw7eyslaXx+TfyYOFqsAb4JbJ6p/d32Tpq5k+CvSK/QN9oniO3Ep0m3ISSpDl5O/OQynngM+CiwcZ5u6KqTSA88Rvdpq/Fd0u2Mxjqb2A68IX8TJWnc+oAbiZ9cxhtPkbZyf0GOzuiipu4k+L4cndEt1xPbeWfmb6IktaRO+wO0Er8hvTGwG83c3/4A4Eni+7GVWAG8KENfdMUCYjvv1PxNlKSW1fmNgPHEQuA84GOkZ732ArYj3TKYXGE/Va2JOwn+mQbeiplO/HuvB+RupCS1YXviX5E2mhPfo4uquLyzDfEPMNwRfH5JGsldwBeik1BjnAAcHZ1EKw4ntmJ6Kn8TJalt02judrZG92MBsAldUMUVgOiFDOYHn1+SRrMUeBtpcJfGsgXwH904URUFwGYVHKMT9wSfX5LGcgXwn9FJqDHeQhc2DaqiAIh+anF+8PklaTxOB26PTkKNMIH07EjW5+uqKAC6cq9iFO4CKKkJlgGvBpZEJ6JGeBHwipwnqKIA2LSCY3RiYfD5JWm85gGvxecBND6fBNbJdfBeuALwaPD5JakVPwTOiE5CjfA80lWjLKooAKL3ll4UfH5JatX7gXOik1AjvJ9MzwJUUQBMreAYnfAWgKSmWUPave7S6ERUezsDc3McuIoCIHob3sXB55ekdqwEjgV+HZ2Iau+9OQ5aRQEwpYJjdGJ58PklqV1PAYcBf4hORLW2Pxm2aq6iAIjeEWpl8PklqROPkIqAO6MTUa29ueoDVvFgwRJg3QqO0651SUttSlKTbQ5cDMyJTkS19Chp6f0VVR2wF24BVNYZkhToYdLW5j4YqJFsTLpSVJkqCoCJFRyjE6uDzy9JVVkMHAWcHZ2IaumEKg9WxS2A6BWtsq6VLEkBJpBWgXsfjnH6qyWkDfgque1dxRUASVK11gCnAYcADwXnovqYTrpNVAkLAEmqr58BewFXRyei2ji8qgNZAEhSvd0HHAh8hvhbropXWQHgMwCS1Bz7A18mw6IwapRnkwrDjngFQJKa4ypgd+BdpFUEVab9qjiIBYAkNcsq4IvATsC5wbkohgWAJBXsPtJmQgcDvwzORd1VyWqRPgMgSb1hP9Krg3NxXOx1S4AZpNdF2+YVAEnqDVcDRwJ7AN/GVVJ72XRgu04PYgEgSb3lVuB1wLbA6cBfYtNRJs/r9AAWAJLUm+4FPg08F3gZ8E3g8ciEVCmvAEiSRrUKuAR4I7AFaXnhM4A7IpNSxzouAHwIUJLKNRPYdyD2AXYGNgjNSON1HvCqTg5gASBJGmoW8HzSMwSzgOcAmwObDMS6wDrA+jHpacBVVLgxULv6g0OSpG57BbFz3287bYDPAEiS1Lp7g8+/cacHsACQJKl1DwSff1qnB7AAkCSpdUuCzz+l0wNYAEiS1Lqng88/udMD+BaAJEntafT85xUASZIKZAEgSVKB1olOQFJXzCBtE3sQsBtpgZcNB/63x4H5wC3AFcCPgae6nqGkxnEhIKm+dgDOIj2xPN7f1BLgTGB2QL5SkxQ//xXfAVINTQM+B6yk/d/WCuAzwNQu5y41RfHzX/EdINXMbOB2qvuNXQds2dUWSM1Q/PxXfAdINbIH8DDV/87uBXbtYjukJih+/iu+A6SamE2eyX9oETCza62R6q/4+a/4DpBqYCrpKf7cv7ebqGANcqlHFD//Fd8BUg18ju795j7apTZJddfo+c+lgKXm2wGYR/fW9VhMut2woEvnk+qq0fOfKwFKzXca3V3Uaz3gw108n6QMvAIgNdsM4EFg3S6fdwnp1UBXDFTJGj3/eQVAara5dH/yB5gOHB5wXkkVsQCQmu2gQs8tqUMWAFKz7RZ4bhcGkhrMAkBqtm0Dz71d4LkldcgCQGq2GYHn3iDw3JI65FsAUrP5+5PiNPr35xUASZIKZAEgSVKBLAAkSSqQBYAkSQWyAJAkqUAWAJIkFcgCQJKkAlkASJJUIAsASZIKZAEgSVKBLAAkSSqQBYAkSQWyAJAkqUAWAJIkFcgCQJKkAlkASJJUIAsASZIKZAEgSVKBLAAkSSqQBYAkSQWyAJAkqUAWAJIkFcgCQJKkAlkASJJUIAsASZIKZAEgSVKBLAAkSSqQBYAkSQWyAJAkqUAWAJIkFcgCQJKkAlkASJJUIAsASZIKZAEgSVKBLAAkSSqQBYAkSQWyAJAkqUAWAJIkFcgCQJKkAlkASJJUIAsASZIKZAEgSVKBLAAkSSqQBYAkSQWyAJAkqUAWAJIkFcgCQJKkAlkASJJUIAsASZIKZAEgSVKBLAAkSSqQBYAkSQWyAJAkqUAWAJIkFcgCQJKkAq0TnYBUkRnAXOAgYDdgFrAhMCkwpxL0RyeQ2UrgcWA+cAtwBfBj4KnAnKTa6A8OlW0H4CxgCfHfRaOMWAKcCcxGpYv+LoYrvgMUYhrwOdJfaNHfQaPMWAF8BpiKShX9HexIX6cHqCKJDlXRBjXLbOB8YOfoRCTgeuAY4MHoRNR1jZ7/LADUNHsAlwKbRSciDXEf6RmU26ITUVc1ev6zAFCTzAauwclf9XQfsDewIDoRdU2j5z9fA1RTTAV+gJO/6mtr4CLS8ylS7VkAqCk+Tnq9T6qzOcDp0UlI4+EtADXBDsA8XLdCzbCYdLvKWwG9r9Hzn1cA1ASn4eSv5lgP+HB0EtJYvAKguptBer1q3ehEpBYsAbbEFQN7XaPnP68AqO7m4uSv5pkOHB6dhDQaCwDV3UHRCUht8rurWrMAUN355L+aatfoBKTRWACo7raNTkBq03bRCUijsQBQ3c2ITkBq0wbRCUijsQCQJKlAFgCquyejE5Da9ER0AtJoLABUd/dEJyC16e7oBKTRWACo7m6JTkBq063RCUijsQBQ3V0RnYDUpsujE5BG41LAqrv1SJuqTI9ORGrBEmAmaWMg9a5Gz39eAVDdLQbOjk5CatF/4+SvmvMKgJpgNmk74EnRiUjjsAJ4Pj4EWIJGz39eAVAT3Al8IToJaZzOwMlfDeAVADXFVOBK4IXRiUijuA44EFgenYi6otHznwWAmmQmcAPw7OhEpBE8AOwD3B+diLqm0fOftwDUJAuAI4D7ohORhrkXOAwnfzWIBYCa5jZgT+AX0YlIA64j/eV/e3QiUissANREC4FDgH8nvW8tRVgBfJJ0z39BcC5SiP7gUNlmAv9Feuc6+rtolBGLga8A26HSRX8XO+JDgOoV6wFzSX+N7Q5sC2wITI5MSo23AnictCnVzaQ3US7GRX6UNHr+swCQms3fnxSn0b8/nwGQJKlAFgCSJBXIAkCSpAJZAEiSVCALAEmSCmQBIElSgSwAJEkqkAWAJEkFsgCQJKlAFgCSJBXIAkCSpAJZAEiSVCALAEmSCmQBIElSgSwAJEkqkAWAJEkFsgCQJKlAFgCSJBXIAkCSpAJZAEiSVCALAEmSCmQBIElSgSwAJEkqkAWAJEkFsgCQJKlAFgCSJBXIAkCSpAJZAEiSVCALAEmSCmQBIElSgSwAJEkqkAWAJEkFsgCQJKlAFgCSJBXIAkCSpAJZAEiSVCALAEmSCmQBIElSgSwAJEkqkAWAJEkFsgCQJKlAFgCSJBXIAkCSpAJZAEiSVCALAEmSCmQBIElSgSwAJEkqkAWAJEkFsgCQJKlAFgCSJBXIAkCSpAJZAEiSVCALAEmSCmQBIElSgSwAJEkqkAWA1GwrAs+9PPDckjpkASA125OB534i8NySOmQBIDXbPYHnvjvw3JI6ZAEgNdstgee+NfDckjpkASA12xWB57488NySOtRXwTH6KzhGJ6pog9RU6wELgOldPu8SYCawuMvnleqk0fOfVwCkZlsMnB1w3v/GyV9qNK8ASM03G5gHTOrS+VYAz8eHAKVGz39eAZCa707gC1083xk4+UsiVUCRIQmmAteR//d2LTClS22S6q74+a/4DpBqYibwF/L91u4Htupaa6T6K37+K74DpBrZFbiX6n9nfwF26WI7pCYofv4rvgOkmtkMuIrqfmPXkq4uSHqm4ue/4jtAqqEpwEdJr+q1+9taDnwC7/lLa1P8/Fd8B0g1NhP4L1orBBYDXwG2C8hXapJGz3+uAyCVYT1gLnAgsDuwLbDhwP/2OGlToZuBK4GLcZEfaTwaPf9ZAEiS1J5Gz38uBCRJUoEsACRJKpAFgCRJBbIAkCSpQBYAkiQVyAJAkqQCWQBIklQgCwBJkgpkASBJUoEsACRJKpAFgCRJBbIAkCSpQBYAkiQVyAJAkqQCWQBIklQgCwBJkgpkASBJUoEsACRJKpAFgCRJBbIAkCSpQBYAkiQVyAJAkqQCWQBIklSgKgqA1RUcoxMTg88vSSpP9NzT8dxbRQGwooJjdGJy8PklSeWZEnz+5Z0ewAJAkqTWWQBUkUSHLAAkSd1mAVBFEh2K/hAkSeWJ/uOz46vvVRQAKys4RifWDz6/JKk8M4LPX4srAMsqOEYnNg0+vySpPJsEn78WBcBjFRyjE9EfgiSpPNF/fD7a6QGqKAAeqeAYnbAAkCR1W/Tc0/HcW0UBsKiCY3QiugqTJJUnugDwCgCwVfD5JUnl2Tr4/As7PUAvXAHYNvj8kqTyRM89Hc+9vVAAzAo+vySpPBYAwIMVHKMTs4LPL0kqSx+wTXAOCzo9QBUFwPwKjtGJ9YDNg3OQJJVjJjAtOIf5nR6gigLgHqC/guN0Yufg80uSyhE95/RTkwJgKRU8jdihXYLPL0kqx67B53+QClbhraIAgHQVIJIFgCSpW6LnnPlVHKSqAmB+RcdpV3Q1JkkqR/ScU8kf3VUVAH+q6Djt2hmYFJyDJKn3TQaeH5zD3VUcpKoC4LcVHadd04A9gnOQJPW+vYCpwTncXsVBqioAKkmmQ38fnYAkqeftG50ANSsAfk8FexN3qA4fiiSpt0XPNcuAO6s4UFUFwCrgdxUdq137BZ9fktTb+oAXBecwD1hdxYGqKgAAbqvwWO2YCewYnIMkqXftQvzKs5XNtVUWAHV4DuBl0QlIknrWodEJUOFD91UWAL+q8FjtOiw6AUlSz6rDH5nXRycwkimkhxP6A2MZMD13QyVJxZlOPea4yl5BrPIKwHLg5gqP144pwIHBOUiSes9LSXNMpJuoYA+AQVUWAADXVHy8dhwbnYAkqeccF50AcHV0AqM5mtjLI/3AE8Sv0iRJ6h1TSXNL9Px2ZO6GdmJz4juoHzgqd0MlScV4JfHz2hpgsyobVfUtgIdJixREOz46AUlSz6jDnHIbsLDKA1ZdAAD8JMMxW3U0sGF0EpKkxtsAmBudBBnm1hwFwCUZjtmqacAJ0UlIkhrvtcC60UlQj7l1TJOBJ4m/X3JL7oZKknrezcTPZ08Ak3I3tCoXEN9h/cCeuRsqSepZexM/j/UD5+VoXI5bAFCP5wAA/ik6AUlSY70tOoEBjbj8P2gr0naF0VXTUuJ3bpIkNc8WpDkkeh5bDWyZo4G5rgDcD1yb6ditmAq8PToJSVLjnEI9FpW7CngwOolWnUJ85dQPLMINgiRJ47cu8Ajx81c/Db2VvSX1uA3QD5ycua2SpN5Rlz9gV9Lg29hXEt+B/cCfid/FSZJUf1OBe4mft/qBy3I2NNczAIPOyXz88XoO8JboJCRJtXcysHV0EgO+H51AJzahHk9R9gMPUI/VnCRJ9TQdWED8fNUPPA1slLOxua8ALAJ+mPkc47UlPgsgSVq7d5Je/6uDc4HHopPo1EHEV1KD8Siwad7mSpIaaHPgceLnqcF4cd7mdkcf8EfiO3Mwvpy3uZKkBvoq8fPTYPyBNHf2hNOJ79DBWAXskre5kqQG2Z00N0TPT4Px3rzN7a4tgBXEd+pg/DRvcyVJDdFHfV5Z7weW0+B3/9fm28R37NA4MW9zJUkN8Abi56Oh8Y2srQ2yK7CG+M4djEfowSpLkjRumwIPEz8fDY3dsrY40E+J79yh8a28zZUk1dj3iJ+HhsZP8jY31mHEd/DwOCxriyVJdXQE8fPP8Dg4a4tr4BbiO3loLKA+Cz9IkvLbjLTFbvT8MzRupYde/Vub1xPf0cPjR1lbLEmqiz7gIuLnneFRxIPpE4HfE9/Zw+OtORstSaqFdxI/3wyPP5DmxiKcSHyHD4/FwE45Gy1JCrUraZOd6PlmeByXs9F1M4F0vyO604fHH4ENMrZbkhRjQ+BO4ueZ4XE7+Tfmq51XEd/xI8UFFPAghiQVpA84n/j5ZaR4ecZ211YfcDPxnT9SfCBjuyVJ3fVh4ueVkeImCv6D8xDiP4CRYhVwVMZ2S5K64xhgNfHzykhxUMZ2N8KFxH8II8XTwAsztluSlNdepAe8o+eTkeK8jO1ujOcCy4j/MEaKhwfykyQ1y7akhd6i55GRYjkwO1/Tm+WzxH8ga4t5wMb5mi5JqtimwO+Inz/WFp/M1/TmWZ/6Lcs4NG4mvUIiSaq3GcCNxM8ba4sFAzlAoMUXAAAHvUlEQVRqiDcS/8GMFr8A1s3WeklSp6YDVxM/X4wWr83W+gbrAy4n/sMZLS4FpuTqAElS26ZSvy3nh8dlFPza31i2pb5PbA7GlcB6uTpAktSydUl/oEXPD6PFEnyofEynEf9BjRW/xHs4klQHG1D/y/79wHtydUAvWYe0OlL0hzVW3ER60lSSFGMj4Hri54Ox4mbS3KZx2BNYQfyHNlbMA2bl6QJJ0ii2o96v+g3GcmC3TH3Qsz5I/Ac3nlgI7JupDyRJf2sf6rvIz/B4f6Y+6GkTgCuI//DGE0spbD9nSQpyNOmBuuhxfzxxFTAxTzf0vucAjxH/IY4nVgP/gq94SFIOfcC/Ut+NfYbHImDrLD1RkFcR/0G2EhfhqoGSVKUZwPnEj++txPFZeqJA3yT+w2wl/gC8IEdHSFJhdgXuJH5cbyW+mqUnCjUV+DXxH2or8TRwKt4SkKR2vY76Lw43PG4GpuXojJJtAzxC/IfbalwCzMzQH5LUqzYDLiR+/G41FpFWtFUGh9OcB0CGxoMDuUuSRncEzXnFb2isAg7J0B8a4t+I/6DbjXOAzavvEklqvM2BbxE/TrcbH6i+SzTcBOBHxH/Y7cZC3A5Skgb1Aa8nXT6PHp/bjXPxea+umQ7cSPyH3klcTnq6VZJKtTtph9Xo8biTuB4f+uu6zYC7iP/wO4nVpEte3haQVJJNgC+S7ptHj8OdxN3AFhX3jcZpJ5qzUuBo8RjwPtKe1pLUq6aTtnx/nPhxt9NYBOxYbfeoVfsDy4j/MlQRD5N+HF5OktRLJgNvBR4gfpytIlYAL6m0h9S242n+paShcS/wDrwiIKnZpgOnAPcRP65WFatIS9SrRl5PM9cIGC0eJ90n26rCfpKk3DYHPkIzF28bLdYAb66um1SlU4j/guSIpcDXgL2q6ypJqtzewJn0zm3Z4fH26rpKObyb+C9JzphHek5gk6o6TJI6sAHp/v5viB8fc8bpVXWY8voo8V+W3LEE+B5wNGmzJEnqlmnAMcD3SRufRY+HueMjlfSauuZ04r803YongG8DrwDWq6LzJGmY9Uh/cHwXeJL4ca9bcVoVnafu+yd678HAsWIlcDXpSzsHl6eU1L7tSNua/5Teva+/tlgz0HY12GtIk2L0lykqHgL+h3QJ62C8XSBpZOuQ/mg4lbRx2UPEj19RsQp4Y2fdWX+l/HV4PGnJ3cnRidTAMtKDhLcBvx34dx5py2JJZXgWaSXVXYFdBv7dCf9AAFgOnETa4KenlVIAABwInA9sGJ1ITS0F7gHmD/z7AGllwkVDYinp3t9q0lWVxRGJSnqG9YBJwERgBmkBsY1JbwptSto3ZStgFrDtQDjRj+xR0sONV0Un0g0lFQAA2wMXA7OjE5Ek1co9wOHA76MT6ZYJ0Ql02V3A3wPXRCciSaqNG4AXUdDkD+UVAJCWpjwE+GF0IpKkcOcBB5AeeizKxOgEgqwkPeW6lPRsQImFkCSVrB/4DOl18ZXBuYQo7RmAkRwInE3avEKS1PsWkV4RvzQ6kUgWAMmzSZeB9o5ORJKU1c3AK0kP/RXNS9/JvcD+wDeiE5EkZfM10oPgxU/+4BWAkbwS+CrpPVpJUvM9AZxM2kBNAywARvZs0sqBBwTnIUnqzJXA64D7ohOpm1LfAhjLk6QC4DHgIOwnSWqaVcDHgDeRrgBoGK8AjG0P4Exgz+hEJEnjchPwZuDW6ETqzIcAx3Yz8HfAu3Dte0mqs6XA6cALcfIfk1cAWrMd8H+Al0YnIkl6hquAtwJ/jE6kKbwC0Jq7gUOBf6TAZSMlqYYWAK8nPbTt5N8CH25rzy3A/yYtH/lCYJ3YdCSpOCuBL5O2770hOJdG8hZA57YHPgEcG52IJBXiItJzWX+KTqTJvAXQubuA40jPBfwmOBdJ6mU3AS8BjsTJv2MWANX5GTCHVAjcHJyLJPWSeaQ/tPYBrgjOpWd4CyCPPuAI4OPArsG5SFJT/Q74FPBdYHVwLj3HAiCvicCrgfdhISBJ43UL8FnSVu1rgnPpWRYA3bMfcBowF/tdkkZyDfBp0kN+/cG59Dwnou7bFXgPcAIwOTgXSYq2grRL3+eB24NzKYoFQJyNSK8OngLsHJyLJHXbH4GvA98AHg7OpUgWAPUwh7SE5WuA6cG5SFIuy4ELga8Cl+Nl/lAWAPUyeFXgONKylq7UKKnpVgNXAt8HzgUej01HgywA6msT0gODxwKH4XLDkppjDXAd8APSxL8gNh2NxAKgGZ5FWlfgMOBgYP3YdCTpbzxJWhDtEtJT/A/GpqOxWAA0zyTSK4WHDYTrC0iKcitpwr+E9Arfyth01AoLgOabQVoecz9g34F/p4ZmJKkXrSJN+NcAVwM/BxZGJqTOWAD0nqnAXqSiYFdgF+AFwJTIpCQ1yjLgDuA20rv5vyJtxLM8MilVywKgDOsAs0kFwc7AdsAsYFtgy7i0JAV7ELhnIO4Gfkua8O8k/cWvHmYBoKn8tRiYSXr7YNOB2GQgNgLWHfjvZ5BeT5wErNflXCX9rcWke++rSQ/iATwNPAYsGoiFA/8+Qnoif/5ALOtuqpIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSfX2/wB8iJ9mcnhIeAAAAABJRU5ErkJggg==" />
                                        </defs>
                                    </svg>
                                </span>
                                <h6 class="gradient-text">Post requirement</h6>
                            </div>
                            <p class="des-title">Post a class and other educator requirements</p>
                        </a>
                    </div>

                </div>
                {{--
                <div class="col-md-4 border" style="border:none">
                    <div data-bs-toggle="tooltip" data-bs-placemen="top" title="Browse through the list of available educators" class="card d-flex flex-column h-100 bg-transparent" style="border:none">
                        <a class="card card-custom shadow-sm explore-educators-btn" href="{{ route('new-school.discover') }}">
                <div class="d-flex card-title">
                    <span><svg width="31" height="32" viewBox="0 0 31 32" fill="none"
                            xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <rect y="0.5" width="31" height="31" fill="url(#pattern0_10168_2186)" />
                            <defs>
                                <pattern id="pattern0_10168_2186" patternContentUnits="objectBoundingBox"
                                    width="1" height="1">
                                    <use xlink:href="#image0_10168_2186" transform="scale(0.00195312)" />
                                </pattern>
                                <image id="image0_10168_2186" width="512" height="512"
                                    preserveAspectRatio="none"
                                    xlink:href="data:image/png;base64,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" />
                            </defs>
                        </svg>
                    </span>
                    <h6 class="gradient-text">Explore educators</h6>
                </div>
                <p class="des-title">Browse vetted Educators matching your needs.</p>
                </a>
            </div>


        </div>
        --}}


        <div class="col-md-4 cursor-pointer" style="border:none" id="calculate_budget_div">
            <div class=" card   card-custom  d-flex flex-column h-100 ">
                <div class="card-title">
                    <h4 class="heading_text">Calculate budget for your requirement</h4>
                    <div class="d-flex">
                        <p class="new_card_text">Use budget calculator to find the best talent available on the platform.</p>
                        <img src="{{ asset('/new_images_school/calculate_budget.png') }}" style="max-width: 100%; height: 60px;" />

                    </div>
                </div>
            </div>

            <!-- //calculate budget modal -->
            <div class="modal fade" id="budgetcalculatormodal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" style="max-width:1106px!important;" role="document">
                    <div class="modal-content p-3">
                        <div class="modal-header" style="border-bottom:none">
                            <div class="d-flex align-items-center">
                                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.39906 9.49321L8.29906 14.3932C8.49906 14.5932 8.59506 14.8265 8.58706 15.0932C8.57906 15.3599 8.47473 15.5932 8.27406 15.7932C8.07406 15.9765 7.84073 16.0725 7.57406 16.0812C7.30739 16.0899 7.07406 15.9939 6.87406 15.7932L0.274061 9.19321C0.174061 9.09321 0.103061 8.98488 0.0610614 8.86821C0.0190614 8.75155 -0.00127179 8.62655 6.15388e-05 8.49321C0.00139487 8.35988 0.0223946 8.23488 0.0630613 8.11821C0.103728 8.00155 0.174395 7.89321 0.275062 7.79321L6.87506 1.19321C7.05839 1.00988 7.28773 0.918213 7.56306 0.918213C7.8384 0.918213 8.07573 1.00988 8.27506 1.19321C8.47506 1.39321 8.57506 1.63088 8.57506 1.90621C8.57506 2.18155 8.47506 2.41888 8.27506 2.61821L3.39906 7.49321H14.5741C14.8574 7.49321 15.0951 7.58921 15.2871 7.78121C15.4791 7.97321 15.5747 8.21055 15.5741 8.49321C15.5734 8.77588 15.4774 9.01355 15.2861 9.20621C15.0947 9.39888 14.8574 9.49455 14.5741 9.49321H3.39906Z" fill="#004CBD" />
                                </svg>

                                <p class="ms-5  new_card_text heading_text bold mb-0">Calculate budget for a class</p>

                            </div>


                            <button type="button" class="close_budgetcontent_modal" data-dismiss="modal" aria-label="Close" style="background: none; border: none; font-size: 1.5rem; line-height: 1; color: #000;">
                                <span aria-hidden="true" style="font-size:2rem;color:#787777">&times;</span>
                            </button>

                        </div>
                        <div class="container">
                            <p style="font-size:14px;width:85%">Enter required details to calculate the budget. You can change the values to adjust as per desired budget.
                                You can save budget as well and use it later for posting a class.</p>
                        </div>
                        <div class="container">
                            <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:#E9E9E9;">
                        </div>

                        <div class="modal-body">
                            <div class="row d-flex justify-content-between">

                                <div class="col-md-6">
                                    <!-- <div class="search-container my-4 column-gap d-flex align-items-center" style="border:1px solid black">
                                        <div class="search-box">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.5 13C4.68333 13 3.146 12.3707 1.888 11.112C0.63 9.85333 0.000667196 8.316 5.29101e-07 6.5C-0.000666138 4.684 0.628667 3.14667 1.888 1.888C3.14733 0.629333 4.68467 0 6.5 0C8.31533 0 9.853 0.629333 11.113 1.888C12.373 3.14667 13.002 4.684 13 6.5C13 7.23333 12.8833 7.925 12.65 8.575C12.4167 9.225 12.1 9.8 11.7 10.3L17.3 15.9C17.4833 16.0833 17.575 16.3167 17.575 16.6C17.575 16.8833 17.4833 17.1167 17.3 17.3C17.1167 17.4833 16.8833 17.575 16.6 17.575C16.3167 17.575 16.0833 17.4833 15.9 17.3L10.3 11.7C9.8 12.1 9.225 12.4167 8.575 12.65C7.925 12.8833 7.23333 13 6.5 13ZM6.5 11C7.75 11 8.81267 10.5627 9.688 9.688C10.5633 8.81333 11.0007 7.75067 11 6.5C10.9993 5.24933 10.562 4.187 9.688 3.313C8.814 2.439 7.75133 2.00133 6.5 2C5.24867 1.99867 4.18633 2.43633 3.313 3.313C2.43967 4.18967 2.002 5.252 2 6.5C1.998 7.748 2.43567 8.81067 3.313 9.688C4.19033 10.5653 5.25267 11.0027 6.5 11Z" fill="black" />
                                            </svg>

                                            <input type="text" id="searcsubject" class="search" placeholder="Search Subject" name="search" style="border:none;padding-left:1rem">
                                        </div>



                                    </div> -->

                                    <!-- //credentialed_or_non_credentialed -->
                                    <div class="row d-flex align-items-center">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Requirement Type<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black;padding:3% 6%">
                                                    <option value="" disabled selected hidden class="placeholder-option">Class</option>

                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                                    </svg>
                                                </div>
                                            </div>


                                        </div>
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Subject<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px">
                                                <!-- <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black;padding:2% 6%">
                                                    <option value="" disabled selected hidden class="placeholder-option">Search Subject</option>
                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select> -->
                                                <select class="form-select sub_subject select22 new_budget" id="sub_subject" name="sub_subject" style="outline:none;">
                                                    <option value="" selected hidden>Select</option>
                                                    @foreach ($subjectArea as $subjects)
                                                    @if (!empty($subjects->subject_area) && $subjects->subjects->isNotEmpty())
                                                    <optgroup label="{{ $subjects->subject_area }}">
                                                        @foreach ($subjects->subjects as $subject)
                                                        @if (!empty($subject->title))
                                                        @php
                                                        $modifiedCode = strlen($subject->subject_code) == 4 ? '0' . $subject->subject_code : $subject->subject_code;
                                                        @endphp
                                                        <option value="{{ $modifiedCode }}">{{ $subject->title }}</option>
                                                        @endif
                                                        @endforeach
                                                    </optgroup>
                                                    @endif
                                                    @endforeach
                                                </select>


                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                                    </svg>
                                                </div>
                                            </div>


                                        </div>
                                        <div>

                                        </div>
                                        <div>

                                        </div>
                                        <div class=" col-md-4">
                                            <label class="form-label platform_school_label m-0 d-inline-block">Educator Profile<span class="text-danger">*</span></label>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="col d-inline-flex align-items-center">

                                                <div class="form-check-inline d-flex gap-2">
                                                    <input class="form-check-input custom-input credentialed" checked type="radio" name="credential_type" id="classType" value="credentialed" style="height:18px!important   ">
                                                    <label class="form-check-label custom-lab" for="classType" style="color:black!important">Credentialed</label>

                                                </div>

                                                <div class="form-check-inline d-flex align-items-center gap-3">
                                                    <input class="form-check-input custom-input non_credentialed" type="radio" name="credential_type" id="nonTeachingType" value="non_credentialed" style="height:18px">
                                                    <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Non-credentialed</label>
                                                </div>
                                            </div>
                                        </div>

                                    </div>


                                    <!-- special education required -->
                                    <div class="row d-flex align-items-center my-3">
                                        <div class=" col-md-4">
                                            <label class="form-label platform_school_label m-0 d-inline-block">Special education certification required*<span class="text-danger">*</span></label>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="col d-inline-flex align-items-center">

                                                <div class="form-check-inline d-flex gap-2">
                                                    <input class="form-check-input custom-input credentialed" checked="" type="radio" name="special_education" id="classType" value="credentialed" style="height:18px!important   ">
                                                    <label class="form-check-label custom-lab" for="classType" style="color:black!important">No</label>

                                                </div>

                                                <div class="form-check-inline d-flex align-items-center gap-3">
                                                    <input class="form-check-input custom-input non_credentialed" type="radio" name="special_education" id="nonTeachingType" value="non_credentialed" style="height:18px">
                                                    <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Yes</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- //delivery mode -->
                                    <div class="row d-flex align-items-center my-3">
                                        <div class=" col-md-4">
                                            <label class="form-label platform_school_label m-0 d-inline-block">Delivery Mode<span class="text-danger">*</span></label>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="col d-inline-flex align-items-center">

                                                <div class="form-check-inline d-flex gap-2">
                                                    <input class="form-check-input custom-input credentialed" checked="" type="radio" name="delivery_mode_budget" id="classType" value="credentialed" style="height:18px!important   ">
                                                    <label class="form-check-label custom-lab" for="classType" style="color:black!important">Online</label>

                                                </div>

                                                <div class="form-check-inline d-flex align-items-center gap-3">
                                                    <input class="form-check-input custom-input non_credentialed" type="radio" name="delivery_mode_budget" id="nonTeachingType" value="non_credentialed" style="height:18px">
                                                    <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Hybrid</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                </div>
                                <div class="col-md-5">
                                    <div class="col-lg-12">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Years of Experience<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper ">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black">
                                                    <option value="" disabled selected hidden class="placeholder-option">0-3 years</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                                    </svg>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="col-lg-12">

                                            <div class="row d-flex justify-content-between my-4">
                                                <div class="col-lg-6 d-flex align-items-center">
                                                    <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Number of instructional days<span class="text-danger">*</span></label>
                                                </div>

                                                <div class="col-lg-4 position-relative">
                                                    <input
                                                        placeholder="Enter"
                                                        class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget"
                                                        style="border-radius:48px; border:1p solid #000000; color:#000000;" />
                                                    <div class=" input_absolute  position-absolute">
                                                        days
                                                    </div>


                                                </div>

                                            </div>


                                        </div>


                                    </div>

                                    <div class="col-lg-12">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Class duration (in hours)<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper ">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black">
                                                    <option value="" disabled selected hidden class="placeholder-option">Select</option>
                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                                    </svg>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="col-lg-12">

                                            <div class="row d-flex justify-content-between my-4">
                                                <div class="col-lg-6 d-flex align-items-center">
                                                    <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Number of non-instructional hours<span class="text-danger">*</span></label>
                                                </div>

                                                <div class="col-lg-4 position-relative">
                                                    <input
                                                        placeholder="Enter"
                                                        class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget"
                                                        style="border-radius:48px; border:1p solid #000000; color:#000000;" />
                                                    <div class=" input_absolute  position-absolute">
                                                        hours
                                                    </div>


                                                </div>

                                            </div>


                                        </div>


                                    </div>
                                    <div class="col-lg-12">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Language of Instruction<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper ">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black">
                                                    <option value="" disabled selected hidden class="placeholder-option">English</option>
                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                                    </svg>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="col-lg-12">

                                            <div class="row d-flex justify-content-between">
                                                <div class="col-lg-6 d-flex align-items-center">
                                                    <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Expected class size<span class="text-danger">*</span></label>

                                                </div>

                                                <div class="col-lg-4 position-relative">
                                                    <input
                                                        placeholder="Enter"
                                                        class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget"
                                                        style="border-radius:48px; border:1p solid #000000; color:#000000;" />
                                                    <div class=" input_absolute_students  position-absolute">
                                                        Students
                                                    </div>



                                                </div>

                                            </div>


                                        </div>
                                        <p style="font-size:12px">(Max: 30 students)</p>


                                    </div>
                                </div>

                            </div>
                            <div class="d-flex align-items-center mt-5">
                                <input type="checkbox" class="error_calculate_budget" style="accent-color: #004CBD;">
                                <p class="ms-2" style="color:black;font-size:12px;font-weight:500">Educator will use my school-provided curriculum and teaching materials on the school’s LMS</p>

                            </div>

                            <hr class="mt-5" style="height:2px; width:100%; border:none; background-color:E9E9E9;">
                            <div class="d-flex justify-content-end amount_budget_management my-5">
                                <div class="d-flex gap-2 ">
                                    <div class="border border-success px-3 py-2" style="background-color:#004CBD;color:white;border-radius:33px;">
                                        Calculate Budget
                                    </div>
                                    <div class="px-3 py-2" style="border:2px solid #004CBD;border-radius:33px;color:004CBD">
                                        $4500- $5500
                                    </div>


                                </div>

                            </div>
                            <hr class="mt-5" style="height:2px; width:100%; border:none; background-color:#E9E9E9;">

                        </div>
                        <div class="modal-footer py-3 mx-4" style="border-top: 0px; display: flex; justify-content: space-between; width: 95%;background-color:#F2F6FB;border-radius:12px">
                            <div>
                                <p style="color:black;font-weight:bold">Looks good? Let’s post this class.</p>
                                <p>You can edit before posting</p>
                            </div>
                            <div>
                                <button type="button" id="post_requirement_submit_budget" class="btn px-2 py-3" data-dismiss="modal" style="border-radius:21px;border:1px solid black;padding:9px 15px!important;background-color:#004CBD;color:white;font-weight:700">Post Requirement</button>
                                <button class="py-2 px-3"
                                    type="button"
                                    style="border-radius: 21px; padding: 6px 15px !important; border:1px solid black;color:black">
                                    Save Budget
                                </button>

                            </div>

                        </div>

                    </div>
                </div>
            </div>


        </div>
        <div class="col-md-4 view-reports-btn cursor-pointer" style="border:none">
            <div data-bs-toggle="tooltip" data-bs-placement="top" title="Get insights into spending and hiring performance" class=" card   card-custom  d-flex flex-column h-100 ">
                <div class="d-flex card-title">
                    <span><svg width="35" height="36" viewBox="0 0 35 36" fill="none"
                            xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <rect y="0.5" width="35" height="35" fill="url(#pattern0_10168_2195)" />
                            <defs>
                                <pattern id="pattern0_10168_2195" patternContentUnits="objectBoundingBox"
                                    width="1" height="1">
                                    <use xlink:href="#image0_10168_2195" transform="scale(0.00195312)" />
                                </pattern>
                                <image id="image0_10168_2195" width="512" height="512"
                                    preserveAspectRatio="none"
                                    xlink:href="data:image/png;base64,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************************************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" />
                            </defs>
                        </svg>
                    </span>
                    <h6 class="gradient-text">View reports</h6>
                </div>
                <p class="des-title">Get insights on financials and class quality.</p>
            </div>
        </div>
    </div>

    <hr class="mt-5 custom-hr">

    <div class="discover-section">
        <div class="discover-header mb-5">
            <div>
                <h5 class="title discover-title">Discover</h5>
                <p class="subtitle">Explore educators to fill open positions</p>
            </div>
            <a href="{{ route('new-school.discover') }}" class="show-more-btn">Show more talent <span style="margin-left: 8px;"><svg width="17"
                        height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M12.8859 9.50675H1.71094C1.42761 9.50675 1.19027 9.41075 0.998941 9.21875C0.807608 9.02675 0.711608 8.78942 0.710941 8.50675C0.710274 8.22408 0.806274 7.98675 0.998941 7.79475C1.19161 7.60275 1.42894 7.50675 1.71094 7.50675H12.8859L7.98594 2.60675C7.78594 2.40675 7.68994 2.17342 7.69794 1.90675C7.70594 1.64008 7.81027 1.40675 8.01094 1.20675C8.21094 1.02342 8.44427 0.927418 8.71094 0.918751C8.97761 0.910084 9.21094 1.00608 9.41094 1.20675L16.0109 7.80675C16.1109 7.90675 16.1819 8.01508 16.2239 8.13175C16.2659 8.24842 16.2866 8.37342 16.2859 8.50675C16.2853 8.64008 16.2646 8.76508 16.2239 8.88175C16.1833 8.99842 16.1123 9.10675 16.0109 9.20675L9.41094 15.8068C9.22761 15.9901 8.99861 16.0818 8.72394 16.0818C8.44927 16.0818 8.21161 15.9901 8.01094 15.8068C7.81094 15.6068 7.71094 15.3694 7.71094 15.0948C7.71094 14.8201 7.81094 14.5824 8.01094 14.3818L12.8859 9.50675Z"
                            fill="#004CBD" />
                    </svg>
                </span>
            </a>
        </div>

        <div class="row g-3 mb-5">
            <!-- Card 1 -->
            @foreach ($users as $user)
            @php
            $requirementId = !empty($requirements[0]) ? encrypt_str($requirements[0]->id) : null;
            @endphp
            <div style="cursor:pointer" class="col-md-4 col-sm-6 ">
                <div class="d-flex flex-column h-100 card discover_card   bg-transparent h-100" style="border:none">
                    <div class="educator-card h-100" onclick="handleCardClick(event, '{{ encrypt_str($user->id) }}', '{{ $requirementId }}')">
                        <div class="card-header card-header_dasboard  d-flex justify-content-between align-items-start">
                            <img src="{{ generateSignedUrl($user->step5->profile_image) }}" alt="" class="profile-pic" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $user->first_name.' '.$user->last_name }}">
                            <div>
                                <h4 class="discover-name">{{ $user->first_name.' '.$user->last_name }}</h4>
                                <p class="location">{{ !empty($user->city) ? $user->city.',' : '' }} {{ $user->state }}</p>
                            </div>
                            <!-- this is the 3 dot -->
                            <div class="d-flex justify-content-start gap-2 applicant-item" data-applicant-id="{{ encrypt_str($user->id) }}" data-requirement-id="{{ $requirementId }}">
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="optionsMenu">

                                </ul>
                                <!-- message button -->
                            </div>
                            <!-- end of the 3 dot -->
                            <g clip-path="url(#clip0_11259_4346)">
                                <path d="M10.5 10.5H10.51V10.51H10.5V10.5ZM10.5 6H10.51V6.01H10.5V6ZM10.5 15H10.51V15.01H10.5V15Z" stroke="black" stroke-width="3" stroke-linejoin="round" />
                            </g>
                            <defs>
                                <clipPath id="clip0_11259_4346">
                                    <rect width="20" height="20" fill="white" transform="translate(0.5 0.5)" />
                                </clipPath>
                            </defs>
                            </svg>

                        </div>
                        @if (!empty($user->step3->format))
                        <div class="button-container">
                            <div>
                                <p class="subjects">
                                    @if (!empty($user->step3) && !empty($user->step3->subjects))
                                    @php
                                    $subjectList = collect($user->step3->subjects)->map(function ($subject) {
                                    $mainSubject = !empty($subject->subject)
                                    ? '<span style="font-weight: 600;">' . v1SubjectName($subject->subject) . '</span>'
                                    : '';
                                    $subSubject = !empty($subject->sub_subject)
                                    ? ' <span style="font-weight:600">:</span> ' . v1SubSubjectname($subject->sub_subject)
                                    : '';
                                    return $mainSubject . $subSubject;
                                    });
                                    @endphp
                                    {!! $subjectList->take(3)->join(', ') !!}@if($subjectList->count() > 3)...@endif
                                    @endif
                                </p>


                                <p class="grades">
                                    <i><span style="font-weight:600">Grade Level(s):</span>
                                        @if (!empty($user->step3) && !empty($user->step3->i_prefer_to_teach))
                                        {{ rtrim(gradeLevel($user->step3->i_prefer_to_teach), ',') }}
                                        @endif
                                    </i>
                                </p>

                                <p class="certified"><span style="font-weight:600">Certified in (States):</span>
                                    @if (!empty($user->step2) && !empty($user->step2->education) && !empty($user->step2->education))
                                    @foreach ($user->step2->education as $education)
                                    {{ !empty($education->states) ? implode(', ', array_map(fn($state) => str_replace('_', ' ', $state), json_decode($education->states))) : '' }}
                                    @endforeach
                                    @endif
                                </p>

                                <p class="certified"><span style="font-weight:600">Additional Certificate:</span>
                                    @if ($user->is_background_check)
                                    <svg width="24" height="24" viewBox="0 0 24 24" style="margin-left: 6px; vertical-align: middle;">
                                        <circle cx="12" cy="12" r="10" fill="#28a745" />
                                        <path d="M7.5 12.5L10.5 15.5L16.5 9.5" stroke="white" stroke-width="2" stroke-linecap="round" fill="none" />
                                    </svg>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="format-container">
                            @php
                            $formats = explode(',', $user->step3->format);
                            $comp = !empty($user->step3->compensation) ? explode(',', $user->step3->compensation) : [];
                            @endphp

                            @if (in_array('hybrid', $formats) && count($comp) > 1)
                            <button class="price-button">
                                {{ ucfirst($formats[0]) }}: ${{ $comp[0] }}/hr <span style="font-weight:bold; color:#004CBD;">|</span> {{ ucfirst($formats[1]) }}: ${{ $comp[1] }}/hr
                            </button>
                            @else
                            <button class="price-button">
                                {{ ucfirst($user->step3->format) }}: ${{ $user->step3->compensation }}/hour
                            </button>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="bottom-line"></div>
    </div>

    <div class="mt-5 latest-activity">
        <div class="latest-header mb-5">
            <h5 class="title">Latest Class Activity</h5>
            <p class="subtitle">Stay updated with new classes created by schools near you.</p>
        </div>

        <div class="class-carousel">
            <button class="carousel-btn left-btn">
                <svg width="21" height="18" viewBox="0 0 21 18"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M5.48316 9.33856L10.0091 13.0754C10.2649 13.2857 10.6807 13.2857 10.9366 13.0754C11.193 12.865 11.193 12.5233 10.9366 12.313L6.87242 8.95735L10.9366 5.60173C11.193 5.39137 11.193 5.04967 10.9366 4.83931C10.6801 4.62895 10.2649 4.62895 10.0091 4.83931L5.48382 8.57613C5.23654 8.77848 5.23588 9.13567 5.48316 9.33856Z"
                        fill="white" />
                    <path
                        d="M10.073 9.33856L14.5983 13.0754C14.8547 13.2857 15.2699 13.2857 15.5257 13.0754C15.7822 12.865 15.7822 12.5233 15.5257 12.313L11.4623 8.95735L15.5264 5.60173C15.7829 5.39137 15.7829 5.04967 15.5264 4.83931C15.2699 4.62895 14.8547 4.62895 14.5989 4.83931L10.0737 8.57613C9.82638 8.77848 9.82572 9.13567 10.073 9.33856Z"
                        fill="white" />
                </svg>
            </button>

            <div class="class-container d-inline-flex justify-content-start">
                @foreach ($postrequirements as $requirement)
                <div class="class-card">
                    <h6 class="class-title">{{ $requirement->requirement_name }} {{ !empty($requirement->parent_id) ? '(Copied)' : '' }}</h6>
                    <p class="class-location">{{ !empty($requirement->city) ? $requirement->city.',' : (!empty(auth()->user()->city) ? auth()->user()->city : '')}} {{ !empty($requirement->state) ? $requirement->state : (!empty(auth()->user()->state) ? auth()->user()->state : '')}}</p>
                    <p class="class-time"><i>{{ !empty($requirement->created_at) ? $requirement->created_at->diffForHumans() : '' }}</i></p>
                </div>
                @endforeach
            </div>

            <button class="carousel-btn right-btn">
                <svg width="21" height="18" viewBox="0 0 21 18"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M15.5168 8.66144L10.9909 4.92462C10.7351 4.71426 10.3193 4.71426 10.0634 4.92462C9.80698 5.13498 9.80698 5.47668 10.0634 5.68704L14.1276 9.04265L10.0634 12.3983C9.80698 12.6086 9.80698 12.9503 10.0634 13.1607C10.3199 13.3711 10.7351 13.3711 10.9909 13.1607L15.5162 9.42387C15.7635 9.22152 15.7641 8.86433 15.5168 8.66144Z"
                        fill="white" />
                    <path
                        d="M10.927 8.66144L6.40174 4.92462C6.14527 4.71426 5.73007 4.71426 5.47426 4.92462C5.21779 5.13498 5.21779 5.47668 5.47426 5.68704L9.53773 9.04265L5.4736 12.3983C5.21713 12.6086 5.21713 12.9503 5.4736 13.1607C5.73007 13.3711 6.14527 13.3711 6.40108 13.1607L10.9263 9.42387C11.1736 9.22152 11.1743 8.86433 10.927 8.66144Z"
                        fill="white" />
                </svg>
            </button>
        </div>
    </div>
</div>

<!-- Separator (Visible on Medium and Larger Screens) -->
<div class="col-md-1 d-none d-md-block text-center order-3 order-md-2 border-col">
    <div style="border-left: 1px solid #eeeeee; height: 100%;"></div>
</div>

<div class="col-md-3 order-1 order-md-2 col-wid">

    <div class="tour-card shadow-sm p-3 position-relative">
        <!-- Close Button -->
        <button type="button" class="btn-close position-absolute top-0 end-0 m-2"
            aria-label="Close"></button>

        <h5 class="tour-title mb-4" data-bs-toggle="tooltip" data-bs-placement="top" title="Learn how to use the app efficiently" style="width:max-content">Take a tour!</h5>
        <p class="tour-description">A quick tour to help you hire educators effortlessly.</p>

        <ul class="tour-list mt-3">
            <li class="tour-item">
                Dashboard overview
                <button class="tour-btn" onclick="startTour()">Start</button>
            </li>
            <li class="tour-item">
                Posting a requirement
                <button class="tour-btn post_requirements_tour" onclick="startTour('post_requirements_dashboard')">Start</button>
            </li>
            <li class="tour-item">
                Discovering educators
                <button class="tour-btn" onclick="startTour('discover_educators')">Start</button>
            </li>
            <li class="tour-item">
                Hiring educators
                <button class="tour-btn" onclick="startTour('hire_educators')">Start</button>
            </li>
            <li class="tour-item">
                Managing contracts
                <button class="tour-btn" onclick="startTour('managing_conracts')">Start</button>
            </li>
            <li class="tour-item">
                Insights
                <button class="tour-btn" onclick="startTour('insights')">Start</button>
            </li>
        </ul>

        <div class="start-hire-btn" style="text-align:center;"><button class="tour-main-btn">Start Hiring Now!</button></div>
    </div>
    <div class="overall-reports">
        <h5 class="reports-section-title mt-5 mb-5 text-primary fw-bold">Overall Reports</h5>
        <div class="row g-3 mb-5">
            <div class="col-md-6">
                <div class="card report-card shadow-sm text-center p-3">
                    <div class="report-icon mb-2">
                        <!-- <img src="hired-icon.svg" alt="Hired" class="report-icon-img"> -->
                        <span><svg width="42" height="42" viewBox="0 0 42 42" fill="none"
                                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <rect width="42" height="42" fill="url(#pattern0_10661_9746)" />
                                <defs>
                                    <pattern id="pattern0_10661_9746" patternContentUnits="objectBoundingBox"
                                        width="1" height="1">
                                        <use xlink:href="#image0_10661_9746" transform="scale(0.00195312)" />
                                    </pattern>
                                    <image id="image0_10661_9746" width="512" height="512"
                                        preserveAspectRatio="none"
                                        xlink:href="data:image/png;base64,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" />
                                </defs>
                            </svg>
                        </span>
                        <h3 class="report-value-number text-primary fw-bold">{{ $hiredEducatorsCount }}</h3>
                    </div>
                    <h6 class="report-value-label text-muted">Hired Educators</h6>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card shadow-sm text-center p-3">
                    <div class="report-icon mb-2">
                        <!-- <img src="requirements-icon.svg" alt="Posted" class="report-icon-img"> -->
                        <span><svg width="42" height="42" viewBox="0 0 42 42" fill="none"
                                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <rect width="42" height="42" fill="url(#pattern0_10661_9746)" />
                                <defs>
                                    <pattern id="pattern0_10661_9746" patternContentUnits="objectBoundingBox"
                                        width="1" height="1">
                                        <use xlink:href="#image0_10661_9746" transform="scale(0.00195312)" />
                                    </pattern>
                                    <image id="image0_10661_9746" width="512" height="512"
                                        preserveAspectRatio="none"
                                        xlink:href="data:image/png;base64,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" />
                                </defs>
                            </svg>
                        </span>
                        <h3 class="report-value-number text-primary fw-bold">{{ $requirementsPostedCount }}</h3>
                    </div>
                    <h6 class="report-value-label text-muted">Requirements Posted</h6>
                </div>
            </div>
            {{-- <div class="col-md-6 mt-3">
                            <div class="card report-card shadow-sm text-center p-3">
                                <div class="report-icon mb-2">
                                    <!-- <img src="talent-icon.svg" alt="Talent" class="report-icon-img"> -->
                                    <span><svg width="42" height="42" viewBox="0 0 42 42" fill="none"
                                            xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                            <rect width="42" height="42" fill="url(#pattern0_10661_9746)" />
                                            <defs>
                                                <pattern id="pattern0_10661_9746" patternContentUnits="objectBoundingBox"
                                                    width="1" height="1">
                                                    <use xlink:href="#image0_10661_9746" transform="scale(0.00195312)" />
                                                </pattern>
                                                <image id="image0_10661_9746" width="512" height="512"
                                                    preserveAspectRatio="none"
                                                    xlink:href="data:image/png;base64,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" />
                                            </defs>
                                        </svg>
                                    </span>
                                    <h3 class="report-value-number text-primary fw-bold">120</h3>
                                </div>
                                <h6 class="report-value-label text-muted">Saved Talent</h6>
                            </div>
                        </div> --}}
            {{-- <div class="col-md-6 mt-3">
                            <div class="card report-card shadow-sm text-center p-3">
                                <div class="report-icon mb-2">
                                    <!-- <img src="contracts-icon.svg" alt="Contracts" class="report-icon-img"> -->
                                    <span><svg width="46" height="46" viewBox="0 0 46 46" fill="none"
                                            xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                            <rect width="46" height="46" fill="url(#pattern0_10661_9752)" />
                                            <defs>
                                                <pattern id="pattern0_10661_9752" patternContentUnits="objectBoundingBox"
                                                    width="1" height="1">
                                                    <use xlink:href="#image0_10661_9752" transform="scale(0.00195312)" />
                                                </pattern>
                                                <image id="image0_10661_9752" width="512" height="512"
                                                    preserveAspectRatio="none"
                                                    xlink:href="data:image/png;base64,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" />
                                            </defs>
                                        </svg>
                                    </span>
                                    <h3 class="report-value-number text-primary fw-bold">22</h3>
                                </div>
                                <h6 class="report-value-label text-muted">Contracts Generated</h6>
                            </div>
                        </div> --}}

        </div>
    </div>
</div>
</div>
<section>
    <hr>
    <div class="row">
        <div class="col-lg-6 mt-5 mb-5">
            <a class="req-heading text-blue">
                <i class="fa fa-arrow-left me-2 ms-4"></i> Posted Requirements
            </a>
        </div>
        <div class="col-lg-6 mt-5 mb-5">
            <div class="d-flex w-100 justify-content-end">
                <a href="{{ route('post-requirements') }}" class="post-arequirement-btn hover_white">
                    + <span class="ms-4"> Post a new requirement <span>
                </a>
            </div>
        </div>
    </div>
    <div class="requirement-continer">
        @include('school-marketplace.components.all-requirements-content')
    </div>
    <div class="text-center mt-4 mb-4">
        <a href="{{ route('allRequirements') }}" class="btn" style="background-color: #0056d2; color: white; text-decoration: none; padding: 10px 20px; border-radius: 30px; font-weight: 600;">
            Show all posted requirements
        </a>
    </div>
</section>
</div>
@endsection