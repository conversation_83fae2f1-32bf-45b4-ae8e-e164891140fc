<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNewSchoolPostRequirementTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('new_school_post_requirement', function (Blueprint $table) {
            $table->id();
            $table->string('position_title')->nullable();
            $table->string('requirement')->nullable();
            $table->string('delivery_mode')->nullable();
            $table->longText('address')->nullable();
            $table->string('position')->nullable();
            $table->string('instructor_language')->nullable();
            $table->string('states_id')->nullable();
            $table->string('certifications')->nullable();
            $table->string('grade_level_id')->nullable();
            $table->string('subject')->nullable();
            $table->integer('total_program_duration')->nullable();
            $table->date('position_start_date')->nullable();
            $table->date('position_end_date')->nullable();
            $table->string('schedule')->nullable();
            $table->time('schedule_start_time')->nullable();
            $table->time('schedule_end_time')->nullable();
            $table->longText('job_description')->nullable();
            $table->string('compensation')->nullable();
            $table->string('per_hour')->nullable();
            $table->longText('benefits')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('new_school_post_requirement');
    }
}
