
$('body').on('click', '.delete_data_activity_theory_lession', function () {
  var id = $(this).attr("data-id");
  currentRow = $(this);
  swal({
    title: "Delete?",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes, delete it!",
    cancelButtonText: "No, cancel!",
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      var url = APP_URL + '/deleteactivitytheorylession';
      $.ajax({
        type: 'POST',
        url: url,
        data: { id: id },
        dataType: "json",
        beforeSend: function () {
          $(".loader").removeClass('d-none');
        },
        success: function (data) {
          currentRow.parents('tr').remove();
          $(".loader").addClass('d-none');
          if (data.success == true) {
            alertify.success(data.message);
          } else {
            alertify.error(data.message);
          }

        },
      }).done(function () {
        setTimeout(function () {
          $(".loader").addClass('d-none');
        }, 500);
      });
    }
    else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
})



$("#add_activity_theory").click(function () {

  var name1 = $('#activity_id').val();
  if (name1 == "") {
    $('#activity_id_error').html('Please select category');
    $('#activity_id').val('');
    $('#activity_id').focus();
    return false;

  } else {
    $('#activity_id_error').html('');
  }
  var name = $('#lesson_name').val();
  if (name == "") {
    $('#lesson_name_error').html('Please enter lesson name');
    $('#lesson_name').val('');
    $('#lesson_name').focus();
    return false;

  } else {
    $('#lesson_name_error').html('');
  }
  if (!/^[a-z A-Z]*$/g.test(name)) {
    $('#lesson_name_error').html('Allow only characters');
    $('#lesson_name').focus();
    return false;
  } else {
    $('#lesson_name_error').html('');
  }


  // var name2 =$('#about_lesson').val();
  var name2 = CKEDITOR.instances['about_lesson'].getData();
  if (name2 == "") {
    $('#about_lesson_error').html('Please enter about activity');
    $('#about_lesson').val('');
    $('#about_lesson').focus();
    return false;

  } else {
    $('#about_lesson_error').html('');
  }



  var status_value = $('#status').val();
  if (status_value == "") {

    $('#status_error').html('Please select status');
    $('#status').val('');
    $('#status').focus();
    return false;
  } else {
    $('#status_error').html(' ');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  for (instance in CKEDITOR.instances) {
    CKEDITOR.instances[instance].updateElement();
  }
  var url = APP_URL + '/storeactivitytheory';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#add_activity_theory_form').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        alertify.success(data.message);
        $('#add_activity_theory_form')[0].reset();
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }

    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });

});



$("#edit_acitivty_theory_lesson").click(function () {

  var name1 = $('#activity_id').val();
  if (name1 == "") {
    $('#activity_id_error').html('Please select category');
    $('#activity_id').val('');
    $('#activity_id').focus();
    return false;

  } else {
    $('#activity_id_error').html('');
  }
  var name = $('#lesson_name').val();
  if (name == "") {
    $('#lesson_name_error').html('Please enter lesson name');
    $('#lesson_name').val('');
    $('#lesson_name').focus();
    return false;

  } else {
    $('#lesson_name_error').html('');
  }
  if (!/^[a-z A-Z]*$/g.test(name)) {
    $('#lesson_name_error').html('Allow only characters');
    $('#lesson_name').focus();
    return false;
  } else {
    $('#lesson_name_error').html('');
  }

  var name2 = CKEDITOR.instances['about_lesson'].getData();
  // var name2 =$('#about_lesson').val();
  if (name2 == "") {
    $('#about_lesson_error').html('Please enter about lesson');
    $('#about_lesson').val('');
    $('#about_lesson').focus();
    return false;

  } else {
    $('#about_lesson_error').html('');
  }



  var status_value = $('#status').val();
  if (status_value == "") {

    $('#status_error').html('Please select status');
    $('#status').val('');
    $('#status').focus();
    return false;
  } else {
    $('#status_error').html(' ');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  for (instance in CKEDITOR.instances) {
    CKEDITOR.instances[instance].updateElement();
  }

  var url = APP_URL + '/editactivitytheory';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#edit_activity_theory_form').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
      } else {
        alertify.error(data.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });
});


$('body').on('click', '#changeStatusActivityTheory', function () {
  var id = $(this).attr("data-id");
  var datavalue = $(this).attr("data-value");
  swal({
    title: "Change status",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes!",
    cancelButtonText: "No, cancel!",
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      var url = APP_URL + '/change-status-activity-theory';

      $.ajax({
        type: 'POST',
        url: url,
        data: { id: id, id1: datavalue },
        dataType: "json",
        beforeSend: function () {
          // $("#overlay").fadeIn(300)
        },
        success: function (data) {
          if (data.success == true) {
            alertify.success(data.message);
            if (datavalue == '1') {
              $('.activityChangeTheory-' + id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
              $('.activityChangeTheory-' + id).html('Active');
            } else {
              $('.activityChangeTheory-' + id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
              $('.activityChangeTheory-' + id).html('Deactive');
            }
          } else {
            alertify.error(data.message);
          }

        },
      }).done(function () {
        setTimeout(function () {
          // $("#overlay").fadeOut(300);
        }, 500);

      });

    }
    else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
})





//**************************************************//






//  login validation 
$("#loginbtn1").click(function (event) {
  event.preventDefault()
  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var regemailid = emailfilter.test(document.getElementById("email").value);
  (regemailid);

  if (regemailid == false) {
    $('.email_error').html('Please enter a valid Email');
    document.getElementById("email").focus();
    $('#email').css('border-color', '#495057');
    return false;
  } else {
    $('.email_error').html('');
    $('#email').css('border-color', '#495057');
  }
  var password = document.getElementById('passwords').value;
  if (password.length == "") {
    $('.password_error').html('Please enter your password');
    document.getElementById('passwords').value = "";
    document.getElementById('passwords').focus();
    return false;
  } else {
    $('.password_error').html('');
    $('#passwords').css('border-color', '#495057');
  }

  // if($('#terms').is(':checked') == false){
  //      event.preventDefault();
  //      alertify.error('Accept, you must accept our terms and conditions');
  //      return false;
  //  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/loginpost-institute';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#loginform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        // alertify.success(data.message);
        window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $(".loader").addClass('d-none');
    }, 500);
  });
});

$(document).on('change', '.profileImgUploads', function (e) {
  var fileName = e.target.files[0].name;
  var image = $('.profileImgUploads').val();
  var img_ex = /(\.jpg|\.jpeg|\.png|\.gif)$/i;
  if (!img_ex.exec(image)) {
    $('#image').val('');
    return false;
  } else {
    var th = $(this);
    var url = APP_URL + '/update_profile_picture';

    $.ajax({
      type: 'POST',
      url: url,
      data: new FormData($('#profileuploadc')[0]),
      processData: false,
      contentType: false,
      dataType: 'json',
      success: function (data) {
        alertify.success(data.message);
        $('#imagePreview').attr("src", APP_URL + '/public/profile/' + data.img);
        $('#profileImgUploads').attr("value", data.img);
      }
    })
  }
})

$('body').on('click', '#personalDetailsSaveBtn', function () {
  var first_name = $('#pr_first_name').val();
  var last_name = $('#pr_last_name').val();
  var about_you = $('#about').val();
  var dob = $('#dob').val();

  if (first_name == "") {
    $('#p_first_name_error').html('Please enter first name');
    $('#pr_first_name').focus();
    return false;
  } else {
    $('#p_first_name_error').html('');
  }
  if (!/^[a-z A-Z]*$/g.test($('#pr_first_name').val())) {
    $('#p_first_name_error').html('Allow only characters!!');
    $('#pr_first_name').focus();
    return false;
  } else {
    $('#p_first_name_error').html('');
  }

  if (last_name == "") {
    $('#p_last_name_error').html('Please enter last name');
    $('#pr_last_name').focus();
    return false;
  } else {
    $('#p_last_name_error').html('');

  }
  if (!/^[a-z A-Z]*$/g.test($('#pr_last_name').val())) {
    $('#p_last_name_error').html('Allow only characters!!');
    $('#pr_last_name').focus();
    return false;
  } else {
    $('#p_last_name_error').html('');
  }
  if ($('input[type=radio][name=gender]:checked').length == 0) {
    $('#p_gender_error').html('Please select gender!!');

    return false;
  } else {
    $('#p_gender_error').html(' ');
  }

  if (dob == "") {
    $('#p_dob_error').html('Please enter your date of birth!!');
    $('#dob').focus();
    return false;
  } else {
    $('#p_dob_error').html('');
  }

  if (about_you == "") {
    $('#about_you_error').html('Please enter about');
    $('#about').focus();
    return false;
  } else {
    $('#about_you_error').html(' ');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/personalInfo';
  var formData = $('#persnal_details').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#contsaveotherdetails', function () {

  var email = $('#email').val();
  var umobile = $("#MobPhone").val();
  var gender = $("#Facebooklink").val();
  var staticLinkedIn = $("#LinkedInlink").val();
  var staticTwitter = $("#Twitterlink").val();
  var fburl = document.getElementById("Facebooklink").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (fburl != "") {
    if (!regexp.test(fburl)) {
      $('#EditableFacebook_error').html('Please enter valid url.');
      document.getElementById('Facebooklink').value = "";
      document.getElementById('Facebooklink').focus();
      return false;
    } else {
      $('#EditableFacebook_error').html('');
    }
  }
  var linurl = document.getElementById("LinkedInlink").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (linurl != "") {
    if (!regexp.test(linurl)) {
      $('#editableLinkedIn_error').html('Please enter valid url.');
      document.getElementById('LinkedInlink').value = "";
      document.getElementById('LinkedInlink').focus();
      return false;
    } else {
      $('#editableLinkedIn_error').html('');
    }
  }
  var twiturl = document.getElementById("Twitterlink").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (twiturl != "") {
    if (!regexp.test(twiturl)) {
      $('#editableTwitter_error').html('Please enter valid url.');
      document.getElementById('Twitterlink').value = "";
      document.getElementById('Twitterlink').focus();
      return false;
    } else {
      $('#editableTwitter_error').html('');
    }
  }

  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var regemailid = emailfilter.test($("#email").val());
  (regemailid);
  if (regemailid == false) {
    $('#email_error').html('Please enter a valid email');
    $("#email").focus();
    return false;
  } else {
    $('#email_error').html('');
  }

  if (umobile == "") {
    $('#editableMobilePhone_error').html('Please enter the mobile number');
    $("#editableMobilePhone").focus();
    return false;
  } else {
    $('#editableMobilePhone_error').html('');
  }
  if ((umobile.length < 12) || (umobile.length > 12)) {
    $('#editableMobilePhone_error').html('Your mobile number must be 1 to 10 Integers');

    $("#editableMobilePhone").focus();
    return false;
  } else {
    $('#editableMobilePhone_error').html('');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-contact';
  var formData = $('#cont_otherdetails').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#expiranceinfoSaveBtn', function (event) {
  event.preventDefault()
  var cttp = true;

  $.each($(".experience_row"), function () {
    var company_name_info = $(this).find('.company_name_info').val();
    if (company_name_info == "") {
      $(this).find('.company_name_info_error').html('Please Enter company');    
      $(this).find('.company_name_info').focus();
      cttp = false;
      return false;
    } else {
      
      $(this).find('.company_name_info_error').html(' ');
    }

    var current_designation = $(this).find('.current_designation').val();
    if (current_designation == "") {
      $(this).find('.current_designation_error').html('Please Enter Position / Job Profile');    
      $(this).find('.current_designation').focus();
      cttp = false;
      return false;
    } else {
       $(this).find('.current_designation_error').html(' ');  
    }

    var joining_date = $(this).find('.joining_date').val();
    if (joining_date == "") {
       $(this).find('.joining_date_error').html('Please Enter Joining Date');    
      $(this).find('.joining_date').focus();
      cttp = false;
      return false;
    } else {
       $(this).find('.joining_date_error').html(' ');  
    }
    

  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/update-expiranceinfo';
  var thi = $(this);
  $.ajax({
    url: url,
    type: 'post',
    data: $('#expiranceinfo').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
});
$(document).ready(function () {
  var i = 1;
  $('body').on('click', '.addMoreExpiranceBtn_test', function () {

    i++;
    $("#experienceinfoid1").append(` <div id="rowexp` + i + `" class="row experience_row pt-3 mb-3 key">
    <div class="col-md-6 form-group">
    <label class="form-label">Institute / Company Name</label>
    <input type="text" id="company" name="company[]" class="form-control company_name_info" placeholder="Institute / Company Name">
    <span id="company_name_info_error" class="err company_name_info_error"></span>
    </div>

    <div class="col-md-6 form-group">
    <label class="form-label">Position / Job Profile</label>
    <input type="text" id="current_designation" name="current_designation[]"  class="form-control current_designation" placeholder="Position / Job Profile">
    <span id="current_designation_error" class="err current_designation_error"></span>
    </div>

    <div class="col-lg-4 col-md-6 form-group">
    <label class="form-label">Joining Date</label>
    <div class="input-group date"  id="datetimepicker_`+ i + `" data-target-input="nearest">
    <input type="text" name="joining_date[]" class="form-control datetimepicker-input joining_date" data-target="#datetimepicker_`+ i + `"/>
    <div class="input-group-append" data-target="#datetimepicker_`+ i + `" data-toggle="datetimepicker">
    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
    </div>
    </div>
    <span id="joining_date_error" class="err joining_date_error"></span>
    </div>

    <div class="col-lg-4 col-md-6 form-group">
    <label class="form-label">Leaving Date</label>
    <div class="input-group date" id="datetimepickerl_`+ i + `" data-target-input="nearest">
    <input type="text" name="leaving_date[]" class="form-control datetimepicker-input " data-target="#datetimepickerl_`+ i + `"/>
    <div class="input-group-append" data-target="#datetimepickerl_`+ i + `" data-toggle="datetimepicker">
    <div class="input-group-text"  onclick="myFunction(this,`+ i + `,'datetimepicker_','datetimepickerl_')"><i class="fa fa-calendar"></i></div>
    </div>
    </div>
    </div>

    <div class="col-lg-4 mb-lg-3">
    <div class="d-flex justify-content-between align-items-end h-100">
    <div class="custom-control custom-radio custom-control-inline mb-2">
    <input type="checkbox" value="1" class="custom-control-input" id="currentlyWorking_`+ i + `" name="current_works[]">
    <label class="custom-control-label" for="currentlyWorking_`+ i + `">Currently Working Here</label>
    </div>

    <button type="button" name="remove" id="`+ i + `" class="btn btn-danger clientremoveMoreExpBtn"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2 align-middle"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg></button>
    </div>
    </div>
    </div>`);
    $('#datetimepicker_'+i).datetimepicker({
      pickTime: false,
      format: 'YYYY-MM-DD',
      maxDate: new Date(),
    });

  

  })

  $(document).on('click', '.clientremoveMoreExpBtn', function () {
    var button_id = $(this).attr("id");
    $('#rowexp' + button_id + '').remove();
  });
})



$('body').on('click', '#saveEducation', function (event) {
  event.preventDefault()
  var cttp = true;
  $.each($(".education_row"), function () {
    var highest_qualification = $(this).find('.highest_qualification').val();
    if (highest_qualification == "") {
      $(this).find('.highest_qualification_error').html('Please Enter Highest Qualification');    
      $(this).find('.highest_qualification').focus();
      cttp = false;
      return false;
    } else {
       $(this).find('.highest_qualification_error').html(' ');  
    }

    var subject = $(this).find('.subject').val();
    if (subject == "") {
       $(this).find('.subject_error').html('Please Enter Subject');    
      $(this).find('.subject').focus();
      cttp = false;
      return false;
    } else {
       $(this).find('.subject_error').html(' ');  
    }

    var joining_date = $(this).find('.joining_date').val();
    if (joining_date == "") {
       $(this).find('.joining_date_error').html('Please Enter Joining Date');    
      $(this).find('.joining_date').focus();
      cttp = false;
      return false;
    } else {
       $(this).find('.joining_date_error').html(' ');  
    }

  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-eductionInfo';
  var thi = $(this);
  $.ajax({
    url: url,
    type: 'post',
    data: $('#educationForm').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$(document).ready(function () {
  var i = 1;
  $('body').on('click', '.addMoreEducationBtn', function () {
    i++;
    $("#EducationsInfo").append(` <div id="row` + i + `" class="row education_row pt-3 mb-3">
    <div class="col-md-6 form-group">
    <label class="form-label">School / University Name</label>
    <input type="text" id="highest_qualification" name="highest_qualification[]" class="form-control highest_qualification" placeholder="School / University Name">
    <span id="highest_qualification_error" class="err highest_qualification_error "></span>
    </div>

    <div class="col-md-6 form-group">
    <label class="form-label">Subject</label>
    <input type="text" id="subject" name="subject[]" class="form-control subject" placeholder="Subject">
    <span id="subject_error" class="err subject_error"></span>
    </div>

    <div class="col-lg-4 col-md-6 form-group">
    <label class="form-label">Joining Date</label>
    <div class="input-group date"  id="datetimepickerr_`+ i + `" data-target-input="nearest">
    <input type="text" name="joining_date[]" class="form-control datetimepicker-input joining_date educationjd " data-target="#datetimepickerr_`+ i + `"/>
    <div class="input-group-append" data-target="#datetimepickerr_`+ i + `" data-toggle="datetimepicker">
    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
    </div>
    </div>
    <span id="joining_date_error" class="err joining_date_error"></span>
    </div>

    <div class="col-lg-4 col-md-6 form-group">
    <label class="form-label">Leaving Date</label>
    <div class="input-group date datetimepickerr"   id="datetimepickerlr_`+ i + `" data-target-input="nearest">
    <input type="text" name="leaving_date[]"  class="form-control datetimepicker-input " data-target="#datetimepickerlr_`+ i + `"/>
    <div class="input-group-append" data-target="#datetimepickerlr_`+ i + `" data-toggle="datetimepicker">
    <div class="input-group-text" onclick="myFunction(this,`+ i + `,'datetimepickerr_','datetimepickerlr_')" data-id="`+ i + `"><i class="fa fa-calendar"></i></div>
    </div>
    </div>
    </div>

    <div class="col-lg-4 mb-lg-3">
    <div class="d-flex justify-content-between align-items-end h-100">
    <div class="custom-control custom-radio custom-control-inline mb-2">
    <input type="checkbox" class="custom-control-input" id="currentlyStudying_`+ i + `" value="1" name="current_study[]">
    <label class="custom-control-label" for="currentlyStudying_`+ i + `">Currently Studying Here</label>
    </div>

    <button type="button" name="remove" id="`+ i + `" class="btn btn-danger clientremoveMoreEduBtn"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2 align-middle"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg></button>
    </div>
    </div>

    </div>`);

  $('#datetimepickerr_'+i).datetimepicker({
      pickTime: false,
      format: 'YYYY-MM-DD',
      maxDate: new Date(),
    });
  })
  $(document).on('click', '.clientremoveMoreEduBtn', function () {
    var button_id = $(this).attr("id");
    $('#row' + button_id + '').remove();
  });

 
  


  
})
function myFunction(thi,val,name,lname) {

   var start_date = $('#'+name+''+val).children('input').val();
  //  console.log(start_date);

   var date = new Date(start_date);
   $('#'+lname+''+val).datetimepicker("destroy");
    $('#'+lname+''+val).datetimepicker({
      format: 'YYYY-MM-DD',
      minDate: date,
      maxDate: new Date(),

  });

  }

// $('body').on('click', '.clientremoveMoreEduBtn', function(e){
//   debugger
//     // e.preventDefault();
//     $(this).find('.experience_row').remove(); //Remove field html
//     // x--; 
//     //Decrement field counter
// }); 


$('body').on('click', '#addSkillsBtnS', function () {

  var skills = $('#addSkillsText').val();

  if (skills == "") {
    $('#skills_error').html('Please Select skill');
    $('$addSkillsText').focus();
    return false;
  } else {
    $('#skills_error').html(' ');
    $('#addSkillsText').val(' ')
    $(`<div class="chip" tabindex="-1">
          <span>
          ${skills}
          </span>
          <span class="" title="Remove chip" aria-label="Remove chip" type="button" onclick="$(this).parent().remove()">
          <i class="fas fa-fw fa-times-circle align-middle text-danger ml-1"></i>
          </span>
          </div>`).appendTo("#skillsChipsContainer");
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/AddSkills';
  $.ajax({
    url: url,
    type: 'post',
    data: { skills: skills },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$('body').on('click', '.RemoveSkills', function () {

  var id = $(this).attr("data-id");
  $(this).parent().remove();
  // alert(id);
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/RemoveSkills';

  $.ajax({
    url: url,
    type: 'post',
    data: { id: id },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

// chang password 

$('body').on('click', '#clientchangepass', function () {
  var old_password = document.getElementById('old_password').value;
  if (old_password.length == "") {
    $('.old_password_error').html('Please Enter Your Old password');
    document.getElementById('old_password').value = "";
    document.getElementById('old_password').focus();
    return false;
  } else {
    $('.old_password_error').html('');
    $('#old_password').css('border-color', '#495057');
  }

  var new_password = document.getElementById('new_password').value;
  if (new_password.length == "") {
    $('.new_password_error').html('Please Enter Your New Password');
    document.getElementById('new_password').value = "";
    document.getElementById('new_password').focus();
    return false;
  } else {
    $('.new_password_error').html('');
    $('#new_password').css('border-color', '#495057');
  }

  var confirm_password = document.getElementById('confirm_password').value;
  if (confirm_password.length == "") {
    $('.confirm_password_error').html('Please Enter Your Confirm Password');
    document.getElementById('confirm_password').value = "";
    document.getElementById('confirm_password').focus();
    return false;
  } else {
    $('.confirm_password_error').html('');
    $('#confirm_password').css('border-color', '#495057');
  }

  if (new_password != confirm_password) {
    $('.confirm_password_error').html('The password and confirm password do not match!!');
    document.getElementById('confirm_password').value = "";
    document.getElementById('confirm_password').focus();
    return false;
  } else {
    $('.confirm_password_error').html('');
    $('#confirm_password').css('border-color', '#495057');
  }


  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/updateChangePasswordforclient';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#clientchangepwdform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // $("#changepwdform").reset();
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    $(".loader").addClass('d-none');
  });

})

$('body').on('click', '#addTrainer', function (event) {
  event.preventDefault()
  var first_name = $('#first_name').val();
  var last_name = $('#last_name').val();
  var association_logo = $("#phone_number").val();
  var email = $("#email").val();
  var about = $("#about").val();
  var highest_qualification = $("#highest_qualification").val();
  var subject = $("#subject").val();
  var skill_name = $("#addtrainerSkillsText").val();


  if (first_name == "") {
    $('#name_error').html('Please enter the first name');
    document.getElementById('first_name').value = "";
    document.getElementById('first_name').focus();
    return false;
  } else {
    $('#name_error').html('');
  }
  if (last_name == "") {
    $('#t_last_name_error').html('Please enter the last name');
    document.getElementById('last_name').value = "";
    document.getElementById('last_name').focus();
    return false;
  } else {
    $('#t_last_name_error').html('');
  }
  if (document.getElementById("Male").checked || document.getElementById("Female").checked || document.getElementById("Other").checked) {
    $('#t_gender_error').html('');
  } else {
    $('#t_gender_error').html('Please select gender');

    document.getElementById("t_gender_error").scrollIntoView();
    return false;
  }
  var dob = $('#dob').val();
  if (dob == "") {
    $('#dob_error').html('Please enter date of birth');
    $('#dob').focus();
    return false;
  } else {
    $('#dob_error').html('');
  }
  // var latitude = $("#latitude").val();
  // if (latitude == "") {
  //   $('#latitude_error').html('Please write latitude');
  //   $("#latitude").focus();

  //   return false;
  // } else {
  //   $('#latitude_error').html('');
  // }
  // var longitude = $("#longitude").val();
  // if (longitude == "") {
  //   $('#longitude_error').html('Please write longitude');
  //   $("#longitude").focus();

  //   return false;
  // } else {
  //   $('#longitude_error').html('');
  // }
  var address = $("#address").val();
  if (address == "") {
    $('#address_error').html('Please enter address');
    document.getElementById('address').value = "";
    document.getElementById('address').focus();
    return false;
  } else {
    $('#address_error').html('');
  }
  if (about == "") {
    $('#about_you_error').html('Please write about you');
    document.getElementById('about').value = "";
    document.getElementById('about').focus();
    return false;
  } else {
    $('#about_you_error').html('');
  }
  var fburl = document.getElementById("EditableFacebook").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (fburl != "") {
    if (!regexp.test(fburl)) {
      $('#EditableFacebook_error').html('Please enter valid url.');
      document.getElementById('EditableFacebook').value = "";
      document.getElementById('EditableFacebook').focus();
      return false;
    } else {
      $('#EditableFacebook_error').html('');
    }
  }
  var linurl = document.getElementById("editableLinkedIn").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (linurl != "") {
    if (!regexp.test(linurl)) {
      $('#editableLinkedIn_error').html('Please enter valid url.');
      document.getElementById('editableLinkedIn').value = "";
      document.getElementById('editableLinkedIn').focus();
      return false;
    } else {
      $('#editableLinkedIn_error').html('');
    }
  }
  var twiturl = document.getElementById("editableTwitter").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (twiturl != "") {
    if (!regexp.test(twiturl)) {
      $('#editableTwitter_error').html('Please enter valid url.');
      document.getElementById('editableTwitter').value = "";
      document.getElementById('editableTwitter').focus();
      return false;
    } else {
      $('#editableTwitter_error').html('');
    }
  }
  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var regemailid = emailfilter.test($("#email").val());
  (regemailid);
  if (regemailid == false) {
    $('.email_error').html('Please enter a valid email');
    $("#email").focus();
    return false;
  } else {
    $('.email_error').html('');
  }
  var cttp = true;
  $.each($(".experience_row"), function () {
    var company = $(this).find('#company').val();
    if (company == "") {
      $(this).find('.company_name_info_error').html('Please write Institute / Company Name');    
    
      $(this).find('#company').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('.company_name_info_error').html('');   
      // $(this).find('#company_name_info_error').html(' ');
    }

    var current_designation = $(this).find('#current_designation').val();
    if (current_designation == "") {
      // $('#current_designation_error').html('Please write Position / Job Profile');
      $(this).find('.current_designation_error').html('Please write Position / Job Profile');   
      $(this).find('#current_designation').focus();
      cttp = false;
      return false;
    } else {
      // $(this).find('#current_designation_error').html(' ');
      $(this).find('.current_designation_error').html(''); 
    }

    var joining_date = $(this).find('.joining_date').val();
    if (joining_date == "") {
      // $('#joining_date_error').html('Please write Position / Job Profile');
      $(this).find('.joining_date_error').html('Please write Position / Job Profile');   
      $(this).find('.joining_date').focus();
      cttp = false;
      return false;
    } else {

      $(this).find('.joining_date_error').html(' ');
    }
  })
  if (cttp === false) {
    return false;
  }
  var cttps = true;
  $.each($(".education_row"), function () {
    var highest_qualification = $(this).find('#highest_qualification').val();
   
    if (highest_qualification == "") {
      $(this).find('.highest_qualification_error').html('Please write school / university Name');  
      // $('#highest_qualification_info_error').html('Please write school / university Name');
      $(this).find('#highest_qualification').focus();
      cttps = false;
      return false;
    } else {
      $(this).find('.highest_qualification_error').html('');  
      // $(this).find('#highest_qualification_info_error').html(' ');
    }

    var subject = $(this).find('#subject').val();
    if (subject == "") {
      $(this).find('.subject_error').html('Please write subject');  
      // $('#subject_info_error').html('Please write subject');
      $(this).find('#subject').focus();
      cttps = false;
      return false;
    } else {
      $(this).find('.subject_error').html('');  
      // $(this).find('#subject_info_error').html(' ');
    }

    var educationjd = $(this).find('.educationjd').val();
    if (educationjd == "") {
      $(this).find('.joining_date_error').html('Please write joing date');  
      // $('#jeducationjd_error').html('Please write joing date');
      $(this).find('.educationjd').focus();
      cttps = false;
      return false;
    } else {
      $(this).find('.joining_date_error').html('');
      // $(this).find('#jeducationjd_error').html(' ');
    }
  })
  if (cttps === false) {
    return false;
  }


  // console.log(skill_name, "skill_name")
  // if (!skill_name) {
  //   $('#skills_error').html('Please write Skills');
  //   document.getElementById('skill_name').value = "";
  //   document.getElementById('skill_name').focus();
  //   return false;
  // } else {
  //   $('#skills_error').html('');
  // }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/save-trainer';
  // var formData = $('#addtrainerfrm').serialize();
  var formData = new FormData($("#addtrainerfrm")[0]);

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    contentType: false,
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        $('#addtrainerfrm')[0].reset();
        window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#addtrainerSkills', function (event) {
  var i = 1;
  event.preventDefault()
  var addtrainerSkillsText = document.getElementById("addtrainerSkillsText").value;

  i++;
  $('#dynamic_field').append(`<div class="input-group mt-2" id="row` + i + `">
  <input type="text" class="form-control" id="addtrainerSkillsText" name="skill_name[]" placeholder="Add Skills" value="`+ addtrainerSkillsText + `">
  <div class="input-group-append">
  <button type="button" name="remove" id="`+ i + `" class="btn btn-danger removeSkillbtn">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2 align-middle"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
  </button></div><br><span class="err" id="skills_error"></span></div>`);
  document.getElementById('addtrainerSkillsText').value = '';

})
$(document).on('click', '.removeSkillbtn', function () {
  var button_id = $(this).attr("id");
  $('#row' + button_id + '').remove();
});

$('body').on('click', '#addtrainerSkillsBtn', function () {

  var skills = $('#addtrainersklText').val();
  var trainer_id = $(this).attr("data-id") // will return the string "123"
  // alert(id);
  if (skills == "") {
    $('#skills_error').html('Please Select skill');
    $('$addSkillsText').focus();
    return false;
  } else {
    $('#skills_error').html(' ');
    $('#addSkillsText').val(' ')
    $(`<div class="chip" tabindex="-1">
        <span>
        ${skills}
        </span>
        <span class="" title="Remove chip" aria-label="Remove chip" type="button" onclick="$(this).parent().remove()">
        <i class="fas fa-fw fa-times-circle align-middle text-danger ml-1"></i>
        </span>
        </div>`).appendTo("#trainerskillsChips");
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/AddtrainerSkills';
  $.ajax({
    url: url,
    type: 'post',
    data: { skills: skills, trainer_id: trainer_id },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$('body').on('click', '.RemoveTrainerSkills', function () {

  var id = $(this).attr("data-id");
  $(this).parent().remove();
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/RemoveTrainerSkills';

  $.ajax({
    url: url,
    type: 'post',
    data: { id: id },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$('body').on('click', '#TrainerPersonalDetaileupdate', function (event) {
  event.preventDefault()
  var latitude = $("#latitude").val();
  if (latitude == "") {
    $('#latitude_error').html('Please write latitude');
    $("#latitude").focus();

    return false;
  } else {
    $('#latitude_error').html('');
  }
  var longitude = $("#longitude").val();
  if (longitude == "") {
    $('#longitude_error').html('Please write longitude');
    $("#longitude").focus();

    return false;
  } else {
    $('#longitude_error').html('');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/updatetrainerinfo';
  var formData = $('#singleTrainerfrm').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#trainercontactbtn', function (event) {
  event.preventDefault()
  var fburl = document.getElementById("EditableFacebook").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (fburl != "") {
    if (!regexp.test(fburl)) {
      $('#EditableFacebook_error').html('Please enter valid url.');
      document.getElementById('EditableFacebook').value = "";
      document.getElementById('EditableFacebook').focus();
      return false;
    } else {
      $('#EditableFacebook_error').html('');
    }
  }
  var linurl = document.getElementById("editableLinkedIn").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (linurl != "") {
    if (!regexp.test(linurl)) {
      $('#editableLinkedIn_error').html('Please enter valid url.');
      document.getElementById('editableLinkedIn').value = "";
      document.getElementById('editableLinkedIn').focus();
      return false;
    } else {
      $('#editableLinkedIn_error').html('');
    }
  }
  var twiturl = document.getElementById("editableTwitter").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (twiturl != "") {
    if (!regexp.test(twiturl)) {
      $('#editableTwitter_error').html('Please enter valid url.');
      document.getElementById('editableTwitter').value = "";
      document.getElementById('editableTwitter').focus();
      return false;
    } else {
      $('#editableTwitter_error').html('');
    }
  }
  var email = $('#email').val();
  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var regemailid = emailfilter.test($("#email").val());
  (regemailid);
  if (regemailid == false) {
    $('#email_error').html('Please enter a valid email');
    $("#email").focus();
    return false;
  } else {
    $('#email_error').html('');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-trainer-contact';
  var formData = $('#trainercontactfrm').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#traexpiranceinfoSaveBtn', function (event) {
  event.preventDefault()
  var cttp = true;
  $.each($(".experience_row"), function () {
    var company_name_info = $(this).find('.company_name_info').val();
    if (company_name_info == "") {
      $(this).find('#company_name_info_error').html('Please Enter institute / company Name');
      $(this).find('.company_name_info').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#company_name_info_error').html(' ');
    }

    var current_designation = $(this).find('#current_designation').val();
    if (current_designation == "") {
      $(this).find('.current_designation_error').html('Please Enter position / job Profile');
      $(this).find('#current_designation').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#current_designation_error').html(' ');
    }

    var joining_date = $(this).find('.joining_date').val();
    if (joining_date == "") {
      $(this).find('#joining_date_error').html('Please Enter joining date');
      $(this).find('.joining_date').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#joining_date_error').html(' ');
    }

  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/update-trainer-expirance';
  var thi = $(this);
  $.ajax({
    url: url,
    type: 'post',
    data: $('#trainerexpirancefrm').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$('body').on('click', '#treducationsSaveBtn', function (event) {
  event.preventDefault()
  var cttp = true;
  $.each($(".education_row"), function () {
    var highest_qualification = $(this).find('.highest_qualification').val();
    if (highest_qualification == "") {
      $(this).find('#highest_qualification_info_error').html('Please Enter school / university Name');
      $(this).find('.highest_qualification').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#highest_qualification_info_error').html(' ');
    }

    var subject = $(this).find('.subject').val();
    if (subject == "") {
      $(this).find('#subject_info_error').html('Please enter subject');
      $(this).find('.subject').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#subject_info_error').html(' ');
    }

    var joining_date = $(this).find('.joining_date').val();
    if (joining_date == "") {
      $(this).find('#joining_datee_error').html('Please Enter joining date');
      $(this).find('.joining_date').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#joining_datee_error').html(' ');
    }

  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-trainer-eductionInfo';
  var thi = $(this);
  $.ajax({
    url: url,
    type: 'post',
    data: $('#traineeducationfrm').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})
$('body').on('click', '#traineractivitybutton', function (event) {
  event.preventDefault()
  var activity = $(this).find('#activity').val();
  if (activity == "") {
    $(this).find('#activity_error').html('Please enter subject');
    $(this).find('#activity').focus();
    cttp = false;
    return false;
  } else {
    $(this).find('#activity_error').html(' ');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-trainer-activity';
  var thi = $(this);
  $.ajax({
    url: url,
    type: 'post',
    data: $('#traineractivityform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})
$(document).on('change', '.trainerprofupload', function (e) {

  var fileName = e.target.files[0].name;
  var image = $('.trainerprofupload').val();
  var img_ex = /(\.jpg|\.jpeg|\.png|\.gif)$/i;
  if (!img_ex.exec(image)) {
    alert('Please upload only .jpg/.jpeg/.png/.gif file.');
    $('#image').val('');
    return false;
  } else {
    var th = $(this);
    var url = APP_URL + '/trainer-profilepic';

    $.ajax({
      type: 'POST',
      url: url,
      data: new FormData($('#traprofileuploadId')[0]),
      processData: false,
      contentType: false,
      dataType: 'json',
      success: function (data) {
        alertify.success(data.message);
        $('#imagePreview').attr("src", APP_URL + '/public/profile/' + data.img);
        $('#trainerprofupload').attr("value", data.img);
      }
    })
  }
})
$('body').on('click', '#trainerremove', function (event) {
  event.preventDefault()
})
function removeTrainer(id) {
  var url = APP_URL + '/delete-trainer/' + id;
  if (confirm('Are you sure to remove this record ?')) {
    $.ajax({
      type: 'get',
      url: url,
      processData: false,
      contentType: false,
      dataType: 'json',
      beforeSend: function () {
        $("#overlay").fadeIn(300)
      },
      success: function (response) {
        if (response.success == true) {
          window.location.href = data.redirect;
          alertify.success(response.message);
        }
      },
    }).done(function () {
      setTimeout(function () {
        $("#overlay").fadeOut(300);
      }, 500);
    });
  }
}

function Trainerstatuschange(id) {
  var url = APP_URL + '/change-status-trainer/' + id;
  if (confirm('Are you sure you want change status ?')) {
    $.ajax({
      type: 'get',
      url: url,
      processData: false,
      contentType: false,
      dataType: 'json',
      beforeSend: function () {
        $("#overlay").fadeIn(300)
      },
      success: function (response) {
        if (response.success == true) {
          // $('#'+id).remove();
          alertify.success(response.message);
        }
      },
    }).done(function () {
      setTimeout(function () {
        $("#overlay").fadeOut(300);
      }, 500);
    });
  }
}


$(document).ready(function () {
  var i = 1;
  $('body').on('click', '.addtrainerMoreEducationBtn', function () {
    i++;
    $("#EducationsInfo").append(` <div id="row` + i + `" class="row education_row pt-3 mb-3">
    <div class="col-md-6 form-group">
    <label class="form-label">School / University Name</label>
    <input type="text" id="highest_qualification" name="highest_qualification[]" class="form-control highest_qualification" placeholder="School / University Name">
    <span id="highest_qualification_info_error" class="err highest_qualification_info_error"></span>
    </div>

    <div class="col-md-6 form-group">
    <label class="form-label">Subject</label>
    <input type="text" id="subject" name="subject[]" class="form-control subject" placeholder="Subject">
    <span id="subject_info_error" class="err subject_info_error"></span>
    </div>
    <div class="col-lg-4 col-md-6 form-group">
    <label class="form-label">Joining Date</label>
    <div class="input-group date" id="datetimepicker711_`+ i + `" data-target-input="nearest">
    <input type="text" class="form-control datetimepicker-input joining_date" data-target="#datetimepicker711_`+ i + `" name="joining_date[]"/>
    <div class="input-group-append" data-target="#datetimepicker711_`+ i + `" data-toggle="datetimepicker">
    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
    </div>
    </div>
    <span id="joining_datee_error" class="err joining_datee_error"></span>
    </div>

    <div class="col-lg-4 col-md-6 form-group">
    <label class="form-label">Leaving Date</label>
    <div class="input-group date" id="datetimepicker811_`+ i + `" data-target-input="nearest">
    <input type="text"  name="leaving_date[]" class="form-control datetimepicker-input" data-target="#datetimepicker811_`+ i + `"/>
    <div class="input-group-append" data-target="#datetimepicker811_`+ i + `" data-toggle="datetimepicker">
    <div class="input-group-text"  onclick="myFunction(this,`+ i + `,'datetimepicker711_','datetimepicker811_')"><i class="fa fa-calendar"></i></div>
    </div>
    </div>
    </div>

    <div class="col-lg-4 mb-lg-3">
    <div class="d-flex justify-content-between align-items-end h-100">
    <div class="custom-control custom-radio custom-control-inline mb-2">
    <input type="checkbox" class="custom-control-input" id="currentlyStudying_`+ i + `" value="1" name="current_study[]">
    <label class="custom-control-label" for="currentlyStudying_`+ i + `">Currently Studying Here</label>
    </div>

    <button type="button" name="remove" id="`+ i + `" class="btn btn-danger clientremoveMoreEduBtn"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2 align-middle"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg></button>
    </div>
    </div> </div>`);


    $('#datetimepicker711_'+i).datetimepicker({
      format: 'YYYY-MM-DD',
      maxDate: new Date()
  });

  // $('#datetimepicker811_'+i).datetimepicker({
  //     useCurrent: false,
  //     format: 'YYYY-MM-DD',
  //     maxDate: new Date()

  // });
  // $("#datetimepicker711_"+i).on("change.datetimepicker", function (e) {
  //     $('#datetimepicker811_'+i).datetimepicker('minDate', e.date);
  // });
  // $("#datetimepicker811_"+i).on("change.datetimepicker", function (e) {
  //     $('#datetimepicker711_'+i).datetimepicker('maxDate', e.date);
  // });
  })
  $(document).on('click', '.clientremoveMoreEduBtn', function () {
    var button_id = $(this).attr("id");
    $('#row' + button_id + '').remove();
  });
})

// 

$('body').on('keyup', '#trainername', function () {

  var instituteidfortrainer = $('#instituteidfortrainer').val();
  var trainername = $(this).val();
  // debugger
  // get_trainer_managementnew(trainername);
  var url = APP_URL + '/get-trainer-management';
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  $.ajax({
    url: url,
    method: 'POST',
    data: {
      trainername: trainername,
      instituteidfortrainer: instituteidfortrainer
    },
    dataType: 'html',
    success: function (data) {
      $('#printtrainermanagementadmin').html('');
      $('#printtrainermanagementadmin').html(data);
    }
  })
});






$('body').on('keyup', '#trainername_institute', function () {
  var trainername = $(this).val();
  get_trainer_managementnew(trainername)
});


$('body').on('click', '.statuschange', function () {
  var id = $(this).data("id"); // will return the number 123
  // var datavalue  = $(this).attr("data-value");
  swal({
    title: "Change status",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes!",
    cancelButtonText: "No, cancel!",
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      var url = APP_URL + '/change-status-trainer/' + id;

      $.ajax({
        type: 'get',
        url: url,
        dataType: "json",
        beforeSend: function () {
          // $("#overlay").fadeIn(300)
        },
        success: function (data) {
          if (data.success == true) {
            alertify.success(data.message);
            if (datavalue == '1') {
              $('.changestatuscls-' + id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
              $('.changestatuscls-' + id).html('Active');
            } else {
              $('.changestatuscls-' + id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
              $('.changestatuscls-' + id).html('Deactive');
            }
          } else {
            alertify.error(data.message);
          }

        },
      }).done(function () {
        setTimeout(function () {
          // $("#overlay").fadeOut(300);
        }, 500);

      });

    }
    else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
})

$('body').on('click', '.deletetrrainers', function () {
  var id = $(this).data("id");;
  currentRow = $(this);
  swal({
    title: "Delete?",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes, delete it!",
    cancelButtonText: "No, cancel!",
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      var url = APP_URL + '/delete-trainer/' + id;
      $.ajax({
        type: 'get',
        url: url,
        dataType: "json",
        beforeSend: function () {
          $(".loader").removeClass('d-none');
        },
        success: function (data) {
          currentRow.parents('tr').remove();
          $(".loader").addClass('d-none');
          if (data.success == true) {
            $('#' + id).remove();
            alertify.success(data.message);
          } else {
            alertify.error(data.message);
          }

        },
      }).done(function () {
        setTimeout(function () {
          $(".loader").addClass('d-none');
        }, 500);
      });
    }
    else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
})

// $('body').on('click','#addCource',function(event){
//    event.preventDefault()
//    var cource_category = $('#cource_category').val();
//    var cource_name = $('#cource_name').val();
//    var course_session = $('#course_session').val();
//    var course_date = $('#course_date').val();
//    var course_timing = $('#course_timing').val();
//    var course_duration = $('#course_duration').val();
//    var course_fee = $('#course_fee').val();
//    var course_benfite = $('#course_benfite').val();
//    var certificate_info = $('#certificate_info').val();
//    var description = $('#description').val();

//    if (cource_category == "") {
//    $('#cource_category_error').html('Please select course category');
//    $('#cource_category').focus();
//    return false;
//    }else{
//    $('#cource_category_error').html('');
//    }
//    if (cource_name == "") {
//    $('#c_cource_name_error').html('Please write course name');
//    $('#cource_name').focus();
//    return false;
//    }else{
//    $('#c_cource_name_error').html('');
//    }
//    if( document.getElementById("course_image").files.length == 0 ){
//    $('#course_image_error').html('Please select course image');
//    $('#course_image').focus();
//    return false;
//    }else{
//      $('#course_image_error').html('');
//    }
//    if (course_session == "") {
//    $('#course_session_error').html('Please write course session');
//    $('#course_session').focus();
//    return false;
//    }else{
//    $('#course_session_error').html('');
//    }
//    if (course_date == "") {
//    $('#course_date_error').html('Please select course date');
//    $('#course_date').focus();
//    return false;
//    }else{
//    $('#course_date_error').html('');
//    }
//    if (course_timing == "") {
//    $('#course_timing_error').html('Please enter course timing');
//    $('#course_timing').focus();
//    return false;
//    }else{
//    $('#course_timing_error').html('');
//    }
//    if (course_fee == "") {
//    $('#course_fee_error').html('Please enter course duration');
//    $('#course_fee').focus();
//    return false;
//    }else{
//    $('#course_fee_error').html('');
//    }
//    if (course_duration == "") {
//    $('#course_duration_error').html('Please enter course duration');
//    $('#course_duration').focus();
//    return false;
//    }else{
//    $('#course_duration_error').html('');
//    }
//    if (course_benfite == "") {
//    $('#course_benfite_error').html('Please write course benfite');
//    $('#course_benfite').focus();
//    return false;
//    }else{
//    $('#course_benfite_error').html('');
//    }
//    if (certificate_info == "") {
//    $('#certificate_info_error').html('Please write certificate info');
//    $('#certificate_info').focus();
//    return false;
//    }else{
//    $('#certificate_info_error').html('');
//    }
//    if (description == "") {
//    $('#description_error').html('Please write description');
//    $('#description').focus();
//    return false;
//    }else{
//    $('#description_error').html('');
//    }
//    $.ajaxSetup({
//        headers: {
//            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//        }
//    });
//       var url=APP_URL+'/save-course';

//    $.ajax({
//      url: url,
//      type: 'post',      
//      processData: false,
//      contentType: false,
//      data: new FormData($('#courceform')[0]),
//          dataType:"json",
//          beforeSend: function() {
//        $("#overlay").fadeIn(300)
//            },
//      success: function(response) {
//         if(response.success==true){
//          alertify.success(response.message);
//        }
//      },
//      }).done(function() {
//        setTimeout(function(){
//        $("#overlay").fadeOut(300);
//        },500);
//      }); 
//  })

// $('body').on('click','#editCource',function(event){
//   event.preventDefault()
//   var cource_category = $('#cource_category').val();
//   var cource_name = $('#cource_name').val();
//   var course_session = $('#course_session').val();
//   var course_date = $('#course_date').val();
//   var course_timing = $('#course_timing').val();
//   var course_duration = $('#course_duration').val();
//   var course_fee = $('#course_fee').val();
//   var course_benfite = $('#course_benfite').val();
//   var certificate_info = $('#certificate_info').val();
//   var description = $('#description').val();

//   if (cource_category == "") {
//   $('#cource_category_error').html('Please select course category');
//   $('#cource_category').focus();
//   return false;
//   }else{
//   $('#cource_category_error').html('');
//   }
//   if (cource_name == "") {
//   $('#c_cource_name_error').html('Please write course name');
//   $('#cource_name').focus();
//   return false;
//   }else{
//   $('#c_cource_name_error').html('');
//   }
//   if (course_session == "") {
//   $('#course_session_error').html('Please write course session');
//   $('#course_session').focus();
//   return false;
//   }else{
//   $('#course_session_error').html('');
//   }
//   if (course_date == "") {
//   $('#course_date_error').html('Please select course date');
//   $('#course_date').focus();
//   return false;
//   }else{
//   $('#course_date_error').html('');
//   }
//   if (course_timing == "") {
//   $('#course_timing_error').html('Please enter course timing');
//   $('#course_timing').focus();
//   return false;
//   }else{
//   $('#course_timing_error').html('');
//   }
//   if (course_fee == "") {
//   $('#course_fee_error').html('Please enter course duration');
//   $('#course_fee').focus();
//   return false;
//   }else{
//   $('#course_fee_error').html('');
//   }
//   if (course_duration == "") {
//   $('#course_duration_error').html('Please enter course duration');
//   $('#course_duration').focus();
//   return false;
//   }else{
//   $('#course_duration_error').html('');
//   }
//   if (course_benfite == "") {
//   $('#course_benfite_error').html('Please write course benfite');
//   $('#course_benfite').focus();
//   return false;
//   }else{
//   $('#course_benfite_error').html('');
//   }
//   if (certificate_info == "") {
//   $('#certificate_info_error').html('Please write certificate info');
//   $('#certificate_info').focus();
//   return false;
//   }else{
//   $('#certificate_info_error').html('');
//   }
//   if (description == "") {
//   $('#description_error').html('Please write description');
//   $('#description').focus();
//   return false;
//   }else{
//   $('#description_error').html('');
//   }
//   $.ajaxSetup({
//       headers: {
//           'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//       }
//   });
//      var url=APP_URL+'/update-course';
//    var thi=$(this);
//   $.ajax({
//     url: url,
//     type: 'post',
//     data: $('#courceeditform').serialize(),
//         dataType:"json",
//         beforeSend: function() {
//       $("#overlay").fadeIn(300)
//           },
//     success: function(response) {
//        if(response.success==true){
//         alertify.success(response.message);
//       }
//     },
//     }).done(function() {
//       setTimeout(function(){
//       $("#overlay").fadeOut(300);
//       },500);
//     }); 
// })

//  $('body').on('click','#courcechangestatus',function(){ 
//   var id  = $(this).attr("data-id");
//   var datavalue  = $(this).attr("data-value");
//    swal({
//      title: "Change status",
//      text: "Please ensure and then confirm!",
//      type: "warning",
//      showCancelButton: !0,
//      confirmButtonText: "Yes!",
//      cancelButtonText: "No, cancel!",
//      reverseButtons: !0
//    }).then(function (e) {
//    if (e.value === true) {
//        $.ajaxSetup({
//          headers: {
//              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//          }
//         });
//          var url=APP_URL+'/course-change-status';

//          $.ajax({
//             type:'POST',
//             url:url,
//             data:{id:id,id1:datavalue},
//             dataType: "json",
//             beforeSend: function() {
//                // $("#overlay").fadeIn(300)
//              },
//             success:function(data){
//             if(data.success==true){
//                alertify.success(data.message);
//                if(datavalue=='1'){
//                  $('.changestatuscls-'+id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
//                  $('.changestatuscls-'+id).html('Active');
//                }else{
//                  $('.changestatuscls-'+id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
//                  $('.changestatuscls-'+id).html('Deactive');
//                }
//             }else{
//                alertify.error(data.message);
//             }

//             },
//          }).done(function() {
//                 setTimeout(function(){
//                      // $("#overlay").fadeOut(300);
//                      },500);

//           }); 

//      }
//      else {
//    e.dismiss;
//    }
//    }, function (dismiss) {
//    return false;
//    })
// })


// $('body').on('click','.cource_delete_data',function(){ 
//    var id  = $(this).attr("data-id");
//    currentRow = $(this);
//     swal({
//     title: "Delete?",
//     text: "Please ensure and then confirm!",
//     type: "warning",
//     showCancelButton: !0,
//     confirmButtonText: "Yes, delete it!",
//     cancelButtonText: "No, cancel!",
//     reverseButtons: !0
//     }).then(function (e) {
//     if (e.value === true) {
//         $.ajaxSetup({
//             headers: {
//                 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//             }
//           });
//           var url=APP_URL+'/deletecourse';
//           $.ajax({
//              type:'POST',
//              url:url,
//              data:{id:id},
//              dataType: "json",
//              beforeSend: function() {
//                $(".loader").removeClass('d-none');
//               },
//              success:function(data){
//               currentRow.parents('tr').remove();
//                $(".loader").addClass('d-none');
//              if(data.success==true){
//                 alertify.success(data.message);
//              }else{
//                 alertify.error(data.message);
//              }

//              },
//           }).done(function() {
//               setTimeout(function(){
//                   $(".loader").addClass('d-none');
//               },500);
//            }); 
//       }
//       else {
//     e.dismiss;
//     }
//     }, function (dismiss) {
//     return false;
//     })
//  })

$(document).ready(function () {
  $('body').on('click', '.addmorestudentBtn', function () {
    let i = $(".course_details").length + 1
    $.ajax({
      url: APP_URL + '/add-more-student-form/' + i,
      type: 'get',
      success: function (data) {
        $("#addstudentapend").append(data);
      },
    })
  })
})

$(document).on('click', '.removeaddstudent', function () {
  var button_id = $(this).attr("id");
  $('#row_student_course_form_' + button_id + '').remove();
});


$('body').on('click', '#addStudent', function (event) {
  event.preventDefault()
  var first_name = $('#first_name').val();
  var last_name = $('#last_name').val();
  var email = $('#email').val();
  var email = $('#email').val();
  var about = $('#about').val();
  var dob = $('#dob').val();

  if (first_name == "") {
    $('#name_error').html('Please write first name');
    $('#first_name').focus();
    return false;
  } else {
    $('#name_error').html('');
  }
  if (last_name == "") {
    $('#p_last_name_error').html('Please write last name');
    $('#last_name').focus();
    return false;
  } else {
    $('#p_last_name_error').html('');
  }
  if (document.getElementById("Male").checked || document.getElementById("Female").checked || document.getElementById("Other").checked) {
    $('#p_gender_error').html('');
  } else {
    $('#p_gender_error').html('Please select gender');
    return false;
  }
  if (dob == "") {
    $('#dob_error').html('Please enter your date of birth!!');
    $('#dob').focus();
    return false;
  } else {
    $('#dob_error').html('');
  }
  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var email = emailfilter.test($("#email").val());
  (email);
  if (email == false) {
    $('#email_error').html('Please enter a valid email');
    $("#email").focus();
    return false;
  } else {
    $('#email_error').html('');
  }
  if (email == "") {
    $('#email_error').html('Please write email name');
    $('#email').focus();
    return false;
  } else {
    $('#email_error').html('');
  }
  var password = document.getElementById('password').value;
  if (password.length == "") {
    $('#pwd_error').html('Please Enter Your New Password');
    document.getElementById('password').value = "";
    document.getElementById('password').focus();
    return false;
  } else {
    $('#pwd_error').html('');
  }
  var repassword = document.getElementById('repassword').value;
  if (repassword.length == "") {
    $('#repwd_error').html('Please Enter Your Confirm Password');
    document.getElementById('repassword').value = "";
    document.getElementById('repassword').focus();
    return false;
  } else {
    $('#repwd_error').html('');
  }
  if (password != repassword) {
    $('#repwd_error').html('The password and confirm password do not match!!');
    document.getElementById('repassword').value = "";
    document.getElementById('repassword').focus();
    return false;
  } else {
    $('#repwd_error').html('');
  }
  if (about == "") {
    $('#about_you_error').html('Please write about you');
    $('#about').focus();
    return false;
  } else {
    $('#about_you_error').html('');
  }
  var cttp = true;
  $.each($(".course_details"), function () {
    var course_name = $(this).find('#course_name').val();
    if (course_name == "") {
      $(this).find('.couse_name_error').html('Please select course');
      $(this).find('#course_name').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('.couse_name_error').html(' ');
    }

    var course_type = $(this).find('#course_type').val();
    if (course_type == "") {
      $(this).find('.course_type_error').html('Please select course type');
      $(this).find('#course_type').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('.course_type_error').html(' ');
    }

    var duration = $(this).find('#duration').val();
    if (duration == "") {
      $(this).find('#duration_error').html('Please select course type');
      $(this).find('#duration').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#duration_error').html(' ');
    }

    var session = $(this).find('#session').val();
    if (session == "") {
      $(this).find('#session_error').html('Please select course session');
      $(this).find('#session').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#session_error').html(' ');
    }
    var startdate = $(this).find('#startdate').val();
    if (startdate == "") {
      $(this).find('#startdate_error').html('Please select course session');
      $(this).find('#startdate').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#startdate_error').html(' ');
    }
    var timing = $(this).find('#timing').val();
    if (timing == "") {
      $(this).find('#timing_error').html('Please select timing');
      $(this).find('#timing').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#timing_error').html(' ');
    }
  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/save-student';

  var formData = new FormData($("#addStudentform")[0]);

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    contentType: false,
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        $('#addStudentform')[0].reset();
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });
})

$(document).on('change', '.editstudentprofileupload', function (e) {

  var th = $(this);
  var url = APP_URL + '/student-profilepic-update';

  $.ajax({
    type: 'POST',
    url: url,
    data: new FormData($('#studentprofileuploadId')[0]),
    processData: false,
    contentType: false,
    dataType: 'json',
    success: function (data) {
      alertify.success(data.message);
      $('#imagePreview').attr("src", APP_URL + '/public/profile/' + data.img);
      $('#trainerprofupload').attr("value", data.img);
    }
  })
})

$('body').on('click', '#updateStudentinfo', function (event) {
  event.preventDefault()
  var first_name = $('#first_name').val();
  var last_name = $('#last_name').val();
  var email = $('#email').val();
  var email = $('#email').val();
  var about = $('#about').val();
  var dob = $('#dob').val();

  if (first_name == "") {
    $('#name_error').html('Please write first name');
    $('#first_name').focus();
    return false;
  } else {
    $('#name_error').html('');
  }
  if (last_name == "") {
    $('#p_last_name_error').html('Please write last name');
    $('#last_name').focus();
    return false;
  } else {
    $('#p_last_name_error').html('');
  }
  if (document.getElementById("Male").checked || document.getElementById("Female").checked || document.getElementById("Other").checked) {
    $('#p_gender_error').html('');
  } else {
    $('#p_gender_error').html('Please select gender');
    return false;
  }
  if (dob == "") {
    $('#dob_error').html('Please enter your date of birth!!');
    $('#dob').focus();
    return false;
  } else {
    $('#dob_error').html('');
  }
  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var email = emailfilter.test($("#email").val());
  (email);
  if (email == false) {
    $('#email_error').html('Please enter a valid email');
    $("#email").focus();
    return false;
  } else {
    $('#email_error').html('');
  }
  if (email == "") {
    $('#email_error').html('Please write email name');
    $('#email').focus();
    return false;
  } else {
    $('#email_error').html('');
  }
  if (about == "") {
    $('#about_you_error').html('Please write about you');
    $('#about').focus();
    return false;
  } else {
    $('#about_you_error').html('');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/updatestudentinfo';
  var formData = $('#editstudentform').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#studentcoursedetails', function (event) {
  event.preventDefault()
  var cttp = true;
  $.each($(".course_details_student"), function () {
    var course_name = $(this).find('#course_name').val();
    if (course_name == "") {
      $(this).find('.couse_name_error').html('Please select course');
      $(this).find('#course_name').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('.couse_name_error').html(' ');
    }
    var course_type = $(this).find('#course_type').val();
    if (course_type == "") {
      $(this).find('#course_type_error').html('Please select course type');
      $(this).find('#course_type').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#course_type_error').html(' ');
    }
    var duration = $(this).find('#duration').val();
    if (duration == "") {
      $(this).find('#duration_error').html('Please select course type');
      $(this).find('#duration').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#duration_error').html(' ');
    }
    var session = $(this).find('#session').val();
    if (session == "") {
      $(this).find('#session_error').html('Please select course session');
      $(this).find('#session').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#session_error').html(' ');
    }
    var startdate = $(this).find('#startdate').val();
    if (startdate == "") {
      $(this).find('#startdate_error').html('Please select course session');
      $(this).find('#startdate').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#startdate_error').html(' ');
    }
    var timing = $(this).find('#timing').val();
    if (timing == "") {
      $(this).find('#timing_error').html('Please select timing');
      $(this).find('#timing').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('#timing_error').html(' ');
    }
  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/updatestudentcoursedetails';
  var formData = $('#studentcourseform').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });
})

$(document).on('click', '.deletestudent', function () {
  var id = $(this).data("id"); // will return the number 123
  var url = APP_URL + '/delete-sudent/' + id;
  if (confirm('Are you sure to remove this record ?')) {
    $.ajax({
      type: 'get',
      url: url,
      processData: false,
      contentType: false,
      dataType: 'json',
      beforeSend: function () {
        $("#overlay").fadeIn(300)
      },
      success: function (response) {
        if (response.success == true) {
          $('#' + id).remove();
          alertify.success(response.message);
        }
      },
    }).done(function () {
      setTimeout(function () {
        $("#overlay").fadeOut(300);
      }, 500);
    });
  }
});

$('body').on('click', '.studentstatuschange', function () {
  var id = $(this).data("id"); // will return the number 123
  // var datavalue  = $(this).attr("data-value");
  swal({
    title: "Change status",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes!",
    cancelButtonText: "No, cancel!",
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      var url = APP_URL + '/change-status-student/' + id;

      $.ajax({
        type: 'get',
        url: url,
        dataType: "json",
        beforeSend: function () {
          // $("#overlay").fadeIn(300)
        },
        success: function (data) {
          if (data.success == true) {
            alertify.success(data.message);
            if (datavalue == '1') {
              $('.changestatuscls-' + id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
              $('.changestatuscls-' + id).html('Active');
            } else {
              $('.changestatuscls-' + id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
              $('.changestatuscls-' + id).html('Deactive');
            }
          } else {
            alertify.error(data.message);
          }

        },
      }).done(function () {
        setTimeout(function () {
          // $("#overlay").fadeOut(300);
        }, 500);

      });

    }
    else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
})

$(document).on('change', '.clientprofileImgUploads', function (e) {
  var th = $(this);
  var url = APP_URL + '/clientupdate_profile_picture';

  $.ajax({
    type: 'POST',
    url: url,
    data: new FormData($('#clientprofileuploadc')[0]),
    processData: false,
    contentType: false,
    dataType: 'json',
    success: function (data) {
      alertify.success(data.message);
      $('#imagePreview').attr("src", APP_URL + '/public/profile/' + data.img);
      $('#profileImgUploads').attr("value", data.img);
    }
  })
})

$('body').on('click', '#clientpersonalDetailsSaveBtn', function () {
  var first_name = $('#pr_first_name').val();
  var last_name = $('#pr_last_name').val();
  var about_you = $('#about').val();
  var dob = $('#dob').val();

  if (first_name == "") {
    $('#p_first_name_error').html('Please enter first name');
    $('#pr_first_name').focus();
    return false;
  } else {
    $('#p_first_name_error').html('');
  }
  if (!/^[a-z A-Z]*$/g.test($('#pr_first_name').val())) {
    $('#p_first_name_error').html('Allow only characters!!');
    $('#pr_first_name').focus();
    return false;
  } else {
    $('#p_first_name_error').html('');
  }

  if (last_name == "") {
    $('#p_last_name_error').html('Please enter last name');
    $('#pr_last_name').focus();
    return false;
  } else {
    $('#p_last_name_error').html('');

  }
  if (!/^[a-z A-Z]*$/g.test($('#pr_last_name').val())) {
    $('#p_last_name_error').html('Allow only characters!!');
    $('#pr_last_name').focus();
    return false;
  } else {
    $('#p_last_name_error').html('');
  }
  if ($('input[type=radio][name=gender]:checked').length == 0) {
    $('#p_gender_error').html('Please select gender!!');

    return false;
  } else {
    $('#p_gender_error').html(' ');
  }

  if (dob == "") {
    $('#p_dob_error').html('Please enter your date of birth!!');
    $('#dob').focus();
    return false;
  } else {
    $('#p_dob_error').html('');
  }

  if (about_you == "") {
    $('#about_you_error').html('Please enter about');
    $('#about').focus();
    return false;
  } else {
    $('#about_you_error').html(' ');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/clientpersonalInfo';
  var formData = $('#client_persnal_details').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#clientsaveotherdetails', function () {

  var email = $('#email').val();
  var umobile = $("#MobPhone").val();
  var gender = $("#Facebooklink").val();
  var staticLinkedIn = $("#LinkedInlink").val();
  var staticTwitter = $("#Twitterlink").val();
  var fburl = document.getElementById("Facebooklink").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (fburl != "") {
    if (!regexp.test(fburl)) {
      $('#EditableFacebook_error').html('Please enter valid url.');
      document.getElementById('Facebooklink').value = "";
      document.getElementById('Facebooklink').focus();
      return false;
    } else {
      $('#EditableFacebook_error').html('');
    }
  }
  var linurl = document.getElementById("LinkedInlink").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (linurl != "") {
    if (!regexp.test(linurl)) {
      $('#editableLinkedIn_error').html('Please enter valid url.');
      document.getElementById('LinkedInlink').value = "";
      document.getElementById('LinkedInlink').focus();
      return false;
    } else {
      $('#editableLinkedIn_error').html('');
    }
  }
  var twiturl = document.getElementById("Twitterlink").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (twiturl != "") {
    if (!regexp.test(twiturl)) {
      $('#editableTwitter_error').html('Please enter valid url.');
      document.getElementById('Twitterlink').value = "";
      document.getElementById('Twitterlink').focus();
      return false;
    } else {
      $('#editableTwitter_error').html('');
    }
  }

  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var regemailid = emailfilter.test($("#email").val());
  (regemailid);
  if (regemailid == false) {
    $('#email_error').html('Please enter a valid email');
    $("#email").focus();
    return false;
  } else {
    $('#email_error').html('');
  }

  if (umobile == "") {
    $('#editableMobilePhone_error').html('Please enter the mobile number');
    $("#editableMobilePhone").focus();
    return false;
  } else {
    $('#editableMobilePhone_error').html('');
  }
  if ((umobile.length < 12) || (umobile.length > 12)) {
    $('#editableMobilePhone_error').html('Your mobile number must be 1 to 10 Integers');

    $("#editableMobilePhone").focus();
    return false;
  } else {
    $('#editableMobilePhone_error').html('');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/client-update-contact';
  var formData = $('#client_otherdetails').serialize();

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    // contentType: false, 
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})
$('body').on('click', '#clientexpiranceinfoSaveBtn', function (event) {
  event.preventDefault()
  var cttp = true;
  $.each($(".experience_row"), function () {
    var company_name_info = $(this).find('.company_name_info').val();
    if (company_name_info == "") {
      // $(this).find('.current_designation_error').html('Please Enter Designation');    
      $(this).find('.company_name_info').focus();
      cttp = false;
      return false;
    } else {
      $(this).find('.company_name_info_error').html(' ');
    }

    var current_designation = $(this).find('.current_designation').val();
    if (current_designation == "") {
      // $(this).find('.current_designation_error').html('Please Enter Designation');    
      $(this).find('.current_designation').focus();
      cttp = false;
      return false;
    } else {
      // $(this).find('.company_name_info_error').html(' ');  
    }

    var joining_date = $(this).find('.joining_date').val();
    if (joining_date == "") {
      // $(this).find('.current_designation_error').html('Please Enter Designation');    
      $(this).find('.joining_date').focus();
      cttp = false;
      return false;
    } else {
      // $(this).find('.company_name_info_error').html(' ');  
    }

  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/client-update-expiranceinfo';
  var thi = $(this);
  $.ajax({
    url: url,
    type: 'post',
    data: $('#clientexpiranceinfo').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
});

$('body').on('click', '#clientsaveEducation', function (event) {
  event.preventDefault()
  var cttp = true;
  $.each($(".education_row"), function () {
    var highest_qualification = $(this).find('.highest_qualification').val();
    if (highest_qualification == "") {
      // $(this).find('.current_designation_error').html('Please Enter Designation');    
      $(this).find('.highest_qualification').focus();
      cttp = false;
      return false;
    } else {
      // $(this).find('.company_name_info_error').html(' ');  
    }

    var subject = $(this).find('.subject').val();
    if (subject == "") {
      // $(this).find('.subject_error').html('Please Enter Designation');    
      $(this).find('.subject').focus();
      cttp = false;
      return false;
    } else {
      // $(this).find('.company_name_info_error').html(' ');  
    }

    var joining_date = $(this).find('.joining_date').val();
    if (joining_date == "") {
      // $(this).find('.current_designation_error').html('Please Enter Designation');    
      $(this).find('.joining_date').focus();
      cttp = false;
      return false;
    } else {
      // $(this).find('.company_name_info_error').html(' ');  
    }

  });
  if (cttp === false) {
    return false;
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/client-update-eductionInfo';
  var thi = $(this);
  $.ajax({
    url: url,
    type: 'post',
    data: $('#clienteducationForm').serialize(),
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$('body').on('click', '#addClientSkillsBtnS', function () {

  var skills = $('#addClientSkillsText').val();

  if (skills == "") {
    $('#skills_error').html('Please Select skill');
    $('$addSkillsText').focus();
    return false;
  } else {
    $('#skills_error').html(' ');
    $('#addSkillsText').val(' ')
    $(`<div class="chip" tabindex="-1">
      <span>
      ${skills}
      </span>
      <span class="" title="Remove chip" aria-label="Remove chip" type="button" onclick="$(this).parent().remove()">
      <i class="fas fa-fw fa-times-circle align-middle text-danger ml-1"></i>
      </span>
      </div>`).appendTo("#skillsChipsContainer");
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/AddClientSkills';
  $.ajax({
    url: url,
    type: 'post',
    data: { skills: skills },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$('body').on('click', '.RemoveClientSkills', function () {

  var id = $(this).attr("data-id");
  $(this).parent().remove();
  // alert(id);
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/RemoveClientSkills';

  $.ajax({
    url: url,
    type: 'post',
    data: { id: id },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

// chang password 

$('body').on('click', '#institutechangepass', function () {
  var old_password = document.getElementById('old_password').value;
  if (old_password.length == "") {
    $('.old_password_error').html('Please Enter Your Old password');
    document.getElementById('old_password').value = "";
    document.getElementById('old_password').focus();
    return false;
  } else {
    $('.old_password_error').html('');
    $('#old_password').css('border-color', '#495057');
  }

  var new_password = document.getElementById('new_password').value;
  if (new_password.length == "") {
    $('.new_password_error').html('Please Enter Your New Password');
    document.getElementById('new_password').value = "";
    document.getElementById('new_password').focus();
    return false;
  } else {
    $('.new_password_error').html('');
    $('#new_password').css('border-color', '#495057');
  }

  var confirm_password = document.getElementById('confirm_password').value;
  if (confirm_password.length == "") {
    $('.confirm_password_error').html('Please Enter Your Confirm Password');
    document.getElementById('confirm_password').value = "";
    document.getElementById('confirm_password').focus();
    return false;
  } else {
    $('.confirm_password_error').html('');
    $('#confirm_password').css('border-color', '#495057');
  }

  if (new_password != confirm_password) {
    $('.confirm_password_error').html('The password and confirm password do not match!!');
    document.getElementById('confirm_password').value = "";
    document.getElementById('confirm_password').focus();
    return false;
  } else {
    $('.confirm_password_error').html('');
    $('#confirm_password').css('border-color', '#495057');
  }


  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/updateChangePasswordforinstute';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#instutechangepwdform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // $("#changepwdform").reset();
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    $(".loader").addClass('d-none');
  });

})

//  $(document).on('change','.courseImageUpload',function(e){
//          var th=$(this);
//          var url=APP_URL+'/course-update-image';

//           $.ajax({
//              type:'POST',
//              url:url,
//              data: new FormData($('#courseimageform')[0]),
//              processData: false,
//              contentType: false, 
//              dataType:'json',
//             success:function(data){   
//                 alertify.success(data.message);
//              $('#imagePreview').attr("src", APP_URL+'/public/profile/'+data.img);
//              $('#trainerprofupload').attr("value", data.img);
//            }
//           })

// })

$(document).on('click', '#addInstituteimagegallery', function (e) {
  if (document.getElementById("image").files.length == 0) {
    $('#image_error').html('Please select course image');
    $('#image').focus();
    return false;
  } else {
    $('#image_error').html('');
  }
  var th = $(this);
  var url = APP_URL + '/add-institute-image-gallery';

  $.ajax({
    type: 'POST',
    url: url,
    data: new FormData($('#addInstituteimagegalleryform')[0]),
    processData: false,
    contentType: false,
    dataType: 'json',
    success: function (data) {
      alertify.success(data.message);
    }
  })

})

// $('body').on('click','#addactivityname',function(event){
//    event.preventDefault()
//    var activity_category = $('#activity_category').val();
//    var activity_name = $('#activity_name').val();
//    var description =CKEDITOR.instances['description'].getData();

//     if (activity_name == "") {
//    $('#activity_name_error').html('Please write activity name');
//    $('#activity_name').focus();
//    return false;
//    }else{
//    $('#activity_name_error').html('');
//    }

//    if (!activity_category) {
//    $('#activity_category_error').html('Please select activity category');
//    $('#activity_category').focus();
//    return false;
//    }else{
//    $('#activity_category_error').html('');
//    }

//    if (description == "") {
//    $('#description_error').html('Please write description');
//    $('#description').focus();
//    return false;
//    }else{
//    $('#description_error').html('');
//    }
//    for (instance in CKEDITOR.instances) {
//      CKEDITOR.instances[instance].updateElement();
//    }
//    $.ajaxSetup({
//        headers: {
//            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//        }
//    });

//       var url=APP_URL+'/save-activity-name';

//    $.ajax({
//      url: url,
//      type: 'post',      
//      processData: false,
//      contentType: false,
//      data: new FormData($('#activitynameform')[0]),
//          dataType:"json",
//          beforeSend: function() {
//        $("#overlay").fadeIn(300)
//            },
//      success: function(response) {
//         if(response.success==true){
//          $('#activitynameform')[0].reset();
//          alertify.success(response.message);
//        }
//      },
//      }).done(function() {
//        setTimeout(function(){
//        $("#overlay").fadeOut(300);
//        },500);
//      }); 
//  })

//  $('body').on('click','#activitynamechangestatus',function(){ 
//   var id  = $(this).attr("data-id");
//   var datavalue  = $(this).attr("data-value");
//    swal({
//      title: "Change status",
//      text: "Please ensure and then confirm!",
//      type: "warning",
//      showCancelButton: !0,
//      confirmButtonText: "Yes!",
//      cancelButtonText: "No, cancel!",
//      reverseButtons: !0
//    }).then(function (e) {
//    if (e.value === true) {
//        $.ajaxSetup({
//          headers: {
//              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//          }
//         });
//          var url=APP_URL+'/activity-name-change-status';

//          $.ajax({
//             type:'POST',
//             url:url,
//             data:{id:id,id1:datavalue},
//             dataType: "json",
//             beforeSend: function() {
//                // $("#overlay").fadeIn(300)
//              },
//             success:function(data){
//             if(data.success==true){
//                alertify.success(data.message);
//                if(datavalue=='1'){
//                  $('.changestatuscls-'+id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
//                  $('.changestatuscls-'+id).html('Active');
//                }else{
//                  $('.changestatuscls-'+id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
//                  $('.changestatuscls-'+id).html('Deactive');
//                }
//             }else{
//                alertify.error(data.message);
//             }

//             },
//          }).done(function() {
//                 setTimeout(function(){
//                      // $("#overlay").fadeOut(300);
//                      },500);

//           }); 

//      }
//      else {
//    e.dismiss;
//    }
//    }, function (dismiss) {
//    return false;
//    })
// })

// $('body').on('click','.activity_name_delete_data',function(){ 
//   var id  = $(this).attr("data-id");
//   currentRow = $(this);
//    swal({
//    title: "Delete?",
//    text: "Please ensure and then confirm!",
//    type: "warning",
//    showCancelButton: !0,
//    confirmButtonText: "Yes, delete it!",
//    cancelButtonText: "No, cancel!",
//    reverseButtons: !0
//    }).then(function (e) {
//    if (e.value === true) {
//        $.ajaxSetup({
//            headers: {
//                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//            }
//          });
//          var url=APP_URL+'/delete-active-name';
//          $.ajax({
//             type:'POST',
//             url:url,
//             data:{id:id},
//             dataType: "json",
//             beforeSend: function() {
//               $(".loader").removeClass('d-none');
//              },
//             success:function(data){
//              currentRow.parents('tr').remove();
//               $(".loader").addClass('d-none');
//             if(data.success==true){
//                alertify.success(data.message);
//             }else{
//                alertify.error(data.message);
//             }

//             },
//          }).done(function() {
//              setTimeout(function(){
//                  $(".loader").addClass('d-none');
//              },500);
//           }); 
//      }
//      else {
//    e.dismiss;
//    }
//    }, function (dismiss) {
//    return false;
//    })
// })

// $('body').on('click','#editactivityname',function(event){
//    event.preventDefault()
//    var activity_category = $('#activity_category').val();
//    var activity_name = $('#activity_name').val();

//   if (activity_name == "") {
//    $('#activity_name_error').html('Please write activity name');
//    $('#activity_name').focus();
//    return false;
//    }else{
//    $('#activity_name_error').html('');
//    }

//    if (activity_category == "") {
//    $('#activity_category_error').html('Please select activity category');
//    $('#activity_category').focus();
//    return false;
//    }else{
//    $('#activity_category_error').html('');
//    }


//    var description = CKEDITOR.instances['description'].getData();
//  if (description == "") {
//    $('#description_error').html('Please write description');
//    $('#description').focus();
//    return false;
//    }else{
//    $('#description_error').html('');
//    }

//  for (instance in CKEDITOR.instances) {
//      CKEDITOR.instances[instance].updateElement();
//    }

//    $.ajaxSetup({
//        headers: {
//            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//        }
//    });
//       var url=APP_URL+'/update-activity-name';

//    $.ajax({
//      url: url,
//      type: 'post',      
//      processData: false,
//      contentType: false,
//      data: new FormData($('#editactivitynameform')[0]),
//          dataType:"json",
//          beforeSend: function() {
//        $("#overlay").fadeIn(300)
//            },
//      success: function(response) {
//         if(response.success==true){
//          alertify.success(response.message);
//        }
//      },
//      }).done(function() {
//        setTimeout(function(){
//        $("#overlay").fadeOut(300);
//        },500);
//      }); 
//  })

// $('body').on('click','#addquestions',function(){
//  var activity_name = $('#activity_name').val();
//  var questions = $('#questions').val();
//  var option_one = $('#option_one').val();
//  var option_two = $('#option_two').val();
//  var option_three = $('#option_three').val();
//  var option_four = $('#option_four').val();
//  var option_five = $('#option_five').val();
//  var answer = $('#answer').val();

//  if (activity_name == "") {
//    $('#activity_name_error').html('Please select activity name');
//    $('#activity_name').focus();
//    return false;
//  }else{
//    $('#activity_name_error').html('');
//  }
//  if (questions == "") {
//    $('#questions_error').html('Please write question');
//    $('#questions').focus();
//    return false;
//  }else{
//    $('#questions_error').html('');

//  }    
//  if(option_one== "") {
//    $('#option_one_error').html('Please enter your option one');
//    $('#option_one').focus();
//    return false;
//  }else{
//    $('#option_one_error').html('');
//  }
//  if(option_two==""){
//    $('#option_two_error').html('Please enter your option two');
//    $('#option_two').focus();
//    return false;
//  }else{
//    $('#option_two_error').html(' ');
//  }
//  if(option_three==""){
//    $('#option_three_error').html('Please enter your option three');
//    $('#option_three').focus();
//    return false;
//  }else{
//    $('#option_three_error').html(' ');
//  }
//  if(option_four==""){
//    $('#option_four_error').html('Please enter your option four');
//    $('#option_four').focus();
//    return false;
//  }else{
//    $('#option_four_error').html(' ');
//  }
//  if(option_five==""){
//    $('#option_five_error').html('Please enter your option five');
//    $('#option_five').focus();
//    return false;
//  }else{
//    $('#option_five_error').html(' ');
//  }
//  if(answer==""){
//    $('#answer_error').html('Please select your answer');
//    $('#answer').focus();
//    return false;
//  }else{
//    $('#answer_error').html(' ');
//  }
//  $.ajaxSetup({
//    headers: {
//      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//    }
//  });
//  var url=APP_URL+'/save-question';
//  var formData = $('#questionsform').serialize();

//  $.ajax({
//    url: url,
//    type: 'post',
//    data: formData,
//    processData: false,
//      // contentType: false, 
//      dataType:"json",
//      beforeSend: function() {
//       $("#overlay").fadeIn(300)
//     },
//     success:function(data){
//       if(data.success==true){
//        alertify.success(data.message);
//        $('#questionsform')[0].reset();
//                // window.location.href = data.redirect;
//              }else{
//                alertify.error(data.message);
//              }
//            },
//          }).done(function() {
//            setTimeout(function(){
//              $("#overlay").fadeOut(300);
//            },500);

//          }); 

//        })

//  $('body').on('click','#changeStatusquestion',function(){ 
//    var id  = $(this).attr("data-id");
//    var datavalue  = $(this).attr("data-value");
//    swal({
//     title: "Change status",
//     text: "Please ensure and then confirm!",
//     type: "warning",
//     showCancelButton: !0,
//     confirmButtonText: "Yes!",
//     cancelButtonText: "No, cancel!",
//     reverseButtons: !0
//   }).then(function (e) {
//     if (e.value === true) {
//       $.ajaxSetup({
//         headers: {
//           'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//         }
//       });
//       var url=APP_URL+'/change-question-status';

//       $.ajax({
//        type:'POST',
//        url:url,
//        data:{id:id,id1:datavalue},
//        dataType: "json",
//        beforeSend: function() {
//                 // $("#overlay").fadeIn(300)
//               },
//               success:function(data){
//                if(data.success==true){
//                 alertify.success(data.message);
//                 if(datavalue=='1'){
//                   $('.activityChangeTheory-'+id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
//                   $('.activityChangeTheory-'+id).html('Active');
//                 }else{
//                   $('.activityChangeTheory-'+id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
//                   $('.activityChangeTheory-'+id).html('Deactive');
//                 }
//               }else{
//                 alertify.error(data.message);
//               }

//             },
//           }).done(function() {
//            setTimeout(function(){
//                       // $("#overlay").fadeOut(300);
//                     },500);

//          }); 

//         }
//         else {
//           e.dismiss;
//         }
//       }, function (dismiss) {
//         return false;
//       })
// })


//  $('body').on('click','.delete_data_question',function(){ 
//    var id  = $(this).attr("data-id");
//    currentRow = $(this);
//    swal({
//     title: "Delete?",
//     text: "Please ensure and then confirm!",
//     type: "warning",
//     showCancelButton: !0,
//     confirmButtonText: "Yes, delete it!",
//     cancelButtonText: "No, cancel!",
//     reverseButtons: !0
//   }).then(function (e) {
//     if (e.value === true) {
//       $.ajaxSetup({
//         headers: {
//           'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//         }
//       });
//       var url=APP_URL+'/delete-questions';
//       $.ajax({
//        type:'POST',
//        url:url,
//        data:{id:id},
//        dataType: "json",
//        beforeSend: function() {
//          $(".loader").removeClass('d-none');
//        },
//        success:function(data){
//         currentRow.parents('tr').remove();
//         $(".loader").addClass('d-none');
//         if(data.success==true){
//           alertify.success(data.message);
//         }else{
//           alertify.error(data.message);
//         }

//       },
//     }).done(function() {
//       setTimeout(function(){
//         $(".loader").addClass('d-none');
//       },500);
//     }); 
//   }
//   else {
//     e.dismiss;
//   }
// }, function (dismiss) {
//   return false;
// })
// })

// $('body').on('click','#updatequestions',function(){
//  var activity_name = $('#activity_name').val();
//  var questions = $('#questions').val();
//  var option_one = $('#option_one').val();
//  var option_two = $('#option_two').val();
//  var option_three = $('#option_three').val();
//  var option_four = $('#option_four').val();
//  var option_five = $('#option_five').val();
//  var answer = $('#answer').val();

//  if (activity_name == "") {
//    $('#activity_name_error').html('Please select activity name');
//    $('#activity_name').focus();
//    return false;
//  }else{
//    $('#activity_name_error').html('');
//  }
//  if (questions == "") {
//    $('#questions_error').html('Please write question');
//    $('#questions').focus();
//    return false;
//  }else{
//    $('#questions_error').html('');

//  }    
//  if(option_one== "") {
//    $('#option_one_error').html('Please enter your option one');
//    $('#option_one').focus();
//    return false;
//  }else{
//    $('#option_one_error').html('');
//  }
//  if(option_two==""){
//    $('#option_two_error').html('Please enter your option two');
//    $('#option_two').focus();
//    return false;
//  }else{
//    $('#option_two_error').html(' ');
//  }
//  if(option_three==""){
//    $('#option_three_error').html('Please enter your option three');
//    $('#option_three').focus();
//    return false;
//  }else{
//    $('#option_three_error').html(' ');
//  }
//  if(option_four==""){
//    $('#option_four_error').html('Please enter your option four');
//    $('#option_four').focus();
//    return false;
//  }else{
//    $('#option_four_error').html(' ');
//  }
//  if(option_five==""){
//    $('#option_five_error').html('Please enter your option five');
//    $('#option_five').focus();
//    return false;
//  }else{
//    $('#option_five_error').html(' ');
//  }
//  if(answer==""){
//    $('#answer_error').html('Please select your answer');
//    $('#answer').focus();
//    return false;
//  }else{
//    $('#answer_error').html(' ');
//  }
//  $.ajaxSetup({
//    headers: {
//      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//    }
//  });
//  var url=APP_URL+'/update-question';
//  var formData = $('#updatequestionsform').serialize();

//  $.ajax({
//    url: url,
//    type: 'post',
//    data: formData,
//    processData: false,
//      // contentType: false, 
//      dataType:"json",
//      beforeSend: function() {
//       $("#overlay").fadeIn(300)
//     },
//     success:function(data){
//       if(data.success==true){
//        alertify.success(data.message);
//                // window.location.href = data.redirect;
//              }else{
//                alertify.error(data.message);
//              }
//            },
//          }).done(function() {
//            setTimeout(function(){
//              $("#overlay").fadeOut(300);
//            },500);

//          }); 

//        })

//  $(document).on('change','.activitynameimage',function(e){
//          var th=$(this);
//          var url=APP_URL+'/activity-update-image';

//           $.ajax({
//              type:'POST',
//              url:url,
//              data: new FormData($('#activitynameimageform')[0]),
//              processData: false,
//              contentType: false, 
//              dataType:'json',
//             success:function(data){   
//                 alertify.success(data.message);
//              $('#imagePreview').attr("src", APP_URL+'/public/profile/'+data.img);
//              $('#trainerprofupload').attr("value", data.img);
//            }
//           })

// })


$("#addnews").click(function () {
  // if( document.getElementById("newsimage").files.length == 0 ){
  //   $('#newsimage_error').html('Please select news image');
  //   $('#newsimage').focus();
  //   return false;
  //   }else{
  //     $('#newsimage_error').html('');
  //   }
  var news_title = $('#news_title').val();
  if (news_title == "") {
    $('#news_title_error').html('Please enter news title');
    $('#news_title').val('');
    $('#news_title').focus();
    return false;

  } else {
    $('#news_title_error').html('');
  }
  var sourse_link = document.getElementById("sourse_link").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (sourse_link != "") {
    if (!regexp.test(sourse_link)) {
      $('#sourse_link_error').html('Please enter valid url.');
      document.getElementById('sourse_link').value = "";
      document.getElementById('sourse_link').focus();
      return false;
    } else {
      $('#sourse_link_error').html('');
    }
  }
  // var name2 =$('#about_lesson').val();
  var name2 = CKEDITOR.instances['news_descriptions'].getData();
  if (name2 == "") {
    $('#news_descriptions_error').html('Please enter news descriptions');
    $('#news_descriptions').val('');
    $('#news_descriptions').focus();
    return false;

  } else {
    $('#news_descriptions_error').html('');
  }



  var newstag = $('#newstag').val();
  if (!newstag) {

    $('#tags_error').html('Please enter tags');
    $('#newstag').val('');
    $('#newstag').focus();
    return false;
  } else {
    $('#tags_error').html(' ');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  for (instance in CKEDITOR.instances) {
    CKEDITOR.instances[instance].updateElement();
  }
  var url = APP_URL + '/save-news';
  $.ajax({
    type: 'POST',
    url: url,
    data: new FormData($('#add_news_form')[0]),
    processData: false,
    contentType: false,
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        alertify.success(data.message);
        $('#add_news_form')[0].reset();
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }

    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });

});

$('body').on('click', '#addNewstags', function (event) {
  var i = 1;
  event.preventDefault()
  var newstag = document.getElementById("newstag").value;

  i++;
  $('#dynamic_tag').append(`<div class="input-group mt-2" id="row` + i + `">
  <input type="text" class="form-control" value="`+ newstag + `" id="newstag" name="newstag[]" placeholder="News tags">
  <div class="input-group-append">
  <button type="button" name="remove" id="`+ i + `" class="btn btn-danger removeTag">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2 align-middle"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
  </button>
  </div><br>
  <span class="err" id="tags_error"></span></div>`);
  document.getElementById('newstag').value = '';

})

$(document).on('click', '.removeTag', function () {
  var button_id = $(this).attr("id");
  $('#row' + button_id + '').remove();
});


$('body').on('click', '.news_delete_data', function () {
  var id = $(this).attr("data-id");
  currentRow = $(this);
  swal({
    title: "Delete?",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes, delete it!",
    cancelButtonText: "No, cancel!",
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      var url = APP_URL + '/delete-news';
      $.ajax({
        type: 'POST',
        url: url,
        data: { id: id },
        dataType: "json",
        beforeSend: function () {
          $(".loader").removeClass('d-none');
        },
        success: function (data) {
          currentRow.parents('tr').remove();
          $(".loader").addClass('d-none');
          if (data.success == true) {
            alertify.success(data.message);
          } else {
            alertify.error(data.message);
          }

        },
      }).done(function () {
        setTimeout(function () {
          $(".loader").addClass('d-none');
        }, 500);
      });
    }
    else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
})

$("#editnews").click(function (event) {
  event.preventDefault()
  var news_title = $('#news_title').val();
  if (news_title == "") {
    $('#news_title_error').html('Please enter news title');
    $('#news_title').val('');
    $('#news_title').focus();
    return false;

  } else {
    $('#news_title_error').html('');
  }
  var sourse_link = document.getElementById("sourse_link").value;
  var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
  if (sourse_link != "") {
    if (!regexp.test(sourse_link)) {
      $('#sourse_link_error').html('Please enter valid url.');
      document.getElementById('sourse_link').value = "";
      document.getElementById('sourse_link').focus();
      return false;
    } else {
      $('#sourse_link_error').html('');
    }
  }
  // var name2 =$('#about_lesson').val();
  var name2 = CKEDITOR.instances['news_descriptions'].getData();
  if (name2 == "") {
    $('#news_descriptions_error').html('Please enter news descriptions');
    $('#news_descriptions').val('');
    $('#news_descriptions').focus();
    return false;

  } else {
    $('#news_descriptions_error').html('');
  }
  var newstag = $('#newstag').val();
  if (newstag == "") {
    $('#newstag_error').html('Please enter news tags');
    $('#newstag').val('');
    $('#newstag').focus();
    return false;

  } else {
    $('#newstag_error').html('');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  for (instance in CKEDITOR.instances) {
    CKEDITOR.instances[instance].updateElement();
  }
  var url = APP_URL + '/update-news';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#editnewsform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        alertify.success(data.message);
        $('#add_news_form')[0].reset();
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }

    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });

});

$(document).on('change', '.newsimageupdate', function (e) {
  var th = $(this);
  var url = APP_URL + '/news-update-image';

  $.ajax({
    type: 'POST',
    url: url,
    data: new FormData($('#newsimageupdateform')[0]),
    processData: false,
    contentType: false,
    dataType: 'json',
    success: function (data) {
      alertify.success(data.message);
      $('#imagePreview').attr("src", APP_URL + '/public/profile/' + data.img);
      $('#trainerprofupload').attr("value", data.img);
    }
  })

})


$('body').on('click', '#addtagbutton', function () {

  var newstag = $('#newstagId').val();
  var news_id = $(this).attr("data-id") // will return the string "123"
  // alert(id);
  if (!newstag) {
    $('#tagerror_error').html('Please Select Tag');
    $('#newstag').focus();
    return false;
  } else {
    $('#tagerror_error').html(' ');
    $('#newstag').val(' ')
    $(`<div class="chip" tabindex="-1">
        <span>
        ${newstag}
        </span>
        <span class="" title="Remove chip" aria-label="Remove chip" type="button" onclick="$(this).parent().remove()">
        <i class="fas fa-fw fa-times-circle align-middle text-danger ml-1"></i>
        </span>
        </div>`).appendTo("#tagsChips");
  }


  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/addNewstags';
  $.ajax({
    url: url,
    type: 'post',
    data: { newstag: newstag, news_id: news_id },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

$('body').on('click', '.RemoveTagsnews', function () {

  var id = $(this).attr("data-id");
  var tag = $(this).attr("data-tag");
  $(this).parent().remove();
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/remove-news-tag';

  $.ajax({
    url: url,
    type: 'post',
    data: { id, tag },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})


$(".resetpasswordbtn").click(function (event) {
  event.preventDefault()
  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var regemailid = emailfilter.test(document.getElementById("email").value);
  (regemailid);

  if (regemailid == false) {
    $('#email_error').html('Please enter a valid Email');
    document.getElementById("email").focus();
    $('#email').css('border-color', '#495057');
    return false;
  } else {
    $('#email_error').html('');
    $('#email').css('border-color', '#495057');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/otp-send';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#resetpasswordform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        alertify.success(data.message);
        window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $(".loader").addClass('d-none');
    }, 500);
  });
});

$(".submitotpbtn").click(function (event) {
  event.preventDefault()
  var userotp = $('#userotp').val();
  if (!userotp) {
    $('#otp_error').html('Please enter a valid otp');
    document.getElementById("userotp").focus();
    $('#userotp').css('border-color', '#495057');
    return false;
  } else {
    $('#otp_error').html('');
    $('#userotp').css('border-color', '#495057');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/otp-match';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#submitotpform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        alertify.success(data.message);
        window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $(".loader").addClass('d-none');
    }, 500);
  });
});

$(".addnewpasswordresetbtn").click(function (event) {
  event.preventDefault()
  var newpassword = document.getElementById('newpassword').value;
  if (newpassword.length == "") {
    $('#password_error').html('Please enter your new password');
    document.getElementById('newpassword').value = "";
    document.getElementById('newpassword').focus();
    return false;
  } else {
    $('#password_error').html('');
    $('#newpassword').css('border-color', '#495057');
  }

  var confnewpassword = document.getElementById('confnewpassword').value;
  if (confnewpassword.length == "") {
    $('#confnewpassword_error').html('Please enter your confirm password');
    document.getElementById('confnewpassword').value = "";
    document.getElementById('confnewpassword').focus();
    return false;
  } else {
    $('#confnewpassword_error').html('');
    $('#confnewpassword').css('border-color', '#495057');
  }

  if (newpassword != confnewpassword) {
    $('#confnewpassword_error').html('The password and confirm password do not match!!');
    document.getElementById('confnewpassword').value = "";
    document.getElementById('confnewpassword').focus();
    return false;
  } else {
    $('#confnewpassword_error').html('');
    $('#confnewpassword').css('border-color', '#495057');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-newpassword';
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#passwordrestform').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        alertify.success(data.message);
        window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $(".loader").addClass('d-none');
    }, 500);
  });
});

$('body').on('click', '#instforgotpwd', function (e) {
  e.preventDefault();
  var email = $('#email').val();
  var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
  var regemailid = emailfilter.test($("#email").val());
  (regemailid);
  if (regemailid == false) {
    $('#email_error').html('Please enter a valid email');
    $("#email").focus();
    e.preventDefault();
    return false;
  } else {
    $('#email_error').html('');
  }
  $('.emailcoo').text(email);
  var url = APP_URL + '/institute-forgot-password';
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  $.ajax({
    type: "post",
    url: url,
    dataType: "json",
    data: $('#forgpasswordformid').serialize(),
    statusCode: {
      404: function (data) {
        console.log(data, "datadatadata")
        alertify.error(data?.responseJSON?.message);
      },
      200: function (data) {
        $(this).next().fadeIn();
        alertify.success(data?.message);
        $('#instnextbtn').trigger('click');
      }
    }
  })
})

$('body').on('click', '#matchotpnext', function (e) {
  e.preventDefault();
  var otp1 = $('#otp1').val();
  var otp2 = $('#otp2').val();
  var otp3 = $('#otp3').val();
  var otp4 = $('#otp4').val();
  if (!otp1) {
    $('#otpp_error').html('Please enter a otp');
    $("#otp1").focus();
    return false;
  } else {
    $('#otpp_error').html('');
  }
  if (!otp2) {
    $('#otpp_error').html('Please enter a otp');
    $("#otp2").focus();
    return false;
  } else {
    $('#otpp_error').html('');
  }
  if (!otp3) {
    $('#otpp_error').html('Please enter a otp');
    $("#otp3").focus();
    return false;
  } else {
    $('#otpp_error').html('');
  }
  if (!otp4) {
    $('#otpp_error').html('Please enter a otp');
    $("#otp4").focus();
    return false;
  } else {
    $('#otpp_error').html('');
  }

  var url = APP_URL + '/institute-otp-match';
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  $.ajax({
    type: "post",
    url: url,
    dataType: "json",
    data: $('#forgpasswordformid').serialize(),
    statusCode: {
      401: function () {
        setTimeout(() => {
          window.location.href = data.redirect;
        }, 500)
      },
      422: function (data) {
        console.log(data, "datadatadata")
        alertify.error(data?.responseJSON?.message);
      },
      200: function (data) {
        $(this).next().fadeIn();
        alertify.success(data.message);
        $('#instotpnextbtn').trigger('click');
      }
    }
  })
})

$('body').on('click', '#institute_reset_password_btn', function (e) {
  e.preventDefault();
  var confirm_password = $('#confirm_password').val();
  var new_password = $('#new_password').val();

  if (!new_password) {
    $('#new_password_error').html('Please enter a new password');
    $("#new_password").focus();
    e.preventDefault();
    return false;
  } else {
    $('#new_password_error').html('');
  }


  if (!confirm_password) {
    $('#confirm_password_error').html('Please enter a confirm password');
    $("#confirm_password").focus();
    e.preventDefault();
    return false;
  } else {
    $('#confirm_password_error').html('');
  }


  if (confirm_password != new_password) {
    $('#confirm_password_error').html('Confirm password is not same');
    $("#confirm_password").focus();
    e.preventDefault();
    return false;
  } else {
    $('#confirm_password_error').html('');
  }


  var url = APP_URL + '/institute-reset-password';
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  $.ajax({
    type: "post",
    url: url,
    dataType: "json",
    data: $('#forgpasswordformid').serialize(),
    statusCode: {
      401: function () {
        window.location.href = data.redirect;
      },
      404: function (data) {
        alertify.error(data?.responseJSON?.message);
      },
      200: function (data) {
        $(this).next().fadeIn();
        alertify.success(data?.message);
        setTimeout(() => {
          window.location.href = data.redirect;
        }, 500)
      }
    }
  })
})


//  taking promocode details

$('body').on('click', '.take_client_promocode', function () {

  swal({
    title: "Want to enter Promocode",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes",
    cancelButtonText: "No",
    allowOutsideClick: false,
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $('#myModal').modal('show');
    }
    else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
})

// 

$('body').on('click', '#checkpromocode', function (event) {

  var plan_id = $('#plan_id').val();
  var promocode_name = $('#promocode_name').val();
  if (promocode_name == "") {
    $('#promocode_name_error').html('Please enter your promo code');
    $('#promocode_name').focus();
    return false;
  } else {
    $('#promocode_name_error').html('');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/checkpromocode_for_institute';
  $.ajax({
    url: url,
    type: 'post',
    data: { promocode: promocode_name, plan_id: plan_id },
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);
        $(document).find('#checkout-total').text(response.total);
        $(document).find('#checkout-discount').html(response.discount);
        $('#checkpromocode').attr('disabled', 'disabled');
        $('.take_client_promocode').attr('disabled', 'disabled');
        $('#myModal').modal('hide');
      } else {
        alertify.error(response.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });

})
$('body').on('click', '#saveAssociation', function (event) {
  event.preventDefault()
  var association_name = $('#association_name').val();
  var association_address = $('#association_address').val();
  var association_logo = $("#association_logo").val();


  if (association_name == "") {
    $('#association_name_error').html('Please enter association name');
    document.getElementById('association_name').value = "";
    document.getElementById('association_name').focus();
    return false;
  } else {
    $('#association_name_error').html('');
  }
  if (association_address == "") {
    $('#association_address_error').html('Please enter association address');
    document.getElementById('association_address').value = "";
    document.getElementById('association_address').focus();
    return false;
  } else {
    $('#association_address_error').html('');
  }
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-associations';
  var formData = new FormData($("#associationform")[0]);

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    contentType: false,
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        // $('#addtrainerfrm')[0].reset();
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '#addactivitycategorytotrainer', function (event) {
  event.preventDefault()

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/update-category-trainer';
  var formData = new FormData($("#addactivitycategoryformtrainer")[0]);

  $.ajax({
    url: url,
    type: 'post',
    data: formData,
    processData: false,
    contentType: false,
    dataType: "json",
    beforeSend: function () {
      $("#overlay").fadeIn(300)
    },
    success: function (data) {
      if (data.success == true) {
        alertify.success(data.message);
        $('#addactivitycategoryformtrainer')[0].reset();
        // window.location.href = data.redirect;
      } else {
        alertify.error(data.message);
      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);

  });

})

$('body').on('click', '.RemoveTrainerCategorys', function () {

  var id = $(this).attr("data-id");
  $(this).parent().remove();
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }

  });
  var url = APP_URL + '/remove_trainers_categors';

  $.ajax({
    url: url,
    type: 'post',
    data: { id: id },
    dataType: "json",
    beforeSend: function () {
      // $("#overlay").fadeIn(300)
    },
    success: function (response) {
      if (response.success == true) {
        alertify.success(response.message);

      }
    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });
})

function trainer_request_accept_reject(id, status) {

  swal({
    title: "Request",
    text: "Please ensure and then confirm!",
    type: "warning",
    showCancelButton: !0,
    confirmButtonText: "Yes!",
    cancelButtonText: "No, cancel!",
    reverseButtons: !0
  }).then(function (e) {
    if (e.value === true) {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      var url = APP_URL + '/accept-reject-trainer-request';

      $.ajax({
        type: 'POST',
        url: url,
        data: {
          id: id,
          status: status
        },
        dataType: "json",
        beforeSend: function () {
          // $("#overlay").fadeIn(300)
        },
        success: function (data) {
          if (data.success == true) {
            alertify.success(data.message);

            if (status == 1) {
              $('#trainerrequeststatus-' + id).removeClass('btn-danger btn-primary');
              $('#trainerrequeststatus-' + id).addClass('btn-success');
              $('#trainerrequeststatus-' + id).text('Accept');
            } else {
              $('#trainerrequeststatus-' + id).removeClass('btn-primary btn-success');
              $('#trainerrequeststatus-' + id).addClass('btn-danger');
              $('#trainerrequeststatus-' + id).text('Reject');
            }
          } else {
            alertify.error(data.message);
          }

        },
      }).done(function () {
        setTimeout(function () {
          // $("#overlay").fadeOut(300);
        }, 500);

      });

    } else {
      e.dismiss;
    }
  }, function (dismiss) {
    return false;
  })
}

$("#admin_select_certification").click(function () {

  var My_result_id = $('#My_result_id').val();
  var name1 = $('#certificate_id').val();
  if (name1 == "") {
    $('#certificate_id_error').html('Please select certificate');
    $('#certificate_id').val('');
    $('#certificate_id').focus();
    return false;

  } else {
    $('#certificate_id_error').html('');
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  var url = APP_URL + '/institute-generate-certificate/' + My_result_id;
  $.ajax({
    type: 'POST',
    url: url,
    data: $('#genrate_certificate_form').serialize(),
    dataType: "json",
    beforeSend: function () {
      $(".loader").removeClass('d-none');
    },
    success: function (data) {
      $(".loader").addClass('d-none');
      if (data.success == true) {
        alertify.success(data.message);
        location.reload();
      } else {
        alertify.error(data.message);
      }

    },
  }).done(function () {
    setTimeout(function () {
      $("#overlay").fadeOut(300);
    }, 500);
  });

});

// newsletter

$("#institute_addnewsletter").click(function (event) {
    event.preventDefault();
    var news_title = $("#news_title").val();
    if (news_title == "") {
        $("#news_title_error").html("Please enter title");
        $("#news_title").val("");
        $("#news_title").focus();
        return false;
    } else {
        $("#news_title_error").html("");
    }
    var sourse_link = document.getElementById("sourse_link").value;
    var regexp =
        /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    if (sourse_link != "") {
        if (!regexp.test(sourse_link)) {
            $("#sourse_link_error").html("Please enter valid url.");
            document.getElementById("sourse_link").value = "";
            document.getElementById("sourse_link").focus();
            return false;
        } else {
            $("#sourse_link_error").html("");
        }
    }
    var fornews = $("#fornews").val();
    if (fornews == "") {
        $("#fornews_error").html("Please select option");
        $("#fornews").val("");
        $("#fornews").focus();
        return false;
    } else {
        $("#fornews_error").html("");
    }
    // var name2 =$('#about_lesson').val();
    var name2 = CKEDITOR.instances["news_descriptions"].getData();
    if (name2 == "") {
        $("#news_descriptions_error").html("Please enter descriptions");
        $("#news_descriptions").val("");
        $("#news_descriptions").focus();
        return false;
    } else {
        $("#news_descriptions_error").html("");
    }

     var options = $('#student_id > option:selected');
   
         if(options.length == 0){
            $("#student_error").html("Please select value");
            $("#student_id").focus();
            return false;
         }else{
            $("#student_error").html("");
         }
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }
    var url = APP_URL + "/institute-save-news-letter";
    $.ajax({
        type: "POST",
        url: url,
        data: new FormData($("#institute_add_news_form")[0]),
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass("d-none");
        },
        success: function (data) {
            $(".loader").addClass("d-none");
            if (data.success == true) {
                alertify.success(data.message);
                $("#institute_add_news_form")[0].reset();
                window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
});


$("#fornews_institute").change(function () {
    // alert($("#type :selected").attr('value'))
    var value = $("#fornews_institute :selected").attr("value");
    var data = $("#fornews_institute :selected").attr("data-id");
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    var url = APP_URL + "/institute-getUserDetails";
    $.ajax({
        type: "POST",
        url: url,
        data: { value: value, data: data },
        dataType: "html",
        success: function (data) {
            $("#append").html(data);
            $(".select2").each(function () {
                $(this)
                    .wrap('<div class="position-relative"></div>')
                    .select2({placeholder: "Select value",dropdownParent: $(this).parent(),
                    });
            });
        },
    }).done(function () {
        setTimeout(function () {
            //  $("#overlay").fadeOut(300);
        }, 500);
    });
});


$("body").on("change", "#student_id", function () {
     $('#student_id1').val('');
    var selected = $(this).find("option:selected", this);
    if (selected.length > 0 && selected[0].value == "0") {
        $("#student_id  option").prop("disabled", true);
        $('.select2-selection__choice').each(function(index, value){
          if(this.title=='ALL'){
            $('#student_id1').val(selected[0].value);
          }else{
            this.remove();   
          }
        });
    } else {
        $("#student_id  option").prop("disabled", false);
    }
});


$("#instituteeditnewsletter").click(function (event) {
    event.preventDefault();
    var news_title = $("#news_title").val();
    if (news_title == "") {
        $("#news_title_error").html("Please enter news title");
        $("#news_title").val("");
        $("#news_title").focus();
        return false;
    } else {
        $("#news_title_error").html("");
    }
    var sourse_link = document.getElementById("sourse_link").value;
    var regexp =
        /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    if (sourse_link != "") {
        if (!regexp.test(sourse_link)) {
            $("#sourse_link_error").html("Please enter valid url.");
            document.getElementById("sourse_link").value = "";
            document.getElementById("sourse_link").focus();
            return false;
        } else {
            $("#sourse_link_error").html("");
        }
    }
    // var name2 =$('#about_lesson').val();
    var name2 = CKEDITOR.instances["news_descriptions"].getData();
    if (name2 == "") {
        $("#news_descriptions_error").html("Please enter news descriptions");
        $("#news_descriptions").val("");
        $("#news_descriptions").focus();
        return false;
    } else {
        $("#news_descriptions_error").html("");
    }
    var fornews = $("#fornews").val();
    if (fornews == "") {
        $("#fornews_error").html("Please select for news");
        $("#fornews").val("");
        $("#fornews").focus();
        return false;
    } else {
        $("#fornews_error").html("");
    }

     var options = $('#student_id > option:selected');
   
         if(options.length == 0){
            $("#student_error").html("Please select value");
            $("#student_id").focus();
            return false;
         }else{
            $("#student_error").html("");
         }
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }
    var url = APP_URL + "/institute-update-news-letter";
    $.ajax({
        type: "POST",
        url: url,
        data: $("#instituteeditnewsform").serialize(),
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass("d-none");
        },
        success: function (data) {
            $(".loader").addClass("d-none");
            if (data.success == true) {
                alertify.success(data.message);
                // $('#adineditnewsform')[0].reset();
                window.location.href = APP_URL + "/institute-news-letter-list";
            } else {
                alertify.error(data.message);
                window.location.href = APP_URL + "/institute-news-letter-list";
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
});



$('body').on('click', '.institute_news_letter_delete_data', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/institute-delete-news-letter';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})


$("body").on("click", ".institute_news_letter_delete_all_data", function () {
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0,
    }).then(
        function (e) {
            if (e.value === true) {
                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                            "content"
                        ),
                    },
                });
                var url = APP_URL + "/institute-delete-all-news-letter";
                $.ajax({
                    type: "POST",
                    url: url,
                    data: {
                        id: "Yes",
                    },
                    dataType: "json",
                    beforeSend: function () {
                        // $(".loader").removeClass("d-none");
                    },
                    success: function (data) {
                        // $(".loader").addClass("d-none");
                        if (data.success == true) {
                            alertify.success(data.message);
                            window.location.href = APP_URL +"/institute-news-letter-list";
                        } else {
                            alertify.error(data.message);
                        }
                    },
                }).done(function () {
                    setTimeout(function () {
                        $(".loader").addClass("d-none");
                    }, 500);
                });
            } else {
                e.dismiss;
            }
        },
        function (dismiss) {
            return false;
        }
    );
});

// Student managemanet 



$(document).on('keyup', '#studentname', function () {
  var studentname = document.getElementById("studentname").value;
  var sortbyactivity = document.getElementById("sortbyactivity").value;
  get_student_managementnew(studentname, sortbyactivity);
});

$(document).on('change', '#sortbyactivity', function () {
  var sortbyactivity = document.getElementById("sortbyactivity").value;
  var studentname = document.getElementById("studentname").value;
  get_student_managementnew(studentname, sortbyactivity);
});


$(document).on('click', '#printstudentmanagemenet .pagination li a', function(event){
  event.preventDefault(); 
   var page = parseInt($(this).attr('href').split('page=')[1]);
   var sortbyactivity = document.getElementById("sortbyactivity").value;
   var studentname = document.getElementById("studentname").value;
  get_student_managementnew(studentname, sortbyactivity,page);
 });

