document.addEventListener("DOMContentLoaded", function () {


    initdateSchedulePickers();
});


function initdateSchedulePickers() {

    $("input.past-date-picker").daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        autoApply: false,
        maxDate: new Date(),

        locale: {
            format: 'MM/DD/YYYY',
            cancelLabel: 'Clear'
        },
    }).on('cancel.daterangepicker', function (ev, picker) {

        $(this).val('');
    });


    $("input.future-date-picker").daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        autoApply: false,
        minDate: new Date(),
        locale: {
            format: 'MM/DD/YYYY',
            cancelLabel: 'Clear'
        },

    }).on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
    });
    //  trigger 'cancel.daterangepicker' if value is empty
    $("input.future-date-picker").each(function () {
        const attributeValue = $(this).attr('value');

        if (attributeValue === '') {
            $(this).trigger('cancel.daterangepicker');
        }
    });

    $("input.past-date-picker").each(function () {
        const attributeValue = $(this).attr('value');
        if (attributeValue === '') {
            $(this).trigger('cancel.daterangepicker');
        }
    });



    // Initialize datesingle
    $("input[name='datesingle']").daterangepicker({

        singleDatePicker: true,
        autoApply: false,
        showDropdowns: true,
        minDate: $("input[name='datesingle']").val() ? moment($("input[name='datesingle']").val(), 'MM/DD/YYYY') :new Date(),

        /*isInvalidDate: function (date) {
            return date.day() === 0; // Disable Sundays
        }*/
    }, function (start, end, label) {
        // Destroy existing datesingle1 instance
        $("input[name='datesingle1']").daterangepicker('destroy');


        /* if ($('#is_imported').length > 0 && !$('#is_imported').is(':checked')) {
             var maxDate = moment(start).add(30, 'days');

             $("input[name='datesingle1']").daterangepicker({
                 singleDatePicker: true,
                 showDropdowns: true,
                 autoApply: false,
                 minDate: start,
                 maxDate: maxDate,
                 // isInvalidDate: function (date) {
                 //     return date.day() === 0; // Disable Sundays
                 // }
             });
         } else {*/
        // Initialize datesingle1 with the new minimum date
        $("input[name='datesingle1']").daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoApply: false,
            minDate: start,
            /*isInvalidDate: function (date) {
                return date.day() === 0; // Disable Sundays
            }*/
        });
        /*  } */


    });

    /*if ($('#is_imported').length > 0 && !$('#is_imported').is(':checked')) {
        var start = $("input[name='datesingle']").val() ? moment($("input[name='datesingle']").val(), 'MM/DD/YYYY') : moment()
        var maxDate = moment(start).add(30, 'days');
        $("input[name='datesingle1']").daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoApply: false,
            // Set minDate to today for the initial setup
            minDate: start,
            maxDate: maxDate,
            // isInvalidDate: function (date) {
            //     return date.day() === 0; // Disable Sundays
            // }
        });
    } else {*/
    $("input[name='datesingle1']").daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        autoApply: false,
        // Set minDate to today for the initial setup
        minDate: $("input[name='datesingle']").val() ? moment($("input[name='datesingle']").val(), 'MM/DD/YYYY') : moment(),
        /*isInvalidDate: function (date) {
            return date.day() === 0; // Disable Sundays
        }*/
    });
    /* } */


}


function disableFieldsets() {
    let weekdays = getWeekdaysBetweenDates();

    for (let i = 0; i <= 7; i++) {
        let disable = weekdays.length === 0 || !weekdays.includes(i + 1);
        $(`fieldset#weekday-${i + 1}`).prop("disabled", disable);
    }
}




function getWeekdaysBetweenDates() {

    let currentDate = moment($("input[name='datesingle']").val(), 'MM/DD/YYYY');
    let endDate = moment($("input[name='datesingle1']").val(), 'MM/DD/YYYY');
    let weekdays = [];
    let i = 0;
    while (currentDate.isSameOrBefore(endDate, 'day') && i < 7) {
        // Check if the current day is a weekday (Monday to Saturday)

        if (currentDate.isoWeekday() >= 1 && currentDate.isoWeekday() <= 7) {
            weekdays.push(currentDate.isoWeekday());
        }

        // Move to the next day
        currentDate.add(1, 'day');
        i++;

        // Break the loop after 7 iterations
        if (i === 7) {
            break;
        }
    }

    return weekdays;
}


$("input[name='datesingle'],input[name='datesingle1']").change(function () {
    disableFieldsets();
});

$("#invite_program").click(function (html) {
    var instructor_id = $('#instructor_id').val();
    if (instructor_id == "") {
        $('#instructor_id_error').html('Please select instructor');
        $('#instructor_id').val('');
        $('#instructor_id').focus();
        return false;

    } else {
        $('#instructor_id_error').html('');

    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/post_invite_program';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#inviteprogram').serialize(),
        dataType: "json",
        beforeSend: function () {

        },
        success: function (data) {

            if (data.success == true) {
                alertify.success(data.message);

                window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }

        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });

});

function get_invite_instructors() {

    if (document.querySelectorAll('#inviteprogram input[type="checkbox"]:checked').length == 0) {
        $('#instructor_id').html("");
        return false;
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/get_invite_instructors';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#inviteprogram').serialize(),
        dataType: "json",
        beforeSend: function () {

        },
        success: function (data) {

            if (data.success == true) {
                var html = `<option>Select Instructors</option>`;

                for (var i = 0; i < data.data.length; i++) {
                    if (data.data[i].last_name) {
                        html += `<option value="` + data.data[i].id + `">` + data.data[i].first_name + ` ` + data.data[i].last_name + `(` + data.data[i].email + `)</option>`;
                    } else {
                        html += `<option value="` + data.data[i].id + `">` + data.data[i].first_name + `(` + data.data[i].email + `)</option>`;
                    }

                }


                $('#instructor_id').html(html);
            } else {
                alertify.error(data.message);
            }

        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });


}

function initAutocompletemap() {
    const mapElement = document.getElementById("map");
    const input = document.getElementById("address");


    if (!mapElement && !input) {
        return false;
    }

    const map = new google.maps.Map(document.getElementById("map"), {
        center: {
            lat: -33.8688,
            lng: 151.2195
        },
        zoom: 13,
        mapTypeId: "roadmap",
    });
    // Create the search box and link it to the UI element.
    const searchBox = new google.maps.places.SearchBox(input);

    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

    map.addListener("bounds_changed", () => {
        searchBox.setBounds(map.getBounds());
    });

    let markers = [];
    searchBox.addListener("places_changed", () => {
        const places = searchBox.getPlaces();

        if (places.length == 0) {
            return;
        }

        // Clear out the old markers.
        markers.forEach((marker) => {
            marker.setMap(null);
        });
        markers = [];

        // For each place, get the icon, name and location.
        const bounds = new google.maps.LatLngBounds();

        places.forEach((place) => {

            const addressComponents = place.address_components;
            let country, city, postal_code, state;
            for (let component of addressComponents) {

                if (component.types.includes('country')) {
                    country = component.long_name;
                } else if (component.types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                } else if (component.types.includes('locality') || component.types.includes('administrative_area_level_3')) {
                    city = component.long_name;
                } else if (component.types.includes('postal_code')) {
                    postal_code = component.long_name;
                }
            }
            $('input[name="city"]').val(city);
            $('input[name="country"]').val(country);
            $('input[name="zipcode"]').val(postal_code);
            $('input[name="state"]').val(state);

            $('input[name="lat"]').val(place.geometry.location.lat());
            $('input[name="lng"]').val(place.geometry.location.lng());

            if (!place.geometry || !place.geometry.location) {
                return;
            }

            const icon = {
                url: place.icon,
                size: new google.maps.Size(71, 71),
                origin: new google.maps.Point(0, 0),
                anchor: new google.maps.Point(17, 34),
                scaledSize: new google.maps.Size(25, 25),
            };

            // Create a marker for each place.
            markers.push(
                new google.maps.Marker({
                    map,
                    icon,
                    title: place.name,
                    position: place.geometry.location,
                }),
            );

            if (place.geometry.viewport) {
                // Only geocodes have viewport.
                bounds.union(place.geometry.viewport);
            } else {
                bounds.extend(place.geometry.location);
            }

        });
        map.fitBounds(bounds);

    });

}


function getlinkfeild(elem) {
    var delivery_type = $(elem).val();
    if (delivery_type == 'Online') {
        $('#link-box').show();
    } else {
        $('#link-box').hide();
    }

    // document.startViewTransition(() => {


    // });

}

function add_more() {
    var html = ` <div class="row after-add-more" style="margin-left:0 !important;"> <div class="col-md-2 form-group">

                                        <input type="text" class="form-control" name="job_title[]" id="first_name" placeholder="Job Title">
                                        <span id="name_error" class="err"></span>
                                    </div>

                                    <div class="col-md-2 form-group">

                                        <input type="text" class="form-control" name="first_name[]" id="first_name" placeholder="First name">
                                        <span id="name_error" class="err"></span>
                                    </div>

                                    <div class="col-md-2 form-group">

                                        <input type="text" id="last_name" class="form-control" name="last_name[]" placeholder="Last name">
                                        <span id="p_last_name_error" class="err"></span>
                                    </div>



                                    <div class="col-md-3 form-group">

                                        <input type="text" class="form-control" name="cemail[]"  placeholder="Email">
                                        <span id="email_errors" class="err"></span>
                                    </div>



                                    <div class="col-md-2 form-group">

                                        <input type="number" class="form-control" name="phone[]" id="phone" placeholder="Phone">
                                        <span id="phone_error" class="err"></span>
                                    </div>


                                    <div class="col-md-1 form-group change">


                                    <a class='btn btn-danger remove'>-</a>

                                    </div> </div>`;



    $(".after-add-more").last().after(html);
    $(html).find(".change").html("<a class='btn btn-danger remove'>-</a>");


}

$("body").on("click", ".remove", function () {
    $(this).parents(".after-add-more").remove();
});


function instructorFilter(elem) {

    if(elem==='Subject'){

        let isSubChecked = $('#Subject').is(':checked');

if(isSubChecked){
    $('#subsubjectblock').show()
}else{
    $('#subsubjectblock').hide()
}
    }


    if (elem === 'standby') {
        let isChecked = $('#standBy').is(':checked');
        $('#commonInviteBtn').text('Invite');

        if(isChecked){

            $('#deadlineinvite').hide();
            $('#deadlineinvitetype').val('1');
            $('#assign_type option[value="standBy"]').prop('disabled',true)
        }else{
            $('#deadlineinvite').show();
            $('#deadlineinvitetype').val('2');
            $('#assign_type option[value="standBy"]').prop('disabled',false)
        }

    }else if (elem ==='All' || elem ==='Sub' ){
        let isChecked = $('#standbyId').is(':checked');
        $('#commonInviteBtn').text(isChecked ? 'Assign' : 'Invite');
    }

    drawAdminTables();
}

function instructorFiltermakeup(elem) {

    if(elem==='Subject'){

        let isSubChecked = $('#Subject').is(':checked');

if(isSubChecked){
    $('#subsubjectblock').show()
}else{
    $('#subsubjectblock').hide()
}
    }


    if (elem === 'standby') {
        let isChecked = $('#standbyId').is(':checked');
        $('#commonInviteBtn').text(isChecked ? 'Assign' : 'Invite');

        if(isChecked){

            $('#deadlineinvite').hide();
            $('#deadlineinvitetype').val('1');
            $('#assign_type option[value="standBy"]').prop('disabled',true)
        }else{
            $('#deadlineinvite').show();
            $('#deadlineinvitetype').val('2');
            $('#assign_type option[value="standBy"]').prop('disabled',false)
        }

    }else if (elem ==='All' || elem ==='Sub' ){
        let isChecked = $('#standbyId').is(':checked');
        $('#commonInviteBtn').text(isChecked ? 'Assign' : 'Invite');
    }

    // drawAdminTables();
}






