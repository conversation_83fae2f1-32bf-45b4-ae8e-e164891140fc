window.commonInstructorTable = [{
    data: 'first_name'
},
{
    data: 'email',
}, {
    data: 'city',
    searchable: false,
    orderable: false,
},
{
    data: 'distance',
    searchable: false,
    orderable: false,
}, {
    data: 'grade',
    searchable: false,
    orderable: false,
}, {
    data: 'aval',
    searchable: false,
    orderable: false,
}, {
    data: 'programs',
    searchable: false,
    orderable: false,
}, {
    data: 'teach'
}
];

window.commonMarketplaceInstructorTable = [{
    data: 'first_name'
},
{
    data: 'email',
},
{
    data: 'status',
},
];

$(function () {
    initializeTimepicker();
    initializeBootstrapDatepicker();
    $('.admin-dataTable').DataTable({
        searching: true
    });



    $("input[name='datesingle']").change(function () {
        $(".no-class-datepicker").datepicker('destroy');
        let startDate = $("input[name='datesingle']").val();
        let endDate = $("input[name='datesingle1']").val();
        $(".no-class-datepicker").data('date-start-date',startDate);
        $(".no-class-datepicker").data('date-end-date',endDate);
        $(".no-class-datepicker").datepicker({ startDate: startDate, endDate: endDate, autoclose: true });

        $(".no-class-datepicker").each(function () {
            let currentDate = $(this).val();
            if(currentDate>endDate || currentDate<startDate){
                $(this).val('');
            }
        });

    });

    $("input[name='datesingle1']").change(function () {
        $(".no-class-datepicker").datepicker('destroy');
        let startDate = $("input[name='datesingle']").val();
        let endDate = $("input[name='datesingle1']").val();
        $(".no-class-datepicker").data('date-start-date',startDate);
        $(".no-class-datepicker").data('date-end-date',endDate);
        $(".no-class-datepicker").datepicker({ startDate: startDate, endDate: endDate, autoclose: true });

        $(".no-class-datepicker").each(function () {
            let currentDate = $(this).val();
            if(currentDate>endDate || currentDate<startDate){
                $(this).val('');
            }
        });

    });

});

function showclasses() {
    var assign_type = $('#assign_type').val();
    if (assign_type == 1) {
        $('#classdivassign').css('display', 'none')
    } else if (assign_type === "0" || assign_type == "standBy") {
        $('#classdivassign').css('display', 'block')
    } else {
        $('#classdivassign').css('display', 'none')
    }
}
$('#manage-payments-excel-form').on('submit', function (event) {

    event.preventDefault();


    if ($('#filter-form').length) {
        if(!$('input[name="range"]')[0]?.value){
            document.getElementById('daterange').value = '';
        }

        var filterData = $('#filter-form').serialize();

        var exportForm = $('<form></form>');
        exportForm.attr('action', $(this).attr('action'));
        exportForm.attr('method', $(this).attr('method'));
        exportForm.append('<input type="hidden" name="filter_data" value="' + filterData + '">');

        $(document.body).append(exportForm);
        exportForm.submit();
        $(document.body).remove(exportForm);
    }
});

function initializeBootstrapDatepicker() {

    $(".no-class-datepicker").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('date-start-date');
        var endDate = $datepicker.data('date-end-date');
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $('.no-class-datepicker').datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            endDate: endDate || new Date(),
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });

    $(".deadline-datepicker").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('date-start-date');
        var endDate = $datepicker.data('date-end-date');
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $('.deadline-datepicker').datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            endDate: endDate || new Date(),
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });

    $(".deadline-datepickermakeup").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('date-start-date');
        var endDate = $datepicker.data('date-end-date');
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $('.deadline-datepickermakeup').datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            // endDate: endDate || new Date(),
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
        $('#class_date').change(setDeadlineEndDate);
    });
    $(".replacement-datepicker").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('date-start-date');
        var endDate = $datepicker.data('date-end-date');
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $('.replacement-datepicker').datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            endDate: endDate || new Date(),
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });

    $(".makeup-datepicker").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('mdate-start-date');
        var endDate = $datepicker.data('mdate-end-date');
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $('.makeup-datepicker').datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            // endDate: endDate || new Date(),
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });


    $(".req-datepicker").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('mdate-start-date');
        var endDate = $datepicker.data('mdate-end-date');
        $datepicker.attr("placeholder", "Deadline");
        $('.req-datepicker').datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            // endDate: endDate || new Date(),
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });

}

function allclassess() {

    if ($('#all_classes').is(':checked')) {


        $('#Classes').prop('disabled', true);


    } else {
        $('#Classes').prop('disabled', false);
    }

}

function syncCheckboxes(checkbox) {
    var nextCheckbox = $(checkbox).next('input[type="checkbox"]');
    const checkboxes = document.querySelectorAll('.program-note-checkbox');
    nextCheckbox.prop('checked', checkbox.checked);
        if (checkbox.checked) {
            checkboxes.forEach(cb => {
                if (cb !== checkbox) {
                    cb.disabled = true;
                }
            });
        } else {
            checkboxes.forEach(cb => {
                cb.disabled = false;
            });
        }
}
function initializeTimepicker() {

    $('.timepicker').timepicker(

        {
            'placeholder': 'h:i A',
            'timeFormat': 'h:i A',
            'step': 1,
            'listWidth': 1,

        },
    ).attr('placeholder', 'HH:MM AM/PM');

    $('.timepicker').bind('timeFormatError', function () {
        $(this).val('');
    });
}
function closeModal(elm) {
    $(elm).closest('.modal').modal('hide');
}
function adminDataTable() {

    $('.admin-modal-dataTable').DataTable({
        searching: true, // Enable searching
        lengthMenu: [10, 15],
        "language": {
            "infoFiltered": ""
        }
    });
}
$('.modal').on('shown.bs.modal', function () {
    initializeTimepicker();
    initdateSchedulePickers();
    initializeSelect2();
    initializeBootstrapDatepicker();

});
$('#FilterModal').on('shown.bs.modal', function () {
    var modelElmBack = $(".modal-backdrop");
    var modelElm = $("#FilterModal.modal");
    modelElm.css("z-index", "1");
    modelElmBack.css("z-index", "0");

    if (typeof $.fn.daterangepicker !== 'undefined') {
        $('.daterangePayment').daterangepicker({
            locale: {
                format: 'MM-DD-YYYY',
                separator: " TO "
            },

            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'This Week': [moment().startOf('week'), moment().endOf('week')],
                'Last Week': [moment().subtract(1, 'week').startOf('week'), moment().subtract(1, 'week').endOf('week')],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                'This Year': [moment().startOf('year'), moment().endOf('year')],
                'Last Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
            },
        }).on('cancel.daterangepicker', function (ev, picker) {
            $(this).val('');
        });
        if(!$('input[name="range"]')[0]?.value){
            document.getElementById('daterange').value = '';
        }

        $('.daterangePayment').on('input', function () {
            if (!$(this).val()) {
                $(this).trigger('cancel.daterangepicker');
            }
        });
        $(".daterangePayment").each(function () {
            const attributeValue = $(this).attr('value');
            if (attributeValue === '') {
                $(this).trigger('cancel.daterangepicker');
            }
        });
    } else {
        $(".daterangePayment").each(function () {
            $(this).trigger('cancel.daterangepicker');
        });
    }

});

$('#searchFilterModal ').on('shown.bs.modal', function () {
    var modelElmBack = $(".modal-backdrop");
    var modelElm = $("#searchFilterModal.modal");
    modelElm.css("z-index", "1");
    modelElmBack.css("z-index", "0");
});

function drawAdminTables() {

    if (typeof dataTable !== 'undefined') {
        dataTable.draw();
    }
    if (typeof dataTable2 !== 'undefined') {
        dataTable2.draw();
    }
}

function initializeSelect2() {
    $(".select2").select2({

        // allowClear: true,
    });
    $(".admin-select2").select2({
        // allowClear: true,
    });
}

function initializeAdminDataTable(selector, url, columns, searching = true) {
    return $(selector).DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'get',
        'autoWidth': true,
        'searching': searching,
        'ordering': true,
        'drawCallback': function (settings) {
            var table = $(this).DataTable();
            var response = table.ajax.json();
            let showFilters = false;
            if(document.getElementById('filter_details')){
                const filters = {
                    "programId": "Program",
                    "userId": "Instructor",
                    "range": "Date Range",
                    "payment_status": "Program Status"
                };
                Object.entries(filters).forEach(([filter, text]) => {
                    if(response[filter]){
                        $(`input[name="${filter}"]`).val(response[filter]);
                        $(`#filter_detail_${filter}`).html(`${text}: <span class="text-primary text-capitalize">${response[filter]}</span>, `);
                        showFilters = true;
                    } else {
                        $(`input[name="${filter}"]`).val('');
                        $(`#filter_detail_${filter}`).html('');
                        showFilters = showFilters ? showFilters : false;
                    }
                });
                if(showFilters) {
                    document.getElementById('filter_details').style.display = 'block';
                } else {
                    document.getElementById('filter_details').style.display = 'none';
                }
            }

            if (document.getElementById('program-excel-form')) {
                const filters = {
                    "searchValue": "searchValue",
                    "columnName": "columnName",
                };
                Object.entries(filters).forEach(([filter, text]) => {
                    if(response[filter]){
                        if (filter === 'columnName' && Array.isArray(response[filter])) {
                            // Remove any previous dynamic inputs for columnName array
                            $(`input[name="columnName[]"]`).remove();

                            // Loop through the array and extract the 'data' field
                            response[filter].forEach((column, index) => {
                                if (column.data && column.data != 'action' && column.data != 'departing' && column.data != 'chat') {
                                    $(`<input />`).attr({
                                        type: 'hidden',
                                        name: `columnName[]`,
                                        value: column.data
                                    }).appendTo('form#program-excel-form');
                                }
                            });
                        } else {
                            $(`input[name="${filter}"]`).val(response[filter]);
                        }
                    } else {
                        $(`input[name="${filter}"]`).val('');
                    }
                });
            }

            var showPagination = table.page.info().recordsTotal > 10;
            $(selector + '_paginate').toggle(showPagination);
        },
        'ajax': {
            'headers': {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            'url': url,
            'data': function (data) {
                if ($('#filter-form').length) {
                    var filterData = $('#filter-form').serialize();

                    // Parse the filter data and add it to the DataTables request
                    var filters = decodeURIComponent(filterData).split('&');
                    filters.forEach(function (filter) {
                        var parts = filter.split('=');
                        data[parts[0]] = parts[1];
                    });
                }
            }

        },
        'columns': columns,
        'order': [
            [0, 'desc']
        ]
    });

}

function initializeAdminDataTable2(selector, url, columns, searching = true) {
    return $(selector).DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'get',
        'autoWidth': true,
        'searching': searching,
        'ordering': true,
        'drawCallback': function (settings) {
            var table = $(this).DataTable();
            var response = table.ajax.json();
            let showFilters = false;


            var showPagination = table.page.info().recordsTotal > 10;
            $(selector + '_paginate').toggle(showPagination);
        },
        'ajax': {
            'headers': {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            'url': url,
            'data': function (data) {
            }

        },
        'columns': columns,
        'order': [
            [0, 'desc']
        ]
    });

}




function openAdminModalIfChecked(formid, route, modalSelector = "#common-admin-modal-lg") {
    // Check if at least one checkbox is checked
    var anyCheckboxChecked = $('input[form="' + formid + '"][type="checkbox"]:checked').length > 0;

    if (anyCheckboxChecked) {

        let formData = $('#' + formid).serialize();
        openAdminModal(route, modalSelector, formData);


        // Open the modal if at least one checkbox is checked

    } else {
        alertify.warning('Please select  at least one class');
    }
}


function openAdminModalIfCheckedIns(formid, route, modalSelector = "#common-admin-modal") {
    // Check if at least one checkbox is checked
    var anyCheckboxChecked = $('input[form="' + formid + '"][type="checkbox"]:checked').length > 0;

    if (anyCheckboxChecked) {

        let formData = $('#' + formid).serialize();

        openAdminModal(route, modalSelector, formData);


        // Open the modal if at least one checkbox is checked

    } else {
        alertify.warning('Please select  at least one instructor');
    }
}

function openAdminModalIfCheckedOnboardingIns(formid, route, modalSelector = "#common-admin-modal") {
    // Check if at least one checkbox is checked
    var anyCheckboxChecked = $('input[form="' + formid + '"][type="checkbox"]:checked').length > 0;

    if (anyCheckboxChecked) {

        let formData = $('#' + formid).serialize();

        openAdminModal(route, modalSelector, formData);


        // Open the modal if at least one checkbox is checked

    } else {
        alertify.warning('Please select  at least one instructor');
    }
}

let previousValue;
let isModalClosed = false;

$('body').on('focus', '#userStatus', function () {
    previousValue = $(this).val();
    isModalClosed = false;
});


$('body').on('change', '#userStatus', function () {
    if (isModalClosed) {
        isModalClosed = false; // Reset flag so next change can work normally
        return;
    }

    let selectedItem = $(this).children("option:selected").val();
    if (selectedItem) {
    } else {
        return false;
    }

    var userid = $(this).data('user-id');
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    if (selectedItem == 'Declined') {
        var url = APP_URL + '/admin/k12connections/sendRejectOnboardingMsg';

        openAdminModalsm(url);
        $('#common-admin-modal').attr('data-selected-item', selectedItem);
        $('#common-admin-modal').attr('data-user-id', userid);
    } else if (selectedItem == 'ChangeRequested') {
        var url = APP_URL + '/admin/k12connections/sendChangeRequestOnboardingMsg';

        openAdminModalsm(url);
        $('#common-admin-modal').attr('data-selected-item', selectedItem);
        $('#common-admin-modal').attr('data-user-id', userid);
    } else if (selectedItem == 'Approved') {
        var url = APP_URL + '/admin/k12connections/sendActiveOnboardingMsg';
        const data = {
            userid: userid
        }
        openAdminModalsm(url, '#common-admin-modal-lg', data);
        $('#common-admin-modal').attr('data-selected-item', selectedItem);
        $('#common-admin-modal').attr('data-user-id', userid);
    } else {
        $.ajaxSetup({
            headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }
        });

        var url = APP_URL + '/admin/k12connections/update_user_status';
        $.ajax({
            type: "POST",
            url: url,
            data: { 'status': selectedItem, 'userid': userid },
            dataType: "json",
            beforeSend: function () {
                $(".loader").removeClass('d-none');
            },
            success: function (response) {
                if (response.status == true && !isModalClosed) {
                    $('#common-admin-modal').modal('hide');
                    window.location.reload();
                    $(".loader").addClass('d-none');
                    alertify.success('Status Update Successfully');
                }
            }
        });
    }
});

// Reset dropdown to previous value when modal close button is clicked
$('body').on('click', '.close', function () {
    isModalClosed = true;
    $('#userStatus').val(previousValue).trigger('change');
    alertify.dismissAll();
});

// $('#common-admin-modal').on('hidden.bs.modal', function () {
//     $('#userStatus').val(previousValue).trigger('change');
// });

$('body').on('click', '#rejectOnboardingMsg, #changeRequestOnboardingMsg', function (e) {
    e.preventDefault();
    e.stopPropagation();
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var selectedItem = $('#common-admin-modal').data('selected-item');
    var userid = $('#common-admin-modal').data('user-id');
    var msg = $('#message').length > 0 ? $('#message').val().trim() : '';

    if (msg == '') {
        alertify.warning('Please enter any reason');
        return;
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/admin/k12connections/update_user_status';
    $.ajax({
        type: "POST",
        url: url,
        data: { 'status': selectedItem, 'userid': userid, 'reason': msg},
        dataType: "html",
        beforeSend: function () {
            $("#rejectOnboardingMsg").html(loading);
            $("#changeRequestOnboardingMsg").html(loading);
        },
        success: function (response) {
            if (typeof response === 'string') {
                response = JSON.parse(response); // Manually parse string response
            }
            if(response.status === true){
                $('#common-admin-modal').modal('hide');
                alertify.success('Status Update Successfully');
                window.location.reload();
            }
        }
    });
});



$('body').on('click', '#activeOnboardingMsg', function (e) {
    e.stopPropagation();
    const checkedBoxes = $('input[type="checkbox"][name^="selected"]:checked');
    if (checkedBoxes.length === 0) {
        e.preventDefault(); // prevent default action (like form submission)
        alertify.error("Please select at least one subject before continuing.");
        return;
    }
    const loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    const selectedItem = $('#common-admin-modal').data('selected-item');
    const userid = $('#common-admin-modal').data('user-id');

    // Collect all indexed inputs from the Blade structure
    let params = {
        'status': selectedItem,
        'userid': userid,
        'budget_lines': [],
        'bilingual_inc': $('input[name="bilengue"]').val(),
        'case_management': $('input[name="case_managemnet_status"]')[0].checked ? $('input[name="case_managemnet"]').val() : 0,
        'in_person': 0
    };
    // Automatically collect all inputs that follow the pattern fieldName[index]
    $('input[name*="["]').each(function () {
        const [name, index] = $(this).attr('name').split('[').map(item => item.replace(']', ''));
        const value = this.type === 'checkbox' ? this.checked :$(this).val();
        if(params.budget_lines[index] === undefined) {
            params.budget_lines[index] = {};
        }
        params.budget_lines[index][name] = value;
    });
    console.log(params);

    // Setup CSRF
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Send AJAX
    $.ajax({
        type: "POST",
        url: APP_URL + '/admin/k12connections/update_user_status',
        data: params,
        dataType: "json",
        beforeSend: function () {
            $("#activeOnboardingMsg").html(loading);
        },
        success: function (response) {
            if (typeof response === 'string') {
                response = JSON.parse(response);
            }

            if (response.status === true) {
                $('#common-admin-modal').modal('hide');
                alertify.success('Status Update Successfully');
                window.location.reload();
                $("#activeOnboardingMsg").text('Confirm');
            }
        },
        error: function (err) {
            alertify.error('Something went wrong. Please try again.');
            console.error('AJAX Error:', err);
        }
    });

});

$('body').on('click', '.instructor_delete', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/admin/k12connections/delete-instructor';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        dataTable.row(currentRow.parents('tr')).remove().draw(false);
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})

function openAdminModalWithData(url, formId){
    let formData = $('#'+formId).serialize();
    openAdminModal(url, undefined, formData )
}

function openAdminModal(url, modalId = "#common-admin-modal-lg", data = {}) {

    $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeSelect2();
            adminDataTable();
        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });

}

function openAdminModalsm(url, modalId = "#common-admin-modal", data = {}) {

    $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeSelect2();
            adminDataTable();
        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });

}

function adminModalSubmit(form, modalId = "common-admin-modal") {
    event.preventDefault();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {
            if (res.status == false) {
                $(form).find('button .save-loader').show();
                $(form).find('button').prop("disabled", true);
                alertify.warning(res.message, 'Error', 5);
                drawAdminTables();
                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 5000)
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 5000)
                }
            }
            if (res.status == true || res.success == true) {

                $(modalId).modal('hide');
                alertify.success(res.message);


                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 1200)
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 800)
                }
                drawAdminTables();
            }

        },
        error: function (data) {
            if (data.status == 422) {
                $.each(data.responseJSON.errors, function (key, value) {
                    var $inputElement = $(form).find('[name="' + key + '"]');
                    if ($inputElement.hasClass('select2')) {
                        $inputElement.next('.select2-container').after('<div class="invalid-feedback d-block">' + value + '</div>');
                    } else {
                        // Apply validation styling to regular input elements
                        $inputElement.addClass('is-invalid');
                        $inputElement.after('<div class="invalid-feedback">' + value + '</div>');
                    }
                });
            }
            else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }
    }).done(function () {
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}

function adminModalZoomSubmit(form, modalId = "common-admin-modal") {
    event.preventDefault();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    // for (instance in CKEDITOR.instances) {
    //     CKEDITOR.instances[instance].updateElement();
    // }
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {

            if (res.status == false) {
                $(form).find('button .save-loader').show();
                $(form).find('button').prop("disabled", true);
                alertify.warning(res.message, 'Error', 5);
                drawAdminTables();
                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 5000)
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 5000)
                }
            }
            if (res.status == true || res.success == true) {

                $(modalId).modal('hide');
                alertify.success(res.message);

                drawAdminTables();
                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 1200)
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 800)
                }
            }

        },
        error: function (data) {
            if (data.status == 422) {
                $.each(data.responseJSON.errors, function (key, value) {
                    var $inputElement = $(form).find('[name="' + key + '"]');
                    if ($inputElement.hasClass('select2')) {
                        $inputElement.next('.select2-container').after('<div class="invalid-feedback d-block">' + value + '</div>');
                    } else {
                        // Apply validation styling to regular input elements
                        $inputElement.addClass('is-invalid');
                        $inputElement.after('<div class="invalid-feedback">' + value + '</div>');
                    }
                });
            }
            else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }
    }).done(function () {
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}


async function deleteRow(url) {


    const confirmation = await swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: true
    });

    if (confirmation.value) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "DELETE",
                dataType: "JSON",
                data: {}
            });

            if (response.status) {
                alertify.success(response.message);
                if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
                    dataTable.draw();
                }
                if (response.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 1200)
                }
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            console.error("Error:", error);
        }
    }
}


function adminFormSubmit(form) {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    let formData = new FormData(form);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            }

            alertify.success(res.message);

            if (res.resetForm) {
                form.reset();
                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].setData('');
                }
            }

            if (res.reload) {
                window.setTimeout(function () {
                    location.reload();
                }, 1200)
            }

            if (res.rurl) {
                window.setTimeout(function () {
                    window.location = res.rurl
                }, 800)
            }
        },
        error: function (data) {
            if (data.status == 422) {
                $.each(data.responseJSON.errors, function (key, value) {

                    if (key.includes('.')) {
                        var fieldName = key.split('.')[0]; // Get the field name before '.'
                        var fieldIndex = key.split('.')[1]; // Get the field index after '.'
                        var inputFields = $(form).find('[name^="' + fieldName + '["]').eq(fieldIndex);
                    } else {
                        var inputFields = $(form).find('[name="' + key + '"]');
                    }
                    if (inputFields.hasClass('select2')) {
                        inputFields.next('.select2-container').after('<div class="invalid-feedback d-block">' + value + '</div>');
                    } else {
                        inputFields.addClass('is-invalid');
                        inputFields.after('<div class="invalid-feedback">' + value + '</div>');
                    }

                    inputFields.focus();

                });
            }
            else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }
    }).done(function () {
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}

async function postAdminWithConfirmation(url, formid = null) {
    let formData = {};

    if (formid) {
        const anyCheckboxChecked = $(`input[form="${formid}"][type="checkbox"]:checked`).length > 0;

        if (anyCheckboxChecked) {
            formData = $(`#${formid}`).serialize();
        } else {
            alertify.warning('Please select at least one class');
            return false;
        }
    }

    const confirmation = await swal({
        title: "Confirm?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes!",
        cancelButtonText: "No!",
        reverseButtons: true
    });

    if (confirmation.value) {
        postAdminData(url, formData);
    }
}


function postAdminData(url, formData = {}) {

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        type: "POST",
        data: formData,
        dataType: "json",
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            } else if (res.status == true) {

                alertify.success(res.message);
                drawAdminTables();

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl;
                    }, 1200)
                }

                if (res.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 1200)
                }
            }

        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });
}

function getDependentData(e, url) {
    const replaceHtmlId = e.dataset.selectTarget;
    const replaceHtmlParent = e.dataset.selectTargetParent;
    const selectedId = $(e).val();
    if (selectedId == '') {
        if (replaceHtmlParent) {

            $(replaceHtmlParent).hide();
        }
        $(replaceHtmlId).html('');
        return false;
    }
    var modifiedUrl = url.replace(/\/id$/, '/' + selectedId);

    $.ajax({
        url: modifiedUrl,
        method: "GET",
        dataType: "json",
        data: {},
        success: function (res) {

            if (res.status == true) {

                if (replaceHtmlParent) {

                    $(replaceHtmlParent).show();
                }
                $(replaceHtmlId).html(res.view);
                // document.startViewTransition(() => {

                //     if (replaceHtmlParent) {

                //         $(replaceHtmlParent).show();
                //     }
                //     $(replaceHtmlId).html(res.view);
                // });
            }

        },
    })
}

function filterFormSubmission(event, modal = '#FilterModal') {
    event.preventDefault();
    drawAdminTables();
    $(modal).modal('hide');
}

function filterFormReset(event, modal = '#FilterModal') {
    event.preventDefault();
    $(event.target).closest('form')[0].reset();
    // Reset Select2
    $(event.target).closest('form').find('select.select2').val(null).trigger('change');

    // Reset DateRangePicker
    $(event.target).closest('form').find('input.daterangepicker').val('');
    drawAdminTables();
    // $(modal).modal('hide');
}

function filterFormResetSubmit(event, modal = '#FilterModal',url) {
    event.preventDefault();
    $(event.target).closest('form')[0].reset();
    // Reset Select2
    $(event.target).closest('form').find('select.select2').val(null).trigger('change');

    // Reset DateRangePicker
    $(event.target).closest('form').find('input.daterangepicker').val('');
    $(modal).modal('hide');
    window.location.href = url;
}

function filterFormResetBtn(event, modal = '#FilterModal') {
    // event.preventDefault();
    $(event.target).closest('form')[0].reset();
    drawAdminTables();
    $(modal).modal('hide');
}

function filterSearchInstructors(form, modal = '#searchFilterModal') {


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        success: function (res) {
            if (res.status == true) {
                $('#user_id').html(res.view);
            }

        },

    });



    $(modal).modal('hide');
}

function filterSearchFormReset(elm, modal = '#searchFilterModal') {

    $('#filter-form')[0].reset();
    $(modal).modal('hide');
}

function getProgramUsers(elm, url) {
    const admin_type = $(elm).val();

    if (admin_type == '') {

        $('#user_id').html('');
        return false;
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        type: 'GET',
        data: {
            admin_type: admin_type,
        },
        dataType: "json",
        success: function (res) {
            if (res.status == true) {
                $('#user_id').html(res.view);
            }

        },

    });
}

let addMoreClicked = false;
function adminAddMore(e, url, count = 0, boxClass) {

    if (count > 0 && boxClass && $('.' + boxClass).length > count) {
        alertify.error('Max limit reached for Adding', 'Error');
        return;

    }


    if (addMoreClicked) {
        return;
    }
    addMoreClicked = true;

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        method: "GET",
        dataType: "JSON",
        data: {},
        success: function (res) {

            if ($(e)) {
                $(e).closest('div.row.' + boxClass).parent().append(res.view);
                initdateSchedulePickers();
                initializeBootstrapDatepicker();
            }



            addMoreClicked = false;

        }
    });
}

function removeDuplicate(e) {
    if ($(e)) {
        $(e).closest('div.row').remove();
    }

    // document.startViewTransition(() => {

    // });

}

function sendInvite() {

    window.setTimeout(function () {
        alertify.success('Invite sent successfully');
    }, 800)
}

function initializeAdminDataTableInvite(selector, url, columns, searching = true) {
    return $(selector).DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'get',
        'autoWidth': true,
        'searching': searching,
        'ordering': true,
        "lengthMenu": [[5, 10, 25, 50, 100], [5, 10, 25, 50, 100]],
        'drawCallback': function (settings) {
            var table = $(this).DataTable();
            var showPagination = table.page.info().recordsTotal > 5;
            $(selector + '_paginate').toggle(showPagination);
        },
        'ajax': {
            'headers': {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            'url': url,
            'data': function (data) {

                if ($('#assigntoinstructor').length) {
                    var filterData = $('#assigntoinstructor').serializeArray();

                    $.each(filterData, function (index, item) {
                        if (data[item.name] === undefined) {
                            data[item.name] = item.value;
                        } else {
                            if (!Array.isArray(data[item.name])) {
                                data[item.name] = [data[item.name]];
                            }
                            data[item.name].push(item.value);
                        }
                    });
                }

            }

        },
        'columns': columns,
        'order': [
            [0, 'desc']
        ]
    });

}

function openAdminModalIfCheckedCommon(formid, route, modalSelector = "#common-admin-modal-lg") {
    // Check if at least one checkbox is checked
    var anyCheckboxChecked = $('input[form="' + formid + '"][type="checkbox"]:checked').length;

    if (anyCheckboxChecked > 0) {
        if (anyCheckboxChecked > 1) {
            alertify.warning('Please select only one program');
        } else {
            let formData = $('#' + formid).serialize();
            openAdminModal(route, modalSelector, formData);
        }

        // Open the modal if at least one checkbox is checked

    } else {
        alertify.warning('Please select one program');
    }
}
function openAdminModalmd(url, modalId = "#common-admin-modal", data = {}) {

    $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeSelect2();
            adminDataTable();
        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });

}

$('#instructor-excel-form').on('submit', function (event) {
    event.preventDefault();
    if ($('#filter-form').length) {
        var filterData = $('#filter-form').serialize();

        var exportForm = $('<form></form>');
        exportForm.attr('action', $(this).attr('action'));
        exportForm.attr('method', $(this).attr('method'));
        exportForm.append('<input type="hidden" name="filter_data" value="' + filterData + '">');
        $(document.body).append(exportForm);
        exportForm.submit();
        $(document.body).remove(exportForm);
    }
});

function initializeBootstrapDatepicker() {
    $(".removedate-datepicker").each(function () {
        var $datepicker = $(this);
        var currentDateString = $datepicker.data('date-now-date');
        var currentDateParts = currentDateString.split('-');
        var currentDate = new Date(currentDateParts[2], currentDateParts[0] - 1, currentDateParts[1]);
        var endDateString = $datepicker.data('date-end-date');
        var endDateParts = endDateString.split('-');
        var endDate = new Date(endDateParts[2], endDateParts[0] - 1, endDateParts[1]);

        $datepicker.attr("placeholder", "MM/DD/YYYY");

        $datepicker.datepicker({
            format: "yyyy-mm-dd",
            startDate: currentDate,
            endDate: endDate,
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });
}

function changePaymentoption(checkbox)
{
    if (checkbox.checked) {
        $htmlForm = `<input type="text" name="description" id="description" placeholder="Enter description" class="form-control">`;
        $('#check_description').html($htmlForm);
    } else {
        $('#check_description').html('');
    }
}

function openAdminModalConfirm(url, modalId = "#common-admin-modal-lg", data = {})
{
    let checkedValues = $('.payment-checkbox:checked').map(function() {
        return $(this).val();
    }).get();
    let userEmail = $('#email').val();
    let contractor_id = $('#contractor_id').val();

    data = {
        checkedValues: checkedValues,
        userEmail: userEmail,
        contractor_id: contractor_id
    };

    $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeSelect2();
            // adminDataTable();
        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });
}
function add_setting(type)
{
    let html = `<div class="col-md-4 form-group d-flex add-setting-after" style="gap:10px;">
                    <input type="text" id="${type}" maxlength="100"  class="form-control" name="${type}[]">
                    <a class="btn btn-danger" onclick="remove_setting(this)">-</a>
                </div>`;

    document.getElementById('settingsContainer').insertAdjacentHTML('beforeend', html);
}

function remove_setting(element)
{
    $(element).parents(".add-setting-after").remove();
}

function add_certificates()
{
    let html = `<div class="col-md-4 form-group d-flex add-setting-after" style="gap:10px;">
                    <input type="text" maxlength="100"  class="form-control" name="certificates[]">
                    <a class="btn btn-danger" onclick="remove_certificates(this)">-</a>
                </div>`;

    document.getElementById('settingsContainer').insertAdjacentHTML('beforeend', html);
}

function remove_certificates(element)
{
    $(element).parents(".add-setting-after").remove();
}

$('body').on('click', '.delete_agency', function () {
    var id = $(this).attr("data-id");

    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-agency';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})

$('body').on('click', '.agency_certificate_delete', function (e) {
    e.preventDefault();
    currentRow = $(this).closest('.certificate-container');
    var id = $(this).attr("data-id");
    var value = $(this).attr("data-value");
    var url = $(this).attr("data-url");
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            $.ajax({
                type: 'Post',
                url: url,
                data: {
                    id: id,
                    value: value,
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
});
//from here i'm inserting the subcategories
function add_subcategory(){
    let html = `<div class="col-md-4 form-group my-3 d-flex sub_category_div" style="gap:10px;">
              <input type="text" maxlength="100"  class="form-control" name="subcategory[]" required>
             <a class="btn btn-danger" onclick="remove_subcategory(this)">-</a>
            </div>`;

    document.getElementById('sub_categories_div').insertAdjacentHTML('beforeend', html);

}
function remove_subcategory(element) {
    const parentDiv = element.closest('.sub_category_div');
    if (parentDiv) {
        parentDiv.remove();
    }
}

//for the deletion os subcategory
$(document).on("click",".delete_subcategory",function(e){




    e.preventDefault();
    var id = $(this).data("id");
    currentRow = $(this).closest('.sub_category_div');
    
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            $.ajax({
                type: 'Get',
                url:APP_URL + "/delete_existing_sub_Category",
                data:{
                    id:id,

                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    
                    currentRow.remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })



})

//For the deletion of the category
$(document).on("click",".delete_category",function(e){

    
    e.preventDefault();
    var id=$(this).data("id");
    currentRow = $(this).closest('.category_div');
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            $.ajax({
                type: 'Get',
                url:APP_URL + "/delete_existing_category",
                data:{
                    id:id,

                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    
                    currentRow.remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })











});



$('body').on('click', '.school_setting_delete', function (e) {
    e.preventDefault();
    var id = $(this).attr("data-id");
    currentRow = $(this).closest('.col-md-4');
    var url = $(this).attr("data-url");
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            $.ajax({
                type: 'Get',
                url: url,
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
});

$('body').on('click', '.agency_certificate_delete', function (e) {
    e.preventDefault();
    var id = $(this).attr("data-id");
    currentRow = $(this).closest('.col-md-6');
    var url = $(this).attr("data-url");
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            $.ajax({
                type: 'POST',
                url: url,
                dataType: "json",
                data: {
                    id: id,
                },
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
});

$('#school-filter-form').on('submit', function(){
    let url = $(this).data('url');

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        type: 'GET',
        url: url,
        data: $(this).serialize(),
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (response) {
            $(".loader").addClass('d-none');
            if (response.success == true) {
                $('#requirements-table').html(response.view);
                $('.admin-dataTable').DataTable({
                    searching: true
                });
                $('#SchoolFilterModal').modal('hide');
            } else {
                console.log('error');
            }
        },
        error: function (xhr, status, error) {
            $(".loader").addClass('d-none'); // Hide loader
            console.log(error);

        }
    });
});

$('body').on('change', '#is_background_check', function(){
    let isChecked = 0;
    let userId = $(this).data('user-id');
    if ($('#is_background_check').is(':checked')) {
        isChecked = 1;
    }
    let url = APP_URL + '/admin/k12connections/update_user_background';

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        type: 'POST',
        url: url,
        data: {
            isChecked: isChecked,
            userId: userId
        },
        dataType: "json",
        success: function (response) {
            if (response.success) {
                console.log('Background Checked');
            } else {
                console.log('error');
            }
        },
        error: function (xhr, status, error) {
            console.log(error);

        }
    });
});

function importSubjectCsv()
{
    $('#subject_csv_file').click();
}

$('body').on('change', '#subject_csv_file', function() {
    const file = this.files[0];
    if (!file) return; // If no file selected

    const validExtensions = ['csv', 'xls', 'xlsx'];
    const fileName = file.name;
    const fileExtension = fileName.split('.').pop().toLowerCase();

    if (!validExtensions.includes(fileExtension)) {
        // alert('Only CSV or Excel files (.csv, .xls, .xlsx) are allowed.');
        alertify.error('Only CSV or Excel files (.csv, .xls, .xlsx) are allowed.');
        $(this).val(''); // Clear the input
        return;
    }

    // If valid, submit the form
    $(this).closest('form').submit();
});

// $('body').on('input', '.basePayBOI, .basePayBOE, .basePaySpecialEdu, .basePayNonTech, .basePay', function (e) {
//     if (e.which === 13) {
//         e.preventDefault();
//     }
//     var $row = $(this).closest('.row');
//     let basePayBOI = parseFloat($row.find('.basePayBOI').val()) || 0;
//     let basePayBOE = parseFloat($row.find('.basePayBOE').val()) || 0;
//     let basePaySpecialEdu = parseFloat($row.find('.basePaySpecialEdu').val()) || 0;
//     let basePayNonTech = parseFloat($row.find('.basePayNonTech').val()) || 0;
//     let basePay = parseFloat($row.find('.basePay').val()) || 0;

//     let total = basePayBOI + basePayBOE + basePaySpecialEdu + basePayNonTech + basePay;

//     // Update the basePayTotal field with the calculated total (2 decimal places)
//     $row.find('.basePayTotal').val(total.toFixed(2));
// });

// $(document).on("input", ".budget_input", function() {
   
//     let total = 0;

//     // Loop through all inputs with class 'budget_input' and type 'number'
//     $(".budget_input[type='number']").each(function() {
//         const value = parseFloat($(this).val());
//         if (!isNaN(value)) {
//             total += value;
//         }
//     });

//     // Set the total in the element with id 'total_budget'
//    $("#total_budget").val(total.toFixed(2));

// });

