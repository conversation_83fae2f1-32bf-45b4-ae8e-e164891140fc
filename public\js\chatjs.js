function getTimestamp(time) {
  var now = new Date();
  var messageTime = new Date(time); // Replace with the actual message send time
  var timeDifference = now - messageTime;

  var minutes = Math.floor(timeDifference / 60000); // 1 minute = 60000 milliseconds

  if (minutes < 1) {
    return 'Just now';
  } else if (minutes < 60) {
    // Show minutes if the message was sent within the last hour
    return minutes + ' min ago';
  } else {
    var hours = Math.floor(minutes / 60);
    if (hours < 24) {
      // Show hours if the message was sent within the last day
      return hours + ' hours ago';
    } else {
      // Show days if the message was sent more than a day ago
      var days = Math.floor(hours / 24);
      return days + ' days ago';
    }
  }
}




$('body').on('click','.userclass',function(){
  var id=$(this).attr('data-id');
  var name=$(this).attr('data-name');
  var email=$(this).attr('data-email');
  var img=$(this).attr('data-img');
  var from_id=$('#from_id').val();
  var to_id=$(this).attr('data-id');
  $('#to_id').val(to_id);
  var authname=  $('#authname').val();
   var name=$(this).attr('data-name');
    var img=$(this).attr('data-img');
    $('#to_name').val($(this).attr('data-name'));
    $('#img').val($(this).attr('data-img'));
  
  $(this).find('.right').removeClass('badge-success').text(' ');
  var headerhtml='<header><img src="'+img+'" alt="" id="imgid" height="50px" width="50px"><div><h2 id="nameid">'+name+'</h2></div></header>';
  $('#chat').prev().remove();;
  $('#chat').before(headerhtml);

  $('#send-container').removeAttr('style');
  $('#chat').html('');
  $('#email').val(email);
  db.ref('Chats/'+from_id+'_'+to_id).on("child_added", function(res){
  
 
       var  msg='';
        if(from_id==res.val().from_userid){
        msg+='<li class="me msg"><div class="entete"><span class="status green"></span> <h2>'+authname+'</h2><br><h3>'+getTimestamp(res.val().time)+'</h3></div><div class="message">'+res.val().message+'</div></li>';
        }else{
        msg+='<li class="you  msg cli"><div class="entete"><span class="status blue"></span> <h2>'+name+'</h2><br><h3>'+getTimestamp(res.val().time)+'</h3></div><div class="message">'+res.val().message+'</div></li>';
        }

        $('#chat').append(msg);
  
        $('#chat li').removeClass('last-appended');
        $('#chat li').addClass('last-appended');
       
         $('#chat').scrollTop($('#chat')[0].scrollHeight);
        $('#chat li').each(function() {
        var imgElement = $(this).find('img');
        imgElement.remove();
        });
        
    });
 
    $("#chat li:last-child").addClass("last");
    $('#message').focus(); 

})


   $(document).ready(function() {
      $('#message').on('keypress', function(e) {
        // Check if the pressed key is Enter (key code 13)
        if (e.which === 13) {
            var message=$('#message').val();
            var from_id=$('#from_id').val();
            var to_id=$('#to_id').val();
            var email=$('#email').val();
            var authname=$('#authname').val();
            var from_img=$('#from_img').val();
            var from_email=$('#from_email').val();
            
            
            const d = new Date();
            var date=" "+d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate()+" "+d.getHours()+":"+d.getMinutes()+":"+d.getSeconds();
            var message=document.getElementById('message').value;
                   var p = {
                                           message:message,
                                           from_userid:from_id,
                                           to_userid:to_id,
                                           time:date
                                           
                                        };
            dbRef.child('Chats/'+from_id+'_'+to_id+'/').push(p);
            dbRef.child('Chats/'+to_id+'_'+from_id+'/').push(p);
            
            var name=$('#to_name').val();
            var img=$('#img').val();
            
            $('#chat').scrollTop($('#chat')[0].scrollHeight);
                      
            
            lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email)
                      $.ajaxSetup({
                          headers: {
                              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                          }
                        });
                      var url = APP_URL + '/' + 'sendMsgs';
                        $.ajax({
                          type: 'POST',
                          url: url,
                          data:$('#send-container').serialize(),
                          success: function (data) { 
                          },
                        })     
                      $('#message').val(' ');
          // Prevent the default form submission
          e.preventDefault();
        }
      });
    });

function lastmessageupdate(from_id,to_id,email,message,date,name,img,from_image,from_name,from_email){

      var newData={
          email: email,
          message:message,
          from_userid:from_id,
          to_userid:to_id,
          time:date,
          to_name:name,
          img:img,
          from_image:from_image,
          from_name:from_name,
          from_email:from_email
    };

    var dbRef = firebase.database().ref('last_message/'+to_id+'/');
 
   dbRef.child(from_id).once('value', function(snapshot) {
      if (snapshot.exists()) {
        // Key exists, update the data
        dbRef.child(from_id).update(newData, function(error) {
          if (error) {
          
          } else {
          
          }
        });
      } else {
        // Key does not exist, push the data with the custom key
        dbRef.child(from_id).set(newData, function(error) {
          if (error) {
         
          } else {
           
          }
        });
      }
    });
  
    }
    function convertToUTC(localDate) {
      // Get the local time zone offset in minutes
      var localOffset = localDate.getTimezoneOffset();
  
      // Get the UTC timestamp by subtracting the local time zone offset
      var utcTimestamp = localDate.getTime() - (localOffset * 60000);
  
      // Create a new Date object using the UTC timestamp
      var utcDate = new Date(utcTimestamp);
  
      // Return the UTC Date object
      return utcDate;
  }

    $('body').on('click','#sendmsg',function(){
      var message=$('#message').val();
      var from_id=$('#from_id').val();
      var to_id=$('#to_id').val();
      var email=$('#email').val();
      var authname=$('#authname').val();
      var from_img=$('#from_img').val();
      var from_email=$('#from_email').val();
    
      
      const d = new Date();
      // var date=" "+d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate()+" "+d.getHours()+":"+d.getMinutes()+":"+d.getSeconds();
      var date = new Date().toISOString()
      var message=document.getElementById('message').value;
             var p = {
                                     message:message,
                                     from_userid:from_id,
                                     to_userid:to_id,
                                     time:date
                                     
                                  };
      dbRef.child('Chats/'+from_id+'_'+to_id+'/').push(p);
      dbRef.child('Chats/'+to_id+'_'+from_id+'/').push(p);
      
      var name=$('#to_name').val();
      var img=$('#img').val();
      
      lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email)
      $('#chat').scrollTop($('#chat')[0].scrollHeight);
   
      $.ajaxSetup({
          headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          }
        });
      var url = APP_URL + '/' + 'sendMsgs';
        $.ajax({
          type: 'POST',
          url: url,
          data:$('#send-container').serialize(),
          success: function (data) { 
          },
        })     
        $('#message').val(' ');

      })



      $( document ).ready(function() {
          
          var from_id=$('#from_id').val();
      
          var authname=  $('#authname').val();
           //---------------------//
           var msageRef = firebase.database().ref('online_user/'+from_id);
             msageRef.update({
                         user_id: from_id,
                         status: 'online'
                         
             });
             
              msageRef.onDisconnect().update({
                         user_id: from_id,
                         status: 'offline'
                        
             });
             var msageRefonline = firebase.database().ref('online_user');
               msageRefonline.once('value').then(function(snapshots) {
                   if (snapshots.exists()) {
                       const data  = snapshots.val();
                      
                       for (const key in data) {
                           if (data.hasOwnProperty(key)) {
                             if(data[key].status=='online'){
                             $('.user'+data[key].user_id).append('<span class="status green"></span>');
                             }
                           }
                         }
                      
                   }
               })
           //---------------------//
               var to_id=$('#to_id').val(); 
               var img=$('#img').val();  
               var to_name=$('#to_name').val();  
               $('#send-container').removeAttr('style');
              
                  
                   var headerhtml='<header><img src="'+img+'" alt="" id="imgid" height="50px" width="50px"><div><h2 id="nameid">'+to_name+'</h2></div></header>';
                   $('#chat').prev().remove();;
                   $('#chat').before(headerhtml);
                    $('#chat').html('');
                 
                   db.ref('Chats/'+from_id+'_'+to_id).on("child_added", function(res){
                      var name=$('#to_name').val();
                   
                        //var  msg='';
                         if(from_id==res.val().from_userid){
                         var msg ='<li class="me msg"><div class="entete"><span class="status green"></span> <h2>'+authname+'</h2><br><h3>'+getTimestamp(res.val().time)+'</h3></div><div class="message">'+res.val().message+'</div></li>';
                         }else{
                         var msg ='<li class="you  msg notc"><div class="entete"><span class="status blue"></span> <h2>'+name+'</h2><br><h3>'+getTimestamp(res.val().time)+'</h3></div><div class="message">'+res.val().message+'</div></li>';
                         }
                         $('#chat').append(msg);
                         $('#chat li').removeClass('last-appended');
                         $('#chat li').addClass('last-appended');
                        
                          $('#chat').scrollTop($('#chat')[0].scrollHeight);
                           $('#chat li').each(function() {
                             var imgElement = $(this).find('img');
                             imgElement.remove();
                             });
                         
                     });

               });
            
         
         