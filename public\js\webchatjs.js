function getTimestamp(time) {
  var now = new Date();
  var messageTime = new Date(time); // Replace with the actual message send time
  var timeDifference = now - messageTime;

  var minutes = Math.floor(timeDifference / 60000); // 1 minute = 60000 milliseconds

  if (minutes < 1) {
    return 'Just now';
  } else if (minutes < 60) {
    // Show minutes if the message was sent within the last hour
    return minutes + ' min ago';
  } else {
    var hours = Math.floor(minutes / 60);
    if (hours < 24) {
      // Show hours if the message was sent within the last day
      return hours + ' hours ago';
    } else {
      // Show days if the message was sent more than a day ago
      var days = Math.floor(hours / 24);
      return days + ' days ago';
    }
  }
}
$('body').on('click','.userclass',function(){

  // db.ref('Chats/' + from_id + '_' + to_id).off("child_added");
  var id=$(this).attr('data-id');
  var name=$(this).attr('data-name');
  var email=$(this).attr('data-email');
  var img=$(this).attr('data-img');
  var from_id=$('#from_id').val();
  var to_id=$(this).attr('data-id');
  var from_img=$('#from_image').val();
  $('#to_id').val(to_id);
  var authname=  $('#authname').val();
  var name=$(this).attr('data-name');
  var img=$(this).attr('data-img');
  var message=$(this).find('.preview').text();
   
  $('#to_name').val($(this).attr('data-name'));
  $('#email').val(email);
  $('#img').val($(this).attr('data-img'));

  var chatsRef = db.ref('Chats/' + from_id + '_' + to_id).off("child_added");
  $(this).find('.right').removeClass('badge-success').text(' ');

  $('#send-container').removeAttr('style');
  $('#chat').html('');
  
  var userhtml='<div class="dashboard__meessage__profile__img"><img loading="lazy" src="'+img+'" alt=""></div><div class="dashboard__meessage__profile__meta d-flex justify-content-between align-items-center" ><div class="text"><h5>'+name+'</h5></div><div class="icon " data-fromid="'+from_id+'" data-toid="'+to_id+'"></div></div>';
         
  $('.topuser').html(userhtml);
 //////------------------remove topuser----------------//
 
  $("#chat li:last-child").addClass("last");
  $('#message').focus(); 
  $('#chat').attr('class','');
  $('#chat').addClass('chat'+id)
  firebasechaton(from_id,to_id,from_img,img)

})
///firebase chat on////

function firebasechaton(from_id,to_id,from_img,img){
            db.ref('Chats/' + from_id + '_' + to_id).on("child_added", function(res) {
            
              var messages = '';
              var requirement = '';

              if (typeof res.val().refranceId !== 'undefined' && res.val().refranceId !== null) {
                  var refranceId = res.val().refranceId;
                  const id = `message_${refranceId}_${res.key}`;
                  if (res.val().message) {
                    messages = `<p id="${id}">`;
                    messages += res.val().message;
                    messages += '</p>';
                }

                  $.ajax({
                      url: '/fetch-requirement/' + refranceId,
                      method: 'GET',
                      success: function(response) {
                          var referenceName = response.name || 'Unknown User';
                          
                          requirement = '<span class="reference-id">' + referenceName + '</span> ';
                          const elem = document.getElementById(id);
                          elem.innerHTML = `${requirement}${res.val().message}`;
                      },
                      error: function(xhr, status, error) {
                          console.error('Error fetching requirement:', error);
                      }
                  });
              } else {
                  if (res.val().message) {
                      messages = '<p>';
                      messages += res.val().message;
                      messages += '</p>';
                  }
              }
              
            
                if(res.val().imageUrl!=undefined){
                  var htmlTag;
                  var url=res.val().imageUrl;
                  var urlWithoutParams = url.split('?')[0];

                  // Extract the file name from the URL without parameters
                  var fileName = urlWithoutParams.substring(urlWithoutParams.lastIndexOf('/') + 1);
                  
                  // Extract the file extension from the file name
                  var fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);
                  switch (fileExtension) {
                    case 'jpg':
                    case 'jpeg':
                    case 'png':
                      htmlTag = '<img src="' + res.val().imageUrl + '" alt="Preview Image" width="100px" height="100px">';
                      break;
                    case 'pdf':
                      htmlTag = '<embed src="' + res.val().imageUrl + '" type="application/pdf" width="100px" height="100px">';
                      break;
                    case 'txt':
                      htmlTag = '<iframe src="' + res.val().imageUrl + '" width="100px" height="100px"></iframe>';
                      break;
                    default:
                      htmlTag = '<p>File type not supported for preview.</p>';
                      break;
                  }
                
                  var msg = '';
                  if (from_id == res.val().from_userid) {
                    msg += '<li class="dashboard__meessage__sent__item"><div class="dashboard__meessage__sent__item__content">'+messages+''+htmlTag+'<span class="time">' + getTimestamp(res.val().time) + '</span></div><div class="dashboard__meessage__sent__item__img"><img loading="lazy" src="' + from_img + '" alt=""></div></li>';
                } else {
                    msg += '<li class="sent"><div class="dashboard__meessage__sent__item__img"><img loading="lazy" src="' + img + '" alt=""></div><div class="dashboard__meessage__sent__item__content">'+messages+''+htmlTag+'<span class="time">' + getTimestamp(res.val().time) + '</span></div></li>';
                }
                }else{
                  var msg = '';
                  if (from_id == res.val().from_userid) {
                      msg += '<li class="dashboard__meessage__sent__item"><div class="dashboard__meessage__sent__item__content">'+messages+'<span class="time">' + getTimestamp(res.val().time) + '</span></div><div class="dashboard__meessage__sent__item__img"><img loading="lazy" src="' + from_img + '" alt=""></div></li>';
                  } else {
                      msg += '<li class="sent"><div class="dashboard__meessage__sent__item__img"><img loading="lazy" src="' + img + '" alt=""></div><div class="dashboard__meessage__sent__item__content">'+messages+'<span class="time">' + getTimestamp(res.val().time) + '</span></div></li>';
                  }
                }
                
                
                  $('.chat'+to_id).append(msg);
                  $('.dashboard__meessage__sent').scrollTop($('.dashboard__meessage__sent')[0].scrollHeight);
              })
}
///////////////////ready function ---------------------//
$(document).ready(function() {
  var pathArray = window.location.pathname.split('/');
  if (pathArray[1] == 'messages') {
  $('#sidebaruser li:first').click();
  var from_id = $('#from_id').val();


  var support_id=$('#support_id').val();
  if(support_id){
    supportwelcomemessage();
  }
  
  
  
  getLastChatMessage('last_message/' + from_id + '/');



  var authname = $('#authname').val();
  //---------------------//
  var msageRef = firebase.database().ref('online_user/' + from_id);
  msageRef.update({
    user_id: from_id,
    status: 'online'
  });

  msageRef.onDisconnect().update({
    user_id: from_id,
    status: 'offline'
  });
  //---------------------//

 
  $('#send-container').removeAttr('style');
  $('#chat').html('');
 

  var onlineUsersRef = firebase.database().ref('online_user');

  onlineUsersRef.on('value', function(snapshot) {
  // Handle the snapshot to get the online users
  var onlineUsers = [];

  snapshot.forEach(function(childSnapshot) {
      var userId = childSnapshot.key;
      var userData = childSnapshot.val();
      if (userData.status=='online') {
          $('.online'+userData.user_id).removeClass('dashboard__meessage__dot').addClass('dashboard__meessage__dotonline');
      }else{
          $('.online'+userData.user_id).removeClass('dashboard__meessage__dotonline').addClass('dashboard__meessage__dot');
      }
  });  

});
  }
})




function getLastChatMessage(chatname) {
              $('#sidebaruser').html('');
              var name=$('#to_name').val();
              var from_id=$('#from_id').val();
              var firstDatas;
       
              db.ref(chatname).orderByChild("time").on("value", function(res){
                  if (res.exists()) {
                    if ($('.availdata')[0].classList.contains('availdata') && $('.availdata')[0].classList.contains('displaynone')) {
                      location.reload();
                    }else{
                      $('.availdata').removeClass('displaynone')
                      $('.nodata').addClass('displaynone')
                    }
                     
                  }else{
                      $('.availdata').addClass('displaynone')
                      $('.nodata').removeClass('displaynone')
                      $('.chat').html(''); 
                  }
                  var data=res.val();
                  firebaseData = res.val();
                  var  html='';
                
                  var keycount=0;
                  for (const key in data) {
                      keycount++;
                 
                      html+='<li class="userclass user'+data[key].from_userid+'"  data-id="'+data[key].from_userid+'" data-email="'+data[key].from_email+'" data-name="'+data[key].from_name+'" data-img="'+data[key].from_image+'" data-message_type="'+(typeof data[key].message_type !== 'undefined' ? data[key].message_type : null) +'" data-refranceId="'+(typeof data[key].refranceId !== 'undefined' ? data[key].refranceId : null)+'" ><div class="dashboard__meessage__contact__wrap"><div class="dashboard__meessage__chat__img"><span class="dashboard__meessage__dot online'+data[key].from_userid+'"></span><img loading="lazy" src="'+data[key].from_image+'" alt=""></div><div class="dashboard__meessage__meta"><div><h5>'+data[key].from_name+'</h5><p class="preview">'+data[key].message+'</p></div><div><span class="chat__time">'+getTimestamp(data[key].time)+'</span></div></div></div></li>';
                     
                      if(keycount=='1'){
                         
                      firstData = data[key];
                     
                      }
                    
                      
                  }
                  $('#sidebaruser').html(html);
              

           
                  // firstDatas = firstData;
                  // var userhtml='<div class="dashboard__meessage__profile__img"><img loading="lazy" src="'+firstDatas.from_image+'" alt=""></div><div class="dashboard__meessage__profile__meta d-flex justify-content-between align-items-center"><div class="text"><h5>'+firstDatas.from_name+'</h5><p>'+firstDatas.message+'</p></div><div class="icon deletechat" data-fromid="'+firstDatas.to_userid+'" data-toid="'+firstDatas.from_userid+'"><svg width="6" height="24" viewBox="0 0 6 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6C4.7 6 6 4.7 6 3C6 1.3 4.7 0 3 0C1.3 0 0 1.3 0 3C0 4.7 1.3 6 3 6Z" fill="#004CBD"/><path d="M3 9C1.3 9 0 10.3 0 12C0 13.7 1.3 15 3 15C4.7 15 6 13.7 6 12C6 10.3 4.7 9 3 9Z" fill="#004CBD"/><path d="M3 18C1.3 18 0 19.3 0 21C0 22.7 1.3 24 3 24C4.7 24 6 22.7 6 21C6 19.3 4.7 18 3 18Z" fill="#004CBD"/></svg></div></div>';
               
                  // $('.topuser').html(userhtml);
                  // alert(userhtml) 
              
              });

            
              db.ref(chatname).once("value", function(res){
                  $('#sidebaruser li:first').click();
                  

              })


              

       
              var im= $('#sidebaruser li:first').attr('data-img');
              var name= $('#sidebaruser li:first').attr('data-name');   
              db.ref(chatname).on("value", function(res){
                  if (res.exists()) {
                      $('.availdata').removeClass('displaynone')
                      $('.nodata').addClass('displaynone')
                  }else{
                      $('.availdata').addClass('displaynone')
                      $('.nodata').removeClass('displaynone')
              
                  }  
              }) 
              
}
$(document).ready(function() {
  // Bind keypress event listener to input fields inside the form
  $('#message').keypress(function(event) {
      // Check if the Enter key is pressed
      if (event.keyCode === 13) {
          // Prevent default form submission
          event.preventDefault();
          
          // Perform AJAX form submission
          submitFormAjax();
      }
  });
});
function submitFormAjax(){
      var fileInput = $('#file');
      if (fileInput.length !== 0) {
        var file = $('#file').prop('files')[0];
      }
     
      var message = $('#message').val();
      var from_id = $('#from_id').val();
      var to_id = $('#to_id').val();
      var email = $('#email').val();
      var authname = $('#authname').val();
      var from_img = $('#from_image').val();
      var from_name = $('#authname').val();
      var from_email=$('#from_email').val();
      var name = $('#to_name').val();
      var img = $('#img').val();
      var to_instructor = $('#to_instructor').val();
      const d = new Date();
      var date = " " + d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate() + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();
      var message = document.getElementById('message').value;
      if(message==''){
return;
      }

      var message_type = $(`#sidebaruser .user${to_id}`).attr('data-message_type');
      var refId = $(`#sidebaruser .user${to_id}`).attr('data-refranceid');
      
      
      var messagesReffrom = dbRef.child('Chats/'+from_id+'_'+to_id+'/');
      var messagesRefto = dbRef.child('Chats/'+to_id+'_'+from_id+'/');
    
      if (file) {
        var fileName = file.name;
        var fileExtension = fileName.split('.').pop().toLowerCase();
        // Upload image to Firebase Storage
        var storageRef = firebase.storage().ref('files/' + file.name);
      
        var sto=storageRef.put(file);
        sto.on('state_changed', 
        function(snapshot) {
          // Handle progress
          var progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          $('.fileupload').text('Upload is ' + Math.round(progress) + '% done');
        
        }, 
        function(error) {
          // Handle errors
          console.error('Error uploading file:', error);
        }, 
        function() {
    
          $('.fileupload').text('');
          $('.preview-container').html('');
          // Handle successful upload
        
        },)
        sto.then(function(snapshot) {
          // Get the image URL after upload
        
          snapshot.ref.getDownloadURL().then(function(imageUrl) {
            // Push the message and image URL to Firebase
    
            pushfilemsg(message,from_id,to_id,date,imageUrl,from_img,img,fileExtension,refId)
            
             $('#file').val('');
    
            
          });
        });
        db.ref('Chats/' + from_id + '_' + to_id).off("child_added");
        $('#file').val(null);
        
      } else {
        messagesReffrom.push({
                                    message:message,
                                     from_userid:from_id,
                                     to_userid:to_id,
                                     message_type: 'requirements',
                                     refranceId: refId,
                                     user_type: 'marketplace',
                                     time:date
                                     
                            });
           messagesRefto.push({
                                    message:message,
                                    from_userid:from_id,
                                    to_userid:to_id,
                                    message_type: 'requirements',
                                    refranceId: refId,
                                    user_type: 'marketplace',
                                    time:date
                                    
                                 });
                                 
       }
      // dbRef.child('Chats/' + from_id + '_' + to_id + '/').push(p);
      // dbRef.child('Chats/' + to_id + '_' + from_id + '/').push(p);

     
      lastmessageupdate(from_id, to_id, email, message, date, name, img, from_img, from_name,from_email,refId,to_instructor);
    
      $('.dashboard__meessage__sent').scrollTop($('.dashboard__meessage__sent')[0].scrollHeight);

      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });

      var url = 'http://127.0.0.1:8000' + '/' + 'sendMsg';
      $.ajax({
        type: 'POST',
          url: url,
          data:$('#send-container').serialize(),
          success: function (data) { 
          },
      }) 
      $('#message').val('');
}

function pushfilemsg(message,from_id,to_id,date,imageUrl,from_img,img,fileExtension,refId){

  var p={
    message:message,
    from_userid:from_id,
    to_userid:to_id,
    time:date,
    imageUrl:imageUrl,
    message_type: 'requirements',
    refranceId: refId,
    
  };
db.ref('Chats/' + from_id + '_' + to_id).off("child_added");
dbRef.child('Chats/'+from_id+'_'+to_id+'/').push(p);
dbRef.child('Chats/'+to_id+'_'+from_id+'/').push(p);

var htmlTag;
switch (fileExtension) {
  case 'jpg':
  case 'jpeg':
  case 'png':
    htmlTag = '<img src="' + imageUrl + '" alt="Preview Image" width="100px" height="100px">';
    break;
  case 'pdf':
    htmlTag = '<embed src="' + imageUrl + '" type="application/pdf" width="100px" height="100px">';
    break;
  case 'txt':
    htmlTag = '<iframe src="' + imageUrl + '" width="100px" height="100px"></iframe>';
    break;
  default:
    htmlTag = '<p>File type not supported for preview.</p>';
    break;
}


var messages = '';
if (message) {
    messages = '<p>' + message + '</p>';
}
var msg = '<li class="dashboard__meessage__sent__item"><div class="dashboard__meessage__sent__item__content">'+messages+''+htmlTag+'<span class="time">' + getTimestamp(date) + '</span></div><div class="dashboard__meessage__sent__item__img"><img loading="lazy"  src="'+from_img+'" alt=""></li>';

$('.chat'+to_id).append(msg);
$('.dashboard__meessage__sent').scrollTop($('.dashboard__meessage__sent')[0].scrollHeight);

}

$('body').on('click','#sendmsg',function(){
  var file = ($('#file').prop('files') !== null &&  $('#file').prop('files') !== undefined) ? $('#file').prop('files')[0] : null;

  var message=$('#message').val();
  var from_id=$('#from_id').val();
  var to_id=$('#to_id').val();
  var email=$('#email').val();
  var authname=$('#authname').val();
  var from_img=$('#from_image').val();
  var from_email=$('#from_email').val();
  var img=$('#to_img').val();
  var to_instructor = $('#to_instructor').val();
  const d = new Date();
  // var date=" "+d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate()+" "+d.getHours()+":"+d.getMinutes()+":"+d.getSeconds();
  var date = new Date().toISOString()
  var message=document.getElementById('message').value;
  var refrenceId = $('#reqId').val();

if(message==''){
return;
}
  var messagesReffrom = dbRef.child('Chats/'+from_id+'_'+to_id+'/');
  var messagesRefto = dbRef.child('Chats/'+to_id+'_'+from_id+'/');

  if (file) {
  
    var fileName = file.name;
    var fileExtension = fileName.split('.').pop().toLowerCase();
    // Upload image to Firebase Storage
    var storageRef = firebase.storage().ref('files/' + file.name);
  
    var sto=storageRef.put(file);
    sto.on('state_changed', 
    function(snapshot) {
      // Handle progress
      var progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
      $('.fileupload').text('Upload is ' + Math.round(progress) + '% done');
      
    }, 
    function(error) {
      // Handle errors
      console.error('Error uploading file:', error);
    }, 
    function() {

      $('.fileupload').text('');
      $('.preview-container').html('');
      // Handle successful upload
  
    },)
    sto.then(function(snapshot) {
      // Get the image URL after upload
    
      snapshot.ref.getDownloadURL().then(function(imageUrl) {
        // Push the message and image URL to Firebase

        pushfilemsg(message,from_id,to_id,date,imageUrl,from_img,img,fileExtension,refrenceId)
        
         $('#file').val('');

         $('#file').val(null);
      });
    });

    
  } else {
   
    messagesReffrom.push({
                                message:message,
                                 from_userid:from_id,
                                 to_userid:to_id,
                                 message_type: 'requirements',
                                 refranceId: refrenceId,
                                 user_type: 'marketplace',
                                 time:date
                                 
                        });
       messagesRefto.push({
                                message:message,
                                from_userid:from_id,
                                to_userid:to_id,
                                message_type: 'requirements',
                                refranceId: refrenceId,
                                user_type: 'marketplace',
                                time:date
                                
                             });
                             
   }
  // dbRef.child('Chats/'+from_id+'_'+to_id+'/').push(p);
  // dbRef.child('Chats/'+to_id+'_'+from_id+'/').push(p);

  var name=$('#to_name').val();
 
  
  lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email,refrenceId, to_instructor)
  
  
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var url = APP_URL + '/' + 'sendMsg';
  $.ajax({
    type: 'POST',
      url: url,
      data:$('#send-container').serialize(),
      success: function (data) { 
      },
    })     
    $('#message').val('');
    $('#MessageModal').modal('hide');
    $('.dashboard__meessage__sent').scrollTop($('.dashboard__meessage__sent')[0].scrollHeight);

  })

  function lastmessageupdate(from_id,to_id,email,message,date,name,img,from_image,from_name,from_email,refId,to_instructor){
      var newData={
          email: email,
          from_email:from_email,
          message:message,
          from_userid:from_id,
          to_userid:to_id,
          time:date,
          to_name:name,
          img:img === 'undefined' ? null : img,
          from_image:from_image,
          from_name:from_name,
          message_type: 'requirements',
          refranceId: refId,
          user_type: 'marketplace',
          to_instructor: to_instructor,
    };
   
  
    var dbRef = firebase.database().ref('last_message/'+to_id+'/');
  
   dbRef.child(from_id).once('value', function(snapshot) {
      if (snapshot.exists()) {
        // Key exists, update the data
        dbRef.child(from_id).update(newData, function(error) {
          if (error) {
          
          } else {
           
          }
        });
      } else {
        // Key does not exist, push the data with the custom key
        dbRef.child(from_id).set(newData, function(error) {
          if (error) {
          
          } else {
            
          }
        });
      }
    });
  
    }


    $('body').on('keypress keyup','#search',function(){
      var name = $('#search').val();
      if (name==null || name=="") {
         
          $('#sidebaruser li').each(function() {
              $(this).show(); 
          })
      }else{
         
         filterList(name);
      }
  
  })
  
  function filterList(searchText) {
    
      // Iterate through each li element in the list
      $('#sidebaruser li').each(function() {
        var currentItemText = $(this).find('h5').text().toLowerCase(); // Get the text of the current li in lowercase
        var currentItemTexts = $(this).find('h5').text();
        // Check if the current li contains the search query
        if (currentItemText.indexOf(searchText) !== -1 || currentItemTexts.indexOf(searchText) !== -1) {
          $(this).show(); // If it does, show the li
        }
        else {
          $(this).hide(); // If it doesn't, hide the li
        }
        
      });
    
    }
  
  $('body').on('click','.deletechat',function(){
   
  var from_id=$(this).attr('data-fromid');
  var to_id=$(this).attr('data-toid');
  
  const chatRef = firebase.database().ref('Chats/' + from_id + '_' + to_id + '/');//160_1
  
      chatRef.remove()
        .then(() => {
          $('#chat li').remove();
          $(this).parents('.topuser').find('p').html('');
          $('.user'+to_id).find('.preview').html('');
        
        })
        .catch(error => {
          console.error('Error deleting chat message:', error);
        });
  
    
  })

  function supportwelcomemessage(){
    var message='Welcome to whizara.com';

    // var from_id='371';
    var from_id=$('#support_id').val();
    var from_img=$('#support_image').val();
    // var from_email='<EMAIL>';
    var from_email=$('#support_email').val();

if(from_id){

}else{
  retrun;
}
    var to_id=$('#from_id').val();
    var email=$('#from_email').val();
    var authname='Support';
   
    var name=$('#authname').val();
    var img=$('#from_image').val();
    
    const d = new Date();
    var date=" "+d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate()+" "+d.getHours()+":"+d.getMinutes()+":"+d.getSeconds();

           var p = {
                                   message:message,
                                   from_userid:from_id,
                                   to_userid:to_id,
                                   time:date
                                   
                                };
  
    $.ajaxSetup({
      headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });
    var url = APP_URL + '/' + 'sendMsgstoSupport';
    $.ajax({
      type: 'POST',
      url: url,
      data:{from_id:from_id},
      success: function (data) { 
        if(data.success==true){
        dbRef.child('Chats/'+from_id+'_'+to_id+'/').push(p);
        dbRef.child('Chats/'+to_id+'_'+from_id+'/').push(p);
        lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email)
        }
      },
    })     
    
    

  }