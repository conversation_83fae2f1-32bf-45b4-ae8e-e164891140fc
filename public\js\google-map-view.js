// Constant for element IDs
const ELEMENT_IDS = {
    searchBox: "in_person_map_search_input",
    mapContainer: "in-person-map",
    drawCirclesButton: "draw-circles",
    radius: "travel_radius",
    inputBinds: {
        latitude: "in_person_latitude",
        longitude: "in_person_longitude",
        address: "in_person_full_address",
    },
};

// Variables for map, marker, and circles
let map;
let marker;
let circle;
let searchLocation;

// Initialize the Google Map
async function initMap() {
    // Default location if geolocation fails
    const lat = $(`#${ELEMENT_IDS?.inputBinds?.latitude}`).val() || "-34.397";
    const lng = $(`#${ELEMENT_IDS?.inputBinds?.longitude}`).val() || "150.644";
    const preFetch = !!$(`#${ELEMENT_IDS?.inputBinds?.longitude}`).val();
    const defaultLocation = { lat: +lat, lng: +lng };
    searchLocation = defaultLocation;

    // Initialize the map
    const { Map } = await google.maps.importLibrary("maps");
    const input = document.getElementById(ELEMENT_IDS.searchBox);
    // Short namespaces can be used.
    window["inperson-map"] = new Map(document.getElementById("in-person-map"), {
        center: defaultLocation,
        zoom: 15,
    });
    map = window["inperson-map"];

    // Initialize the search box
    const searchBox = new google.maps.places.SearchBox(input);

    // Bias the search results towards the map's current viewport
    map.addListener("bounds_changed", () => {
        searchBox.setBounds(map.getBounds());
    });
    input.addEventListener('blur', () => {
        if (!input.placeSelected) {
            input.value = '';
        }
        input.placeSelected = false;
    });

    input.addEventListener('input', () => {
        input.placeSelected = false;
    });

    // Handle place selection from the search box
    searchBox.addListener("places_changed", () => {
        const places = searchBox.getPlaces();
        if (places.length === 0) return;
        input.placeSelected = true;

        const place = places[0];
        if (!place.geometry || !place.geometry.location) {
            alert("Place details not available.");
            return;
        }

        // Set search location and update the map
        searchLocation = place.geometry.location;
        map.setCenter(searchLocation);
        map.setZoom(15);

        // Add or move the marker
        if (!marker) {
            marker = new google.maps.Marker({
                map: map,
            });
        }
        marker.setPosition(searchLocation);
        marker.setTitle(place.name);
        drawCircles(searchLocation);
    });

    // Fallback to user's current location
    if(preFetch){
        marker = new google.maps.Marker({
            position: searchLocation,
            map: map,
            title: input.value
        });
        drawCircles(searchLocation);
    } else if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude,
                };

                map.setCenter(userLocation);
                marker = new google.maps.Marker({
                    position: userLocation,
                    map: map,
                });
                searchLocation = userLocation; // Set as default search location
            },
            () => handleLocationError(true, map.getCenter())
        );
    } else {
        handleLocationError(false, map.getCenter());
    }

    // Function to handle the map click event and get place name
    map.addListener("click", (event) => {
        const clickedLocation = event.latLng;

        // Reverse geocode the clicked location to get the place details
        const geocoder = new google.maps.Geocoder();
        geocoder.geocode({ location: clickedLocation }, (results, status) => {
            if (status === "OK") {
                if (results[0]) {
                    // Get the place name from the geocoding result
                    const placeName = results[0].formatted_address; // You can customize to use specific parts of the address
                    console.log("Place Name:", placeName); // Display place name
                    searchLocation = clickedLocation;
                    // Create or update the marker at the clicked location
                    if (!marker) {
                        marker = new google.maps.Marker({
                            map: map,
                            position: clickedLocation,
                            title: placeName,
                        });
                    } else {
                        marker.setPosition(clickedLocation);
                        marker.setTitle(placeName);
                    }
                    drawCircles(searchLocation);
                } else {
                    alert("No results found.");
                }
            } else {
                alert("Geocoder failed due to: " + status);
            }
        });
    });

    // Attach event listener for drawing circles
    $("body").on("change", `#${ELEMENT_IDS.radius}`, function () {
        drawCircles(searchLocation);
    });
    $("body").on("click", `#${ELEMENT_IDS.drawCirclesButton}`, function () {
        drawCircles(searchLocation);
    });
}

// Function to draw circles on the map
function drawCircles(location) {
    // Get radius values in miles
    const radiusInput = document.getElementById(ELEMENT_IDS.radius);
    if (!radiusInput || !(radiusInput.value >= 0)) {
        adjustZoomLevel(radiusMiles);
        return;
    }
    if (radiusInput.value > 99) radiusInput.value = 99;
    const radiusMiles = parseFloat(radiusInput.value); // Parent circle radius
    // Convert radii from miles to meters (Google Maps requires meters)
    const radiusMeters = radiusMiles * 1609.34;

    // Remove existing circles if they exist
    if (circle) circle.setMap(null);

    // Draw the parent circle if radius1 is greater than 0
    if (radiusMeters > 0) {
        circle = new google.maps.Circle({
            strokeColor: "#014bbe",
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: "#014bbe",
            fillOpacity: 0.15,
            map: map,
            center: location,
            radius: radiusMeters,
        });
    }
    map.setCenter(location);
    adjustZoomLevel(radiusMiles);
}

// Function to adjust the zoom level based on radius
function adjustZoomLevel(radiusInMiles) {
    switch (true) {
        case radiusInMiles <= 3:
            map.setZoom(13);
            break;
        case radiusInMiles <= 7:
            map.setZoom(11);
            break;
        case radiusInMiles <= 12:
            map.setZoom(10);
            break;
        case radiusInMiles <= 22:
            map.setZoom(9);
            break;
        case radiusInMiles <= 30:
            map.setZoom(8);
            break;
        case radiusInMiles <= 50:
            map.setZoom(7);
            break;
        default:
            map.setZoom(7);
            break; // Default zoom level for radius > 50 miles
    }
    updateValues();
}
// Function to toggle visibility of circles
function toggleCircles(show) {
    if (circle) circle.setVisible(show);
}

// Handle geolocation errors
function handleLocationError(browserHasGeolocation, pos) {
    const infoWindow = new google.maps.InfoWindow();
    infoWindow.setPosition(pos);
    infoWindow.setContent(
        browserHasGeolocation
            ? "Error: The Geolocation service failed."
            : "Error: Your browser doesn't support geolocation."
    );
    infoWindow.open(map);
}

function updateValues(lat, lon, add) {
    const latitudeInput = document.getElementById(
        ELEMENT_IDS?.inputBinds?.latitude
    );
    const longitudeInput = document.getElementById(
        ELEMENT_IDS?.inputBinds?.longitude
    );
    const addressInput = document.getElementById(
        ELEMENT_IDS?.inputBinds?.address
    );
    if (latitudeInput) latitudeInput.value = typeof(searchLocation.lat) == 'function'? searchLocation.lat(): searchLocation.lat;
    if (longitudeInput) longitudeInput.value = typeof(searchLocation.lng) == 'function'? searchLocation.lng(): searchLocation.lng;
    if (addressInput) addressInput.value = marker.getTitle();
}

//#region location search 

function initGoogleMapsCityAutocomplete() {
    document.querySelectorAll('.location_city_search').forEach((input) => {
        if(input.classList.contains('gmap_configured')) return;
        const autocomplete = new google.maps.places.Autocomplete(input, {
            types: ['(cities)']
        });
        input.classList.add('gmap_configured')
        autocomplete.addListener('place_changed', () => fillInAddress(autocomplete, input));
        input.addEventListener('blur', () => {
            if (!input.placeSelected) {
                input.value = '';
            }
            input.placeSelected = false;
        });

        input.addEventListener('input', () => {
            input.placeSelected = false;
        });
    });
}

function fillInAddress(autocomplete, input) {
    const place = autocomplete.getPlace();
    let city = '';
    let state = '';
    let country = '';

    place.address_components.forEach(component => {
        const types = component.types;
        if (types.includes('locality')) city = component.long_name;
        if (types.includes('administrative_area_level_1')) state = component.long_name;
        if (types.includes('country')) country = component.long_name;
    });
    input.placeSelected = true;
    console.log(city, state, country)
}
