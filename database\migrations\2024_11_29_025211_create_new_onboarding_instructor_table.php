<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNewOnboardingInstructorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('new_onboarding_instructor', function (Blueprint $table) {
            $table->id();
            $table->string('user_id')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('full_name')->nullable();
            $table->string('email')->nullable();
            $table->string('password')->nullable();
            $table->string('gender')->nullable();
            $table->smallInteger('type')->nullable();
            $table->string('profile_status')->nullable();
            $table->string('image')->nullable();
            $table->string('phone_number')->nullable();
            $table->text('about')->nullable();
            $table->longText('address')->nullable();
            $table->string('country')->nullable();
            $table->string('state')->nullable();
            $table->string('city')->nullable();
            $table->string('zipcode')->nullable();
            $table->text('regions')->nullable();
            $table->string('application')->nullable();
            $table->integer('district')->nullable();
            $table->string('organizationtype')->nullable();
            $table->string('organizationname')->nullable();
            $table->enum('status', ['0', '1', '2', '3']);
            $table->integer('email_verify_status')->nullable();
            $table->integer('department')->nullable();
            $table->string('cust_type')->nullable();
            $table->string('teach')->nullable();
            $table->string('teacher_type')->nullable();
            $table->string('cbo')->nullable();
            $table->text('grade')->nullable();
            $table->string('remember_token')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->integer('is_background_check')->nullable();
            $table->string('is_medical_check')->nullable();
            $table->string('description')->nullable();
            $table->string('inpersonrate')->nullable();
            $table->string('onlinerate')->nullable();
            $table->string('review_deadline')->nullable();
            $table->string('email_verify_time')->nullable();
            $table->string('start_date')->nullable();
            $table->string('submission_date')->nullable();
            $table->string('social_id')->nullable();
            $table->string('social_type')->nullable();
            $table->string('is_contract')->nullable();
            $table->string('contract_sent_date')->nullable();
            $table->string('is_resubmit')->nullable();
            $table->string('is_approved')->nullable();
            $table->string('is_sub')->nullable();
            $table->string('is_special')->nullable();
            $table->string('email_notification')->nullable();
            $table->string('app_notification')->nullable();
            $table->string('website_url')->nullable();
            $table->string('candidate_id')->nullable();
            $table->timestamp('login_at')->nullable();
            $table->string('stripe_id')->nullable();
            $table->string('card_brand')->nullable();
            $table->string('card_last_four')->nullable();
            $table->timestamp('trial_ends_at')->nullable();
            $table->string('pending_onboarding_steps', 1000)->default(json_encode(["step-1:us-work-authorization","step-2:how-wizara-works","step-3:certification","step-3:education","step-3:teaching-experience","step-3:resume","step-4:grade-levels","step-4:subject","step-4:proficiency","step-4:format","step-4:schedule","step-5:profile","step-6:assessment","step-7:agreement"]));
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('new_onboarding_instructor');
    }
}
