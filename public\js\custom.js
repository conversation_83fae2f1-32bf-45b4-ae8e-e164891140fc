



$("#Editinstitute").click(function () {


    var name = $('#first_name').val();
    if (!/^[a-z A-Z]*$/g.test(name)) {
        $('#name_error').html('Allow only characters');
        $('#first_name').focus();
        return false;
    } else {
        $('#name_error').html('');
    }

    var surename = $('#last_name').val();
    if (!/^[a-z A-Z]*$/g.test(surename)) {

        $('#p_last_name_error').html('Allow Only Characters');
        $('#last_name').focus();

        return false;

    } else {
        $('#p_last_name_error').html('');
    }

    // var district = $('#district').val();
    // if (district == "") {
    //     $('#district_error').html('Please select district');
    //     $('#district').focus();
    //     return false;
    // } else {
    //     $('#district_error').html('');
    // }



    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        $('#email_error').html('Please enter valid email address');
        $("#email").focus();

        return false;
    } else {
        $('#email_error').html('');
    }
    // var contry_code = $("#contry_code").val();
    // if (contry_code == "") {
    //     $('#contry_code_error').html('Please select county code');
    //     $("#contry_code").focus();

    //     return false;
    // } else {
    //     $('#contry_code_error').html('');
    // }
    // var umobile = $("#phone").val();
    // if (umobile == "") {
    //     $('#phone_error').html('Please enter mobile number');
    //     $("#phone").focus();

    //     return false;
    // } else {
    //     $('#phone_error').html('');
    // }

    // var website = document.getElementById("website").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (website != "") {
    //     if (!regexp.test(website)) {
    //         $('#website_error').html('Please enter valid url.');
    //         document.getElementById('website').value = "";
    //         document.getElementById('website').focus();
    //         return false;
    //     } else {
    //         $('#website_error').html('');
    //     }
    // }
    // var fburl = document.getElementById("Facebooklink").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (fburl != "") {
    //     if (!regexp.test(fburl)) {
    //         $('#EditableFacebook_error').html('Please enter valid url.');
    //         document.getElementById('Facebooklink').value = "";
    //         document.getElementById('Facebooklink').focus();
    //         return false;
    //     } else {
    //         $('#EditableFacebook_error').html('');
    //     }
    // }
    // var linurl = document.getElementById("LinkedInlink").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (linurl != "") {
    //     if (!regexp.test(linurl)) {
    //         $('#editableLinkedIn_error').html('Please enter valid url.');
    //         document.getElementById('LinkedInlink').value = "";
    //         document.getElementById('LinkedInlink').focus();
    //         return false;
    //     } else {
    //         $('#editableLinkedIn_error').html('');
    //     }
    // }
    // var twiturl = document.getElementById("Twitterlink").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (twiturl != "") {
    //     if (!regexp.test(twiturl)) {
    //         $('#editableTwitter_error').html('Please enter valid url.');
    //         document.getElementById('Twitterlink').value = "";
    //         document.getElementById('Twitterlink').focus();
    //         return false;
    //     } else {
    //         $('#editableTwitter_error').html('');
    //     }
    // }
    // var youtube_url = document.getElementById("youtube_url").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (youtube_url != "") {
    //     if (!regexp.test(youtube_url)) {
    //         $('#youtube_url_error').html('Please enter valid url.');
    //         document.getElementById('youtube_url').value = "";
    //         document.getElementById('youtube_url').focus();
    //         return false;
    //     } else {
    //         $('#youtube_url_error').html('');
    //     }
    // }
    // var latitude = $("#latitude").val();
    // if (latitude == "") {
    //     $('#latitude_error').html('Please write latitude');
    //     $("#latitude").focus();

    //     return false;
    // } else {
    //     $('#latitude_error').html('');
    // }
    // var longitude = $("#longitude").val();
    // if (longitude == "") {
    //     $('#longitude_error').html('Please write longitude');
    //     $("#longitude").focus();

    //     return false;
    // } else {
    //     $('#longitude_error').html('');
    // }
    var address = $("#address").val();
    if (address == "") {
        $('#address_error').html('Please write address');
        $("#address").focus();

        return false;
    } else {
        $('#address_error').html('');
    }

    // const el = $("#institute_logo")[0];
    // if (el.files && el.files[0]) {
    //     const file = el.files[0];
    //     const maxFileSize = 500;
    //     const maxWidth = 900;
    //     const maxHeight = 900;
    //     const img = new Image();
    //     img.src = window.URL.createObjectURL(file);
    //     img.onload = () => {
    //         if (file.type.match('image.*') && file.size > maxFileSize) {
    //             $('#institute_logo_error').html('file is too big');
    //             $("#institute_logo").focus();

    //             return false;
    //         } else if (file.type.match('image.*') && (img.width > maxWidth || img.height > maxHeight)) {
    //             $('#phone_error').html('Please choose one with maximum dimensions of ${maxWidth}x${maxHeight}.');
    //             $("#institute_logo").focus();

    //             return false;
    //         } else {
    //             $('#institute_logo_error').html('');
    //         }
    //     };
    // }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/editschool';
    var formData = new FormData($("#editinstitute")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
});


$('body').on('click', '.delete_data_Institute', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/deleteinstitute';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {

                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        currentRow.parents('tr').remove();
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})


// Institude
$("#addInstitute").click(function () {

    var name = $('#other_full_name').val();
    if ($('#other_full_name').is(':visible') && name == '') {
        $('#full_name_error').html('Full name is requried');
        $('#other_full_name').focus();
        return false;
    } else {
        $('#full_name_error').html('');
    }

    // var surename = $('#last_name').val();
    // if (!/^[a-z A-Z]*$/g.test(surename)) {

    //     $('#p_last_name_error').html('Allow Only Characters');
    //     $('#last_name').focus();

    //     return false;

    // } else {
    //     $('#p_last_name_error').html('');
    // }





    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        $('#email_error').html('Please enter valid email address');
        $("#email").focus();
        return false;
    } else {
        $('#email_error').html('');
    }
    // var district = $("#district").val();
    // if (district == "") {
    //     $('#district_error').html('Please select district');
    //     $("#district").focus();

    //     return false;
    // } else {
    //     $('#district_error').html('');
    // }
    // var umobile = $("#phone").val();
    // if (umobile == "") {
    //     $('#phone_error').html('Please enter mobile number');
    //     $("#phone").focus();
    //     return false;
    // } else {
    //     $('#phone_error').html('');
    // }

    // var website = document.getElementById("website").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (website != "") {
    //     if (!regexp.test(website)) {
    //         $('#website_error').html('Please enter valid url.');
    //         document.getElementById('website').value = "";
    //         document.getElementById('website').focus();
    //         return false;
    //     } else {
    //         $('#website_error').html('');
    //     }
    // }
    // var fburl = document.getElementById("Facebooklink").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (fburl != "") {
    //     if (!regexp.test(fburl)) {
    //         $('#EditableFacebook_error').html('Please enter valid url.');
    //         document.getElementById('Facebooklink').value = "";
    //         document.getElementById('Facebooklink').focus();
    //         return false;
    //     } else {
    //         $('#EditableFacebook_error').html('');
    //     }
    // }
    // var linurl = document.getElementById("LinkedInlink").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (linurl != "") {
    //     if (!regexp.test(linurl)) {
    //         $('#editableLinkedIn_error').html('Please enter valid url.');
    //         document.getElementById('LinkedInlink').value = "";
    //         document.getElementById('LinkedInlink').focus();
    //         return false;
    //     } else {
    //         $('#editableLinkedIn_error').html('');
    //     }
    // }
    // var twiturl = document.getElementById("Twitterlink").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (twiturl != "") {
    //     if (!regexp.test(twiturl)) {
    //         $('#editableTwitter_error').html('Please enter valid url.');
    //         document.getElementById('Twitterlink').value = "";
    //         document.getElementById('Twitterlink').focus();
    //         return false;
    //     } else {
    //         $('#editableTwitter_error').html('');
    //     }
    // }
    // var youtube_url = document.getElementById("youtube_url").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (youtube_url != "") {
    //     if (!regexp.test(youtube_url)) {
    //         $('#youtube_url_error').html('Please enter valid url.');
    //         document.getElementById('youtube_url').value = "";
    //         document.getElementById('youtube_url').focus();
    //         return false;
    //     } else {
    //         $('#youtube_url_error').html('');
    //     }
    // }

    // var address = $("#address").val();
    // if (address == "") {
    //     $('#address_error').html('Please write address');
    //     $("#address").focus();

    //     return false;
    // } else {
    //     $('#address_error').html('');
    // }
    // const el = $("#institute_logo")[0];
    // if (el.files && el.files[0]) {
    //     const file = el.files[0];
    //     const maxFileSize = 2000;
    //     const maxWidth = 900;
    //     const maxHeight = 900;
    //     const img = new Image();
    //     img.src = window.URL.createObjectURL(file);
    //     img.onload = () => {
    //         if (file.type.match('image.*') && file.size > maxFileSize) {
    //             // $('#institute_logo_error').html('file is too big');
    //             // $("#institute_logo").focus();

    //             return false;
    //         } else if (file.type.match('image.*') && (img.width > maxWidth || img.height > maxHeight)) {
    //             $('#phone_error').html('Please choose one with maximum dimensions of ${maxWidth}x${maxHeight}.');
    //             $("#institute_logo").focus();
    //             return false;
    //         } else {
    //             $('#institute_logo_error').html('');
    //         }
    //     };
    // }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/storeinstitute';
    var formData = new FormData($("#institute_details")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        beforeSend: function () {
            $(".loader").removeClass('d-none');
            $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.success == true) {
                $(".loader").addClass('d-none');
                alertify.success(data.message);
                $('#institute_details')[0].reset();
                window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);

    });
});



$('body').on('click', '#changestatus', function () {
    var id = $(this).attr("data-id");
    var datavalue = $(this).attr("data-value");
    swal({
        title: "Change status",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }

            });
            var url = APP_URL + '/change-status';

            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id,
                    id1: datavalue
                },
                dataType: "json",
                beforeSend: function () {
                    $("#overlay").fadeIn(300)
                },
                success: function (data) {
                    if (data.success == true) {
                        alertify.success(data.message);
                        if (datavalue == '1') {
                            $('.changestatuscls-' + id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").prop("onclick", null).off("click");
                            $('.changestatuscls-' + id).html('Active');
                        } else {
                            $('.changestatuscls-' + id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").prop("onclick", null).off("click");
                            $('.changestatuscls-' + id).html('Deactive');
                        }
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $("#overlay").fadeOut(300);
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})


// change password

$('body').on('click', '#changepasswords', function () {

    var old_password = document.getElementById('old_password').value;
    if (old_password.length == "") {
        $('.old_password_error').html('Please Enter Your Old password');
        document.getElementById('old_password').value = "";
        document.getElementById('old_password').focus();
        return false;
    } else {
        $('.old_password_error').html('');
        $('#old_password').css('border-color', '#495057');
    }

    var new_password = document.getElementById('new_password').value;
    if (new_password.length == "") {
        $('.new_password_error').html('Please Enter Your New Password');
        document.getElementById('new_password').value = "";
        document.getElementById('new_password').focus();
        return false;
    } else {
        $('.new_password_error').html('');
        $('#new_password').css('border-color', '#495057');
    }

    var confirm_password = document.getElementById('confirm_password').value;
    if (confirm_password.length == "") {
        $('.confirm_password_error').html('Please Enter Your Confirm Password');
        document.getElementById('confirm_password').value = "";
        document.getElementById('confirm_password').focus();
        return false;
    } else {
        $('.confirm_password_error').html('');
        $('#confirm_password').css('border-color', '#495057');
    }

    if (new_password != confirm_password) {
        $('.confirm_password_error').html('The password and confirm password do not match!!');
        document.getElementById('confirm_password').value = "";
        document.getElementById('confirm_password').focus();
        return false;
    } else {
        $('.confirm_password_error').html('');
        $('#confirm_password').css('border-color', '#495057');
    }


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/updateChangePassword';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#changepwdform').serialize(),
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                // $("#changepwdform").reset();
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $(".loader").addClass('d-none');
    });

})

function getUserTimezone() {
    var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return timezone;
  }

//  login validation
$("#loginbtn").click(function () {
    // alert('hello');
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test(document.getElementById("email").value);
    (regemailid);

    if (regemailid == false) {
        $('.email_error').html('Please enter a valid Email');
        document.getElementById("email").focus();
        $('#email').css('border-color', '#495057');
        return false;
    } else {
        $('.email_error').html('');
        $('#email').css('border-color', '#495057');
    }
    var password = document.getElementById('passwords').value;
    if (password.length == "") {
        $('.password_error').html('Please enter your password');
        document.getElementById('passwords').value = "";
        document.getElementById('passwords').focus();
        return false;
    } else {
        $('.password_error').html('');
        $('#passwords').css('border-color', '#495057');
    }

    // if($('#terms').is(':checked') == false){
    //      event.preventDefault();
    //      alertify.error('Accept, you must accept our terms and conditions');
    //      return false;
    //  }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/loginpost';
    var formData = $('#loginfrm').serialize() + '&timezone=' + encodeURIComponent(getUserTimezone());

    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {
            $(".loader").addClass('d-none');
            if (data.success == true) {
                firebaseauth(data.email, data.password);
                firebasesignin(data.email, data.password)
                // alertify.success(data.message);
                window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $(".loader").addClass('d-none');
        }, 500);
    });
});
//  signup





$("#signup_btn").click(function () {

    var name = document.getElementById('first_name').value;
    if (name.length == "") {
        $('.name_error').html('Please enter full name');
        document.getElementById('first_name').value = "";
        document.getElementById('first_name').focus();
        return false;

    } else {
        $('.name_error').html('');
        $('#first_name').css('border-color', '#495057');
    }
    if (!/^[a-z A-Z]*$/g.test(name)) {
        $('.name_error').html('Allow only characters');

        document.getElementById('first_name').focus();

        $('#first_name').css('border-color', '#ff0000');

        return false;

    } else {

        $('.name_error').html('');

        $('#first_name').css('border-color', '#495057');

    }


    var surename = document.getElementById('last_name').value;

    if (surename.length == "") {

        $('.name_error').html('Please enter full name');
        document.getElementById('last_name').value = "";
        document.getElementById('last_name').focus();
        return false;
    } else {
        $('.name_error').html('');
        $('#last_name').css('border-color', '#495057');
    }

    if (!/^[a-z A-Z]*$/g.test(surename)) {



        $('.name_error').html('Allow Only Characters');
        document.getElementById('last_name').focus();
        $('#last_name').css('border-color', '#ff0000');

        return false;

    } else {
        $('.name_error').html('');
        $('#last_name').css('border-color', '#495057');

    }

    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test(document.getElementById("email").value);
    (regemailid);
    if (regemailid == false) {
        $('.email_error').html('Please enter valid email address');
        document.getElementById("email").focus();
        $('#email').css('border-color', '#495057');
        return false;
    } else {
        $('.email_error').html('');
        $('#email').css('border-color', '#495057');
    }
    var umobile = document.getElementById("phone").value;
    if (umobile == "") {
        $('.phone_error').html('Please enter mobile number');
        document.getElementById("phone").focus();
        $('#phone').css('border-color', '#ff0000');
        return false;
    } else {
        $('.phone_error').html('');
        $('#phone').css('border-color', '#495057');
    }
    var password = document.getElementById('password').value;
    if (password.length == "") {
        $('.password_error').html('Please enter password');
        document.getElementById('password').value = "";
        document.getElementById('password').focus();

        return false;

    } else {

        $('.password_error').html('');
        $('#password').css('border-color', '#495057');
    }
    var repassword = document.getElementById('repassword').value;
    if (repassword.length == "") {
        $('.repassword_error').html('Please enter confirm password');
        document.getElementById('repassword').value = "";
        document.getElementById('repassword').focus();
        return false;

    } else {

        $('.repassword_error').html('');
        $('#repassword').css('border-color', '#495057');
    }

    if (password != repassword) {
        $('.repassword_error').html('The password and confirm password do not match');
        document.getElementById('repassword').value = "";
        document.getElementById('repassword').focus();
        return false;

    } else {

        $('.repassword_error').html('');

        $('#repassword').css('border-color', '#495057');

    }

    if ($('#terms').is(':checked') == false) {
        event.preventDefault();
        alertify.error('By signing up, you must accept our terms and conditions');
        return false;
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }

    });
    var url = APP_URL + '/signuppost';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#signupfrm').serialize(),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                window.location.href = data.redirect;
            } else {

                alertify.error(data.message);

            }

        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);

    });

});

//  edit profile


$('body').on('click', '#personalInfoSaveBtn', function () {
    var first_name = $('#p_first_name').val();
    var last_name = $('#p_last_name').val();
    var about_you = $('#about').val();
    var dob = $('#dob').val();

    if (first_name == "") {
        $('#p_first_name_error').html('Please enter first name');
        $('#p_first_name').focus();
        return false;
    } else {
        $('#p_first_name_error').html('');
    }
    if (!/^[a-z A-Z]*$/g.test($('#p_first_name').val())) {
        $('#p_first_name_error').html('Allow only characters!!');
        $('#p_first_name').focus();
        return false;
    } else {
        $('#p_first_name_error').html('');
    }

    if (last_name == "") {
        $('#p_last_name_error').html('Please enter last name');
        $('#p_last_name').focus();
        return false;
    } else {
        $('#p_last_name_error').html('');

    }
    if (!/^[a-z A-Z]*$/g.test($('#p_last_name').val())) {
        $('#p_last_name_error').html('Allow only characters!!');
        $('#p_last_name').focus();
        $('#p_last_name').css('border-color', '#ff0000');
        return false;
    } else {
        $('#p_last_name_error').html('');
        $('#p_last_name').css('border-color', '#000000');
    }
    if ($('input[type=radio][name=gender]:checked').length == 0) {
        $('#p_gender_error').html('Please select gender!!');

        return false;
    } else {
        $('#p_gender_error').html(' ');
    }

    if (dob == "") {
        $('#p_dob_error').html('Please enter your date of birth!!');
        $('#dob').focus();
        return false;
    } else {
        $('#p_dob_error').html('');
    }

    if (about_you == "") {
        $('#about_you_error').html('Please enter about');
        $('#about').focus();
        return false;
    } else {
        $('#about_you_error').html(' ');
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/personal_info';
    var formData = $('#content_details').serialize();

    $.ajax({
        url: url,
        type: 'post',
        data: formData,
        processData: false,
        // contentType: false,
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                // window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);

    });

})


$('body').on('click', '#contenturlbtn', function () {
    var email = $('#email').val();
    var umobile = $("#editableMobilePhone").val();
    var staticFacebook = $("#EditableFacebook").val();
    var staticLinkedIn = $("#editableLinkedIn").val();
    var staticTwitter = $("#editableTwitter").val();

    // var fburl = document.getElementById("EditableFacebook").value;
    // var regexp = /^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/;
    // if (fburl != "") {
    //     if (!regexp.test(fburl)) {
    //         $('#EditableFacebook_error').html('Please enter valid url.');
    //         document.getElementById('EditableFacebook').value = "";
    //         document.getElementById('EditableFacebook').focus();
    //         return false;
    //     } else {
    //         $('#EditableFacebook_error').html('');
    //     }
    // }
    // var linurl = document.getElementById("editableLinkedIn").value;
    // var regexp = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
    // if (linurl != "") {
    //     if (!regexp.test(linurl)) {
    //         $('#editableLinkedIn_error').html('Please enter valid url.');
    //         document.getElementById('editableLinkedIn').value = "";
    //         document.getElementById('editableLinkedIn').focus();
    //         return false;
    //     } else {
    //         $('#editableLinkedIn_error').html('');
    //     }
    // }
    // var twiturl = document.getElementById("editableTwitter").value;
    // var regexp = /^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/;
    // if (twiturl != "") {
    //     if (!regexp.test(twiturl)) {
    //         $('#editableTwitter_error').html('Please enter valid url.');
    //         document.getElementById('editableTwitter').value = "";
    //         document.getElementById('editableTwitter').focus();
    //         return false;
    //     } else {
    //         $('#editableTwitter_error').html('');
    //     }
    // }
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        $('#email_error').html('Please enter a valid email');
        $("#email").focus();
        return false;
    } else {
        $('#email_error').html('');
    }

    if (umobile == "") {
        $('#editableMobilePhone_error').html('Please enter the mobile number');
        $("#editableMobilePhone").focus();
        return false;
    } else {
        $('#editableMobilePhone_error').html('');
    }
    if ((umobile.length < 12) || (umobile.length > 12)) {
        $('#editableMobilePhone_error').html('Your mobile number must be 1 to 10 Integers');

        $("#editableMobilePhone").focus();
        return false;
    } else {
        $('#editableMobilePhone_error').html('');
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/edit-contact';
    var formData = $('#content_otherdetails').serialize();

    $.ajax({
        url: url,
        type: 'post',
        data: formData,
        processData: false,
        // contentType: false,
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                // window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);

    });

})


$(document).on('change', '.profileImgUploadBtn', function (e) {

    var fileName = e.target.files[0].name;
    var image = $('.profileImgUploadBtn').val();
    var img_ex = /(\.jpg|\.jpeg|\.png|\.gif)$/i;
    if (!img_ex.exec(image)) {
        $('#image').val('');
        return false;
    } else {
        var th = $(this);
        var url = APP_URL + '/update_profile_image';

        $.ajax({
            type: 'POST',
            url: url,
            data: new FormData($('#profileuploadId')[0]),
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function (data) {
                alertify.success(data.message);
                $('#imagePreview').attr("src", APP_URL + '/public/profile/' + data.img);
                $('#profileImgUploadBtn').attr("value", data.img);
            }
        })
    }
})


// forgotpwd


$('body').on('click', '.forgotpwd_resend_btn', function (e) {
    e.preventDefault();
    var email = $('#email').val();
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        $('#email_error').html('Please enter a valid email');
        $("#email").focus();
        e.preventDefault();
        return false;
    } else {
        $('#email_error').html('');
    }

    $('.emailcoo').text(email);
    var url = APP_URL + '/admin-forgot-password';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        type: "post",
        url: url,
        dataType: "json",
        data: $('#forgid').serialize(),
        statusCode: {
            404: function (data) {
                alertify.error(data?.responseJSON?.message);
            },
            200: function (data) {
                $(this).next().fadeIn();
                alertify.success(data?.message);
                $('#nextbtn').trigger('click');
            }
        }
    })
})

$('body').on('click', '#sendotp', function (e) {
    e.preventDefault();
    var otp1 = $('#otp1').val();
    var otp2 = $('#otp2').val();
    var otp3 = $('#otp3').val();
    var otp4 = $('#otp4').val();
    if (!otp1) {
        $('#otp_error').html('Please enter a otp');
        $("#otp1").focus();
        return false;
    } else {
        $('#otp_error').html('');
    }
    if (!otp2) {
        $('#otp_error').html('Please enter a otp');
        $("#otp2").focus();
        return false;
    } else {
        $('#otp_error').html('');
    }
    if (!otp3) {
        $('#otp_error').html('Please enter a otp');
        $("#otp3").focus();
        return false;
    } else {
        $('#otp_error').html('');
    }
    if (!otp4) {
        $('#otp_error').html('Please enter a otp');
        $("#otp4").focus();
        return false;
    } else {
        $('#otp_error').html('');
    }

    var url = APP_URL + '/admin-otp-match';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        type: "post",
        url: url,
        dataType: "json",
        data: $('#forgid').serialize(),
        statusCode: {
            401: function () {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 500)
            },
            422: function (data) {
                alertify.error(data?.responseJSON?.message);
            },
            200: function (data) {
                $(this).next().fadeIn();
                alertify.success(data.message);
                $('#nextbtnotp').trigger('click');
            }
        }
    })
})

$('body').on('click', '#admin_reset_password_btn', function (e) {
    e.preventDefault();
    var confirm_password = $('#confirm_password').val();
    var new_password = $('#new_password').val();

    if (!new_password) {
        $('#new_password_error').html('Please enter a new password');
        $("#new_password").focus();
        e.preventDefault();
        return false;
    } else {
        $('#new_password_error').html('');
    }


    if (!confirm_password) {
        $('#confirm_password_error').html('Please enter a confirm password');
        $("#confirm_password").focus();
        e.preventDefault();
        return false;
    } else {
        $('#confirm_password_error').html('');
    }


    if (confirm_password != new_password) {
        $('#confirm_password_error').html('Confirm password is not same');
        $("#confirm_password").focus();
        e.preventDefault();
        return false;
    } else {
        $('#confirm_password_error').html('');
    }


    var url = APP_URL + '/admin-reset-password';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        type: "post",
        url: url,
        dataType: "json",
        data: $('#forgid').serialize(),
        statusCode: {
            401: function () {
                window.location.href = "/login"
            },
            404: function (data) {
                alertify.error(data?.responseJSON?.message);
            },
            200: function (data) {
                $(this).next().fadeIn();
                alertify.success(data?.message);
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 500)
            }
        }
    })
})


$('body').on('click', '.addMoreExpBtn_test', function () {
    // alert('hello');
    var lastkey = $(".key").length;

    var max_fields_limit = 10; //set limit for maximum input fields
    if (lastkey == 1) {
        var x = 0; //initialize counter for text box
    } else if (lastkey == 0) {
        var x = 0; //initialize counter for text box
    } else {
        var x = lastkey - 1; //initialize counter for text box
    }

    // alert(lastkey + + x);
    if (x < max_fields_limit) {
        x++;
        $("#experieninfoid1").append(`<div id="row` + x + `" class="row experience_row pt-3 mb-3 key">
      <div class="col-md-6 form-group">
      <label class="form-label">Institute / Company Name</label>
      <input type="text" id="company` + x + `" name="company[]" class="form-control company_name_info" placeholder="Institute / Company Name">
      </div>

      <div class="col-md-6 form-group">
      <label class="form-label">Position / Job Profile</label>
      <input type="text" id="current_designation` + x + `" name="current_designation[]"  class="form-control current_designation" placeholder="Position / Job Profile">
      </div>

      <div class="col-lg-4 col-md-6 form-group">
      <label class="form-label">Joining Date</label>
      <div class="input-group date"  id="expJoiningDate_` + x + `" data-target-input="nearest">
      <input type="text" name="joining_date[]" class="form-control datetimepicker-input expJoiningDate" data-target="#expJoiningDate_` + x + `"/>
      <div class="input-group-append" data-target="#expJoiningDate_` + x + `" data-toggle="datetimepicker">
      <div class="input-group-text"><i class="fa fa-calendar"></i></div>
      </div>
      </div>
      </div>

      <div class="col-lg-4 col-md-6 form-group">
      <label class="form-label">Leaving Date</label>
      <div class="input-group date" id="expLeavingDate_` + x + `" data-target-input="nearest">
      <input type="text" name="leaving_date[]" class="form-control datetimepicker-input expLeavingDate" data-target="#expLeavingDate_` + x + `"/>
      <div class="input-group-append" data-target="#expLeavingDate_` + x + `" data-toggle="datetimepicker">
      <div class="input-group-text"><i class="fa fa-calendar"></i></div>
      </div>
      </div>
      </div>

      <div class="col-lg-4 mb-lg-3">
      <div class="d-flex justify-content-between align-items-end h-100">
      <div class="custom-control custom-radio custom-control-inline mb-2">
      <input type="checkbox" value="1" class="custom-control-input" id="currentlyWorking` + x + `" name="current_work[]">
      <label class="custom-control-label" for="currentlyWorking` + x + `">Currently Working Here</label>
      </div>

      <button type="button" id="` + x + `" class="btn btn-danger removeMoreExpBtn"><i class="align-middle" data-feather="trash-2"></i> </button>
      </div>
      </div>
      </div>`);
    }
    $('#addMoreExpBtn_test').prop('disabled', false);

    //Once remove button is clicked
})

$(document).on('click', '.removeMoreExpBtn', function () {
    var button_id = $(this).attr("id");
    $('#row' + button_id + '').remove();
});


$('body').on('click', '.addMoreEduBtn', function () {
    // alert('hello');
    var lastkey = $(".keyedu").length;

    var max_fields_limit = 10; //set limit for maximum input fields
    if (lastkey == 1) {
        var y = 0; //initialize counter for text box
    } else if (lastkey == 0) {
        var y = 0; //initialize counter for text box
    } else {
        var y = lastkey - 1; //initialize counter for text box
    }
    // alert(lastkey + + x);
    if (y < max_fields_limit) {
        y++;
        $("#eductioninfo").append(`<div id="row` + y + `" class="row education_row pt-3 mb-3">
      <div class="col-md-6 form-group">
      <label class="form-label">School / University Name</label>
      <input type="text" id="highest_qualification` + y + `" name="highest_qualification[]" class="form-control highest_qualification" placeholder="School / University Name">
      </div>

      <div class="col-md-6 form-group">
      <label class="form-label">Subject</label>
      <input type="text" id="subject` + y + `" name="subject[]" class="form-control subjects" placeholder="Subject">
      </div>

      <div class="col-lg-4 col-md-6 form-group">
      <label class="form-label">Joining Date</label>
      <div class="input-group date" id="eduJoiningDate_` + y + `" data-target-input="nearest">
      <input type="text" class="form-control datetimepicker-input eduJoiningDate" data-target="#eduJoiningDate_` + y + `" name="joining_date[]"/>
      <div class="input-group-append" data-target="#eduJoiningDate_` + y + `" data-toggle="datetimepicker">
      <div class="input-group-text"><i class="fa fa-calendar"></i></div>
      </div>
      </div>
      </div>

      <div class="col-lg-4 col-md-6 form-group">
      <label class="form-label">Leaving Date</label>
      <div class="input-group date" id="eduLeavingDate_` + y + `" data-target-input="nearest">
      <input type="text"  name="leaving_date[]" class="form-control datetimepicker-input eduLeavingDate" data-target="#eduLeavingDate_` + y + `"/>
      <div class="input-group-append" data-target="#eduLeavingDate_` + y + `" data-toggle="datetimepicker">
      <div class="input-group-text"><i class="fa fa-calendar"></i></div>
      </div>
      </div>
      </div>

      <div class="col-lg-4 mb-lg-3">
      <div class="d-flex justify-content-between align-items-end h-100">
      <div class="custom-control custom-radio custom-control-inline mb-2">
      <input type="checkbox" class="custom-control-input" id="currentlyStudying_` + y + `" value="1" name="current_work[]">
      <label class="custom-control-label" for="currentlyStudying_` + y + `">Currently Studying Here</label>
      </div>
      <button type="button" class="btn btn-danger removeMoreeduBtn" id="` + y + `"><i class="align-middle" data-feather="trash-2"></i> </button>
      </div>
      </div> </div>`);
    }
    $('.addMoreEduBtn').prop('disabled', false);




    //Once remove button is clicked
})

$(document).on('click', '.removeMoreeduBtn', function () {
    var button_id = $(this).attr("id");
    $('#row' + button_id + '').remove();
});




$(document).on('change', '.profileImgUploadinst', function (e) {
    var th = $(this);
    var url = APP_URL + '/update-institute_picture';
    $.ajax({
        type: 'POST',
        url: url,
        data: new FormData($('#editinstituteimage')[0]),
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function (data) {
            alertify.success(data.message);
            $('#imagePreview').attr("src", APP_URL + '/public/uploads/institute/' + data.img);
            $('#trainerprofupload').attr("value", data.img);
        }
    })

})



$('body').on('submit', '#permission_form', function (event) {

    event.preventDefault();

    var formData = $('#permission_form').serialize();

    var url = APP_URL + '/add_permission';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        type: "POST",
        url: url,
        data: formData,
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
            } else {
                alertify.error(data.message);
            }
        }
    });

})


function update_status(id, url, message = "Please ensure and then confirm!") {
    swal({
        title: "Change status",
        text: message,
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                dataType: "json",
                beforeSend: function () {
                    // $("#overlay").fadeIn(300)
                },
                success: function (data) {
                    if (data.status) {
                        alertify.success(data.message);
                        var status_val = $('.changestatuscls-' + id).data('data');

                        if (status_val == 1) {
                            $('.changestatuscls-' + id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
                            $('.changestatuscls-' + id).data('data', 0);
                            $('.changestatuscls-' + id).html('Active');
                        } else {
                            $('.changestatuscls-' + id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
                            $('.changestatuscls-' + id).data('data', 1);
                            $('.changestatuscls-' + id).html('Deactive');
                        }
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    // $("#overlay").fadeOut(300);
                }, 500);

            });

        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
}

function delete_status(id, url, message = "Please ensure and then confirm!") {

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        type: 'POST',
        url: url,
        data: { id: id },
        dataType: "json",
        beforeSend: function () {
            // $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.status) {
                alertify.success(data.message);

            } else {
                alertify.error(data.message);
            }

        },
    }).done(function () {
        setTimeout(function () {
            // $("#overlay").fadeOut(300);
        }, 500);

    });

}






function update_statusp(id, url, message = "Please ensure and then confirm!", status, note, onlinerate, inpersonrate) {
    swal({
        title: "Change status",
        text: message,
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id, id1: status, note: note, onlinerate: onlinerate, inpersonrate: inpersonrate },
                dataType: "json",
                beforeSend: function () {
                    // $("#overlay").fadeIn(300)
                },
                success: function (data) {
                    if (data.success == true) {
                        alertify.success(data.message);
                        $('#statusmodel').modal('hide');
                        var status_val = $('.changestatuscls-' + id).data('data');
                        if (status_val == 1) {
                            $('.changestatuscls-' + id).css("color", "#fffff").css("background-color", "#4bbf73").css("border-color", "#4bbf73").removeAttr('id');
                            $('.changestatuscls-' + id).data('data', 0);
                            $('.changestatuscls-' + id).html('Active');
                        } else {
                            $('.changestatuscls-' + id).css("color", "#ffffff").css("background-color", "#e5a54b").css("border-color", "#e5a54b").removeAttr('id');
                            $('.changestatuscls-' + id).data('data', 1);
                            $('.changestatuscls-' + id).html('Deactive');
                        }
                        location.reload();
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    // $("#overlay").fadeOut(300);
                }, 500);

            });

        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
}




// Delete Single
function single_delete_data(id, url, type, msg = "Please ensure and then confirm!?") {
    swal({
        title: "Delete?",
        text: msg,
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                type: type,
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    $(document).find('#section_tr_' + id).remove();

                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
}

// edit content-setting
$("#edit_setting_terms_btn").click(function (event) {
    event.preventDefault();

    var title = $('#title').val();
    var description = CKEDITOR.instances['description'].getData();
    var status_value = $('#status').val();

    if (title == "") {
        $('#title_error').html('Please enter title');
        $('#title').val('');
        $('#title').focus();
        return false;
    } else {
        $('#title_error').html('');
    }

    $('#description_hidden').val(description);
    if (description == "") {
        $('#description_error').html('Please enter description');
        $('#description').focus();
        return false;
    } else {
        $('#description_error').html('');
    }

    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }

    if (title == 'Online Logistics/Prerequisites' || title == 'In person Logistics/Prerequisites'){
    } else {
        if (status_value == "") {
            $('#status_error').html('Please select status');
            $('#status').val('');
            $('#status').focus();
            return false;
        } else {
            $('#status_error').html('');
        }
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    var url = APP_URL + '/edittermssetting';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#edit_setting_terms_form')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300);
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                window.location.href = APP_URL + '/terms-setting-list';
            }
        },
        complete: function () {
            setTimeout(function () {
                $("#overlay").fadeOut(300);
            }, 500);
        }
    });
});

// add_faqs
$("#add_faqs").click(function () {
    var question = $('#question').val();
    var status_value = $('#status').val();
    var description = CKEDITOR.instances['description'].getData();

    if (question == "") {
        $('#question_error').html('Please enter question');
        $('#question').val('');
        $('#question').focus();
        return false;
    } else {
        $('#question_error').html('');
    }

    if (status_value == "") {
        $('#status_error').html('Please select status');
        $('#status').val('');
        $('#status').focus();
        return false;
    } else {
        $('#status_error').html(' ');
    }

    $('#description_hidden').val(description);
    if (description == "") {
        $('#description_error').html('Please enter description');
        $('#description').focus();
        return false;
    } else {
        $('#description_error').html('');
    }

    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/storefaqs';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#add_faqs_form').serialize(),
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {
            $(".loader").addClass('d-none');
            if (data.success == true) {
                alertify.success(data.message);
                $('#add_faqs_form')[0].reset();
                window.location.href = APP_URL + '/faqs-list';
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
});

// edit faqs
$("#edit_faqs_btn").click(function () {
    var question = $('#question').val();
    var status_value = $('#status').val();
    var description = CKEDITOR.instances['description'].getData();

    if (question == "") {
        $('#question_error').html('Please enter question');
        $('#question').val('');
        $('#question').focus();
        return false;
    } else {
        $('#question_error').html('');
    }

    if (status_value == "") {

        $('#status_error').html('Please select status');
        $('#status').val('');
        $('#status').focus();
        return false;
    } else {
        $('#status_error').html(' ');
    }

    $('#description_hidden').val(description);
    if (description == "") {
        $('#description_error').html('Please enter description');
        $('#description').focus();
        return false;
    } else {
        $('#description_error').html('');
    }

    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/editfaqs';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#edit_faqs_form').serialize(),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                window.location.href = APP_URL + '/faqs-list';
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
});


$('body').on('click', '.delete_faqs', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-faqs';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }
                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})


// })

function request_accept_reject(id, status) {

    swal({
        title: "Request",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/accept-reject-student-request';

            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id,
                    status: status
                },
                dataType: "json",
                beforeSend: function () {
                    // $("#overlay").fadeIn(300)
                },
                success: function (data) {
                    if (data.success == true) {
                        alertify.success(data.message);

                        if (status == 1) {
                            $('#studentrequeststatus-' + id).removeClass('btn-danger btn-primary');
                            $('#studentrequeststatus-' + id).addClass('btn-success');
                            $('#studentrequeststatus-' + id).text('Accept');
                        } else {
                            $('#studentrequeststatus-' + id).removeClass('btn-primary btn-success');
                            $('#studentrequeststatus-' + id).addClass('btn-danger');
                            $('#studentrequeststatus-' + id).text('Reject');
                        }
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    // $("#overlay").fadeOut(300);
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
}




function redirect_url(url) {
    setTimeout(function () {
        window.location.href = APP_URL + '/' + url;
    }, 600);
}






$("#addadminbutton").click(function (event) {
    event.preventDefault()
    var first_name = $('#first_name').val();
    var first_nameCount = first_name ? first_name.length : 0;
    if (first_name == "") {
        $('#first_name_error').html('Please enter first name');
        $('#first_name').val('');
        $('#first_name').focus();


    } else {
        if (first_nameCount > 30) {
            $('#first_name_error').html('Maximum 30 words allowed');

            $('#first_name').focus();

        }else{
            $('#first_name_error').html('');
        }

    }
    var last_name = $('#last_name').val();
    var last_nameCount = last_name ? last_name.length : 0;
    if (last_name == "") {
        $('#last_name_error').html('Please enter last Name');
        $('#last_name').val('');
        $('#last_name').focus();


    } else {
        if (last_nameCount > 30) {
            $('#last_name_error').html('Maximum 30 words allowed');

            $('#last_name').focus();

        }else{
            $('#last_name_error').html('');
        }

    }
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        $('#email_error').html('Please enter valid email address');
        $("#email").focus();


    } else {
        $('#email_error').html('');
    }
    var phone = $('#phone').val();
    var phoneCount = phone ? phone.length : 0;
    if (phone == "") {
        $('#phone_error').html('Please select phone number');
        $('#phone').val('');
        $('#phone').focus();


    } else {

        if (phoneCount > 10) {
            $('#phone_error').html('Maximum 10 digit phone number allowed');

            $('#phone').focus();

        }else{
            $('#phone_error').html('');
        }

    }
    var department = $('#department').val();
    if (department == "") {
        $('#department_error').html('Please select department');
        $('#department').val('');
        $('#department').focus();


    } else {
        $('#department_error').html('');
    }

    var type = $('#type').val();
    if (type == "") {
        $('#type_error').html('Please Select Role');
        $('#type').val('');
        $('#type').focus();


    } else {
        $('#type_error').html('');
    }

    // var state = $('#state').val();
    // if (state == "") {
    //     $('#state_error').html('Please Select State');
    //     $('#state').val('');
    //     $('#state').focus();


    // } else {
    //     $('#state_error').html('');
    // }
    var city = $('#city').val();
    if(city.length > 50){
        $('#city_error').html('Maximum 50 characters allowed.');
    }else{
        $('#city_error').html('');
    }
    var profileStatus = $('#profilestatusedit').val();
    var onlineRate = $('#onlinerate').val();
    var inPersonRate = $('#inpersonrate').val();

    $('#profilestatusedit_error').html('');
    $('#onlinerate_error').html('');
    $('#inpersonrate_error').html('');

    if (profileStatus == '16' && onlineRate === '') {
        $('#onlinerate_error').html('Please enter online rate.');
    } else if (profileStatus == '17' && inPersonRate === '') {
        $('#inpersonrate_error').html('Please enter in-person rate.');
    } else if (profileStatus == '20') {
        if (onlineRate === '') {
            $('#onlinerate_error').html('Please enter online hourly rate.');
        }
        if (inPersonRate === '') {
            $('#inpersonrate_error').html('Please enter in-person hourly rate.');
        }

        if (onlineRate !== '' && inPersonRate !== '') {
            $('#onlinerate_error').html('');
            $('#inpersonrate_error').html('');
        }
    }

    if (first_name == "" || first_nameCount > 30 || last_name == "" || last_nameCount > 30 || type == "" || department == "" || phone == "" || phoneCount > 10 || regemailid == false || profileStatus == "") {
        return false;
    }

    var words = $('#about').val();
    if (words) {
    var wordCount = words ? words.length : 0;

      $('#wordCount').text('Words: ' + wordCount);

      // Perform validation
      if (wordCount > 501) {
        $('#about_error').html('Maximum 500 characters allowed.');
        $('#about').focus();
        //alert('Maximum 100 words allowed.');
        return false;
      }else {
        $('#about_error').html('');
    }
}
    // if (document.getElementById("file_data").files.length == 0) {
    //     $('#file_data_error').html('Please select profile image');
    //     $('#file_data').focus();
    //     return false;
    // } else {
    //     $('#file_data_error').html('');
    // }
    // var about = $('#about').val();
    // if (about == "") {
    //     $('#about_error').html('Please enter about us');
    //     $('#about').val('');
    //     $('#about').focus();
    //     return false;

    // } else {
    //     $('#about_error').html('');
    // }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    var url = APP_URL + '/save-admin';
    $.ajax({
        type: 'POST',
        url: url,
        data: new FormData($('#addadminform')[0]),
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {
            $(".loader").addClass('d-none');
            if (data.success == true) {
                alertify.success(data.message);
                $('#addadminform')[0].reset();
                setTimeout(function () {
                    window.location.href = data.redirect;
                }, 3000);


            } else {
                alertify.error(data.message);
            }

        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(3000);
        }, 500);
    });

});

$('body').on('click', '.admin_delete', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-admin';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})

$('body').on('click', '#editadminbutton', function (event) {
    event.preventDefault()
    var first_name = $('#first_name').val();
    if (first_name == "") {
        $('#first_name_error').html('Please enter first name');
        $('#first_name').val('');
        $('#first_name').focus();


    } else {
        $('#first_name_error').html('');
    }
    var last_name = $('#last_name').val();
    if (last_name == "") {
        $('#last_name_error').html('Please enter last Name');
        $('#last_name').val('');
        $('#last_name').focus();


    } else {
        $('#last_name_error').html('');
    }
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        $('#email_error').html('Please enter valid email address');
        $("#email").focus();


    } else {
        $('#email_error').html('');
    }
    var phone = $('#phone').val();
    if (phone == "") {
        $('#phone_error').html('Please select phone number');
        $('#phone').val('');
        $('#phone').focus();


    } else {
        $('#phone_error').html('');
    }
    var department = $('#department').val();
    if (department == "") {
        $('#department_error').html('Please select department');
        $('#department').val('');
        $('#department').focus();


    } else {
        $('#department_error').html('');
    }

    var type = $('#type').val();
    if (type == "") {
        $('#type_error').html('Please Select Role');
        $('#type').val('');
        $('#type').focus();


    } else {
        $('#type_error').html('');
    }

    // var state = $('#state').val();
    // if (state == "") {
    //     $('#state_error').html('Please Select State');
    //     $('#state').val('');
    //     $('#state').focus();


    // } else {
    //     $('#state_error').html('');
    // }

    if (first_name == "" || last_name == ""  || type == "" || department == "" || phone == "" || regemailid == false) {
        return false;
    }

    var words = $('#about').val();
    if (words) {
    var wordCount = words ? words.length : 0;

      $('#wordCount').text('Words: ' + wordCount);

      // Perform validation
      if (wordCount > 501) {
        $('#about_error').html('Maximum 500 characters allowed.');
        $('#about').focus();
        //alert('Maximum 100 words allowed.');
        return false;
      }else {
        $('#about_error').html('');
    }
}

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/admin-update';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#editadminform')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$(document).on('change', '.adminimagechange', function (e) {
    var th = $(this);
    var url = APP_URL + '/adminimagechange';

    $.ajax({
        type: 'POST',
        url: url,
        data: new FormData($('#adminimagechangeform')[0]),
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function (data) {
            alertify.success(data.message);
            $('#imagePreview').attr("src", APP_URL + '/public/profile/' + data.img);
            $('#trainerprofupload').attr("value", data.img);
        }
    })

})
$(".changeLang").change(function () {
    var url = APP_URL + '/changeLang';
    window.location.href = url + "?lang=" + $(this).val();
});


// $(document).ready(function () {
//     CKEDITOR.replace('certificate_description_en', {
//         filebrowserUploadUrl: "{{route('ckeditor.upload', ['_token' => csrf_token() ])}}",
//         filebrowserUploadMethod: 'form'
//     });
//     CKEDITOR.replace('certificate_description_de', {
//         filebrowserUploadUrl: "{{route('ckeditor.upload', ['_token' => csrf_token() ])}}",
//         filebrowserUploadMethod: 'form'
//     });
// })






$('#showPass').on('click', function () {

    var passInput = $("#passwords");
    if (passInput.attr('type') === 'password') {
        passInput.attr('type', 'text');
    } else {
        passInput.attr('type', 'password');
    }
})





$(document).ready(function () {
    var user_type = $('#user_type').val();
    if (user_type == '1') {
        $('#Student_list').show();
        $("#Trainer_list").addClass("d-none");

    } else {
        $('#Trainer_list').removeClass('d-none');
        $("#Student_list").addClass("d-none");
    }
})

$(document).on('change', '#user_type', function () {
    var user_type = $('#user_type').val();
    if (user_type == '1') {
        $('#Student_list').removeClass('d-none');
        $("#Trainer_list").addClass("d-none");
    } else if (user_type == '2') {
        $('#Trainer_list').removeClass('d-none');
        $("#Student_list").addClass("d-none");
    }
})













//role
$('body').on('click', '#editrolebutton', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    if (name == "") {
        $('#name_error').html('Please enter role name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }


    var status = $('#status').val();
    if (status == "") {
        $('#status_error').html('Please select status');
        $('#status').val('');
        $('#status').focus();
        return false;

    } else {
        $('#status_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/role-update';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#editroleform')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#addrolebutton', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    if (name == "") {
        $('#name_error').html('Please enter role name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }


    var status = $('#status').val();
    if (status == "") {
        $('#status_error').html('Please select status');
        $('#status').val('');
        $('#status').focus();
        return false;

    } else {
        $('#status_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/role-save';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addroleform')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);

                $('#changepwdform')[0].reset();
            } else {
                alertify.error(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.role_delete', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-role';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})
//role

//DISTRICT
$('body').on('click', '#add_disrtict', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    var contact_info = $('#contact_info').val();
    var address = $('#address').val();
    var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }

    // if (contact_info == "") {
    //     $('#contact_info_error').html('Please enter contact info');
    //     $('#contact_info').val('');
    //     $('#contact_info').focus();
    //     return false;

    // } else {
    //     $('#contact_info_error').html('');
    // }

    if (address == "") {
        $('#address_error').html('Please enter address info');
        $('#address_info').val('');
        $('#address_info').focus();
        return false;

    } else {
        $('#address_error').html('');
    }

    if (notes == "") {
        $('#notes_error').html('Please enter notes');
        $('#notes_info').val('');
        $('#notes_info').focus();
        return false;

    } else {
        $('#notes_error').html('');
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-district';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addistrict')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#addistrict')[0].reset();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#update_disrtict', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    var contact_info = $('#contact_info').val();
    var address = $('#address').val();
    var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }


    // if (contact_info == "") {
    //     $('#contact_info_error').html('Please enter contact info');
    //     $('#contact_info').val('');
    //     $('#contact_info').focus();
    //     return false;

    // } else {
    //     $('#contact_info_error').html('');
    // }

    if (address == "") {
        $('#address_error').html('Please enter address info');
        $('#address_info').val('');
        $('#address_info').focus();
        return false;

    } else {
        $('#address_error').html('');
    }

    if (notes == "") {
        $('#notes_error').html('Please enter notes');
        $('#notes_info').val('');
        $('#notes_info').focus();
        return false;

    } else {
        $('#notes_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update-district';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#updatedistrict')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.delete_data_district', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-district';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})
//DISTRICT

//cbo
$('body').on('click', '#add_cbo', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    var contact_info = $('#contact_info').val();
    var address = $('#address').val();
    var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }

    // if (contact_info == "") {
    //     $('#contact_info_error').html('Please enter contact info');
    //     $('#contact_info').val('');
    //     $('#contact_info').focus();
    //     return false;

    // } else {
    //     $('#contact_info_error').html('');
    // }

    if (address == "") {
        $('#address_error').html('Please enter address info');
        $('#address_info').val('');
        $('#address_info').focus();
        return false;

    } else {
        $('#address_error').html('');
    }

    if (notes == "") {
        $('#notes_error').html('Please enter notes');
        $('#notes_info').val('');
        $('#notes_info').focus();
        return false;

    } else {
        $('#notes_error').html('');
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-cbo';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addcbo')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#addcbo')[0].reset();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#update_cbo', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    var contact_info = $('#contact_info').val();
    var address = $('#address').val();
    var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }


    // if (contact_info == "") {
    //     $('#contact_info_error').html('Please enter contact info');
    //     $('#contact_info').val('');
    //     $('#contact_info').focus();
    //     return false;

    // } else {
    //     $('#contact_info_error').html('');
    // }

    if (address == "") {
        $('#address_error').html('Please enter address info');
        $('#address_info').val('');
        $('#address_info').focus();
        return false;

    } else {
        $('#address_error').html('');
    }

    if (notes == "") {
        $('#notes_error').html('Please enter notes');
        $('#notes_info').val('');
        $('#notes_info').focus();
        return false;

    } else {
        $('#notes_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update-cbo';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#updatecbo')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.delete_data_cbo', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-cbo';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})
//cbo


//Class
$('body').on('click', '#add_class', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    // var contact_info = $('#contact_info').val();
    // var address = $('#address').val();
    // var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }

    // if (contact_info == "") {
    //     $('#contact_info_error').html('Please enter contact info');
    //     $('#contact_info').val('');
    //     $('#contact_info').focus();
    //     return false;

    // } else {
    //     $('#contact_info_error').html('');
    // }

    // if (address == "") {
    //     $('#address_error').html('Please enter address info');
    //     $('#address_info').val('');
    //     $('#address_info').focus();
    //     return false;

    // } else {
    //     $('#address_error').html('');
    // }

    // if (notes == "") {
    //     $('#notes_error').html('Please enter notes');
    //     $('#notes_info').val('');
    //     $('#notes_info').focus();
    //     return false;

    // } else {
    //     $('#notes_error').html('');
    // }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-class';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addclass')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#addclass')[0].reset();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#update_class', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    var contact_info = $('#contact_info').val();
    var address = $('#address').val();
    var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }


    // if (contact_info == "") {
    //     $('#contact_info_error').html('Please enter contact info');
    //     $('#contact_info').val('');
    //     $('#contact_info').focus();
    //     return false;

    // } else {
    //     $('#contact_info_error').html('');
    // }

    // if (address == "") {
    //     $('#address_error').html('Please enter address info');
    //     $('#address_info').val('');
    //     $('#address_info').focus();
    //     return false;

    // } else {
    //     $('#address_error').html('');
    // }

    // if (notes == "") {
    //     $('#notes_error').html('Please enter notes');
    //     $('#notes_info').val('');
    //     $('#notes_info').focus();
    //     return false;

    // } else {
    //     $('#notes_error').html('');
    // }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update-class';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#updateclass')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.delete_data_class', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-class';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})
//Class



//Subject
$('body').on('click', '#add_subject', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    var classid = $('#class').val();
    var grade = $('#grade').val();
    var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter subject name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }



    // if (grade == "") {
    //     $('#grade_error').html('Please Select grade');
    //     $('#grade_info').val('');
    //     $('#grade_info').focus();
    //     return false;

    // } else {
    //     $('#grade_error').html('');
    // }


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-subject';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addsubject')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#addsubject')[0].reset();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#update_subject', function (event) {
    event.preventDefault()
    var name = $('#name').val();
    var classid = $('#class').val();
    var grade = $('#grade').val();
    var notes = $('#notes').val();
    if (name == "") {
        $('#name_error').html('Please enter subject name');
        $('#name').val('');
        $('#name').focus();
        return false;

    } else {
        $('#name_error').html('');
    }

    // if (classid == "") {
    //     $('#class_info_error').html('Please Select class');
    //     $('#class_info').val('');
    //     $('#class_info').focus();
    //     return false;

    // } else {
    //     $('#class_info_error').html('');
    // }

    // if (grade == "") {
    //     $('#grade_error').html('Please Select grade');
    //     $('#grade_info').val('');
    //     $('#grade_info').focus();
    //     return false;

    // } else {
    //     $('#grade_error').html('');
    // }

    // if (notes == "") {
    //     $('#notes_error').html('Please enter notes');
    //     $('#notes_info').val('');
    //     $('#notes_info').focus();
    //     return false;

    // } else {
    //     $('#notes_error').html('');
    // }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update-subject';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#updatesubject')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.delete_data_subject', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-subject';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})
//Subject

$('body').on('click', '#schoolchangepassword', function () {

    var new_password = document.getElementById('new_password').value;
    if (new_password.length == "") {
        $('.new_password_error').html('Please Enter Your New Password');
        document.getElementById('new_password').value = "";
        document.getElementById('new_password').focus();
        return false;
    } else {
        $('.new_password_error').html('');
        $('#new_password').css('border-color', '#495057');
    }

    var confirm_password = document.getElementById('confirm_password').value;
    if (confirm_password.length == "") {
        $('.confirm_password_error').html('Please Enter Your Confirm Password');
        document.getElementById('confirm_password').value = "";
        document.getElementById('confirm_password').focus();
        return false;
    } else {
        $('.confirm_password_error').html('');
        $('#confirm_password').css('border-color', '#495057');
    }

    if (new_password != confirm_password) {
        $('.confirm_password_error').html('The password and confirm password do not match!!');
        document.getElementById('confirm_password').value = "";
        document.getElementById('confirm_password').focus();
        return false;
    } else {
        $('.confirm_password_error').html('');
        $('#confirm_password').css('border-color', '#495057');
    }


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/updateChangePasswordforschool';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#school_changepassword').serialize(),
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                // $("#changepwdform").reset();
                $('#school_changepassword')[0].reset();
                window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $(".loader").addClass('d-none');
    });

})

$('body').on('click', '#staffchangepassword', function () {

    var new_password = document.getElementById('new_password').value;
    if (new_password.length == "") {
        $('.new_password_error').html('Please Enter Your New Password');
        document.getElementById('new_password').value = "";
        document.getElementById('new_password').focus();
        return false;
    } else {
        $('.new_password_error').html('');
        $('#new_password').css('border-color', '#495057');
    }

    var confirm_password = document.getElementById('confirm_password').value;
    if (confirm_password.length == "") {
        $('.confirm_password_error').html('Please Enter Your Confirm Password');
        document.getElementById('confirm_password').value = "";
        document.getElementById('confirm_password').focus();
        return false;
    } else {
        $('.confirm_password_error').html('');
        $('#confirm_password').css('border-color', '#495057');
    }

    if (new_password != confirm_password) {
        $('.confirm_password_error').html('The password and confirm password do not match!!');
        document.getElementById('confirm_password').value = "";
        document.getElementById('confirm_password').focus();
        return false;
    } else {
        $('.confirm_password_error').html('');
        $('#confirm_password').css('border-color', '#495057');
    }


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/updateChangePasswordforstaff';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#staff_changepassword').serialize(),
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
                // $("#changepwdform").reset();
                $('#staff_changepassword')[0].reset();
                window.location.href = data.redirect;
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $(".loader").addClass('d-none');
    });

})

$(document).on('change', '.getschool', function (e) {


    var id = document.getElementById('district').value;

    var url = APP_URL + '/getSchoolBydistrict/' + id;
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        type: 'GET',
        url: url,
        data: {
            id: id
        },
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function (data) {

            var html = `<option value="">Select School</option>`;

            for (var i = 0; i < data.data.length; i++) {

                html += `<option value="` + data.data[i].id + `">` + data.data[i].full_name + `(` + data.data[i].email + `)</option>`;
            }


            $('#schoolname').html(html);


        }
    })

})

$(document).on('click', '.sendcred', function (e) {


    var id = document.getElementById('useridc').value;

    var url = APP_URL + '/sendcred';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        type: 'post',
        url: url,
        data: {
            uid: id
        },

        dataType: 'json',
        success: function (data) {
            alertify.success(data.message);

        }
    })

})

$(document).on('change', '#organizationtype', function (e) {

    var dvalue = $('#organizationtype').val();

    if (dvalue == 'Public School')
        $('#districtid').css('display', 'block');
    else
        $('#districtid').css('display', 'none');
})

//interview
$('body').on('click', '#add_interview', function (event) {
    event.preventDefault()
    var link = $('#link').val();
    if (link == "") {
        $('#link_error').html('Please enter link');
        $('#link').val('');
        $('#link').focus();
        return false;

    } else {
        $('#link_error').html('');
    }






    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-Interview';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addInterview')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                location.reload();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})



//PROGRAM
$('body').on('click', '#add_program', function (event) {
    event.preventDefault();

    var delivery_type = $('#delivery_type').val();
    var program_status = $('#program_status').val();


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }
    var url = APP_URL + '/save-program';
    var formData = new FormData($('#addprogram')[0]);

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300);
            $('#addprogram').find('.is-invalid').removeClass('is-invalid');
            $('#addprogram').find('.invalid-feedback').remove();
            $('#addprogram').find('button .save-loader').show();
            $('#addprogram').find('button').prop("disabled", true);

        },
        success: function (response) {
            if (response.success == true) {
                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].setData('');
                }
                alertify.success(response.message);
                $('#addprogram')[0].reset();
                $('.select2').val('').trigger("change");
              if(delivery_type=='Online' && program_status=='Publish'){
                openAdminModal(response.redirect,'#common-admin-modal');
                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].setData('');
                }
              }
            } if (response.success == false) {
                alertify.error(response.message);
            }
            $('#addprogram').find('button .save-loader').hide();
            $('#addprogram').find('button').prop("disabled", false);

        }, error: function (data) {
            if (data.status == 422) {
                /*  $.each(data.responseJSON.errors, function (key, value) {
                     $('#addprogram').find('[name="' + key + '"]').addClass('is-invalid').focus();
                     $('#addprogram').find('[name="' + key + '"]').after('<div class="invalid-feedback">' + value + '</div>');
                 }); */
                $.each(data.responseJSON.errors, function (key, value) {

                    if (key.includes('.')) {
                        var fieldName = key.split('.')[0]; // Get the field name before '.'
                        var fieldIndex = key.split('.')[1]; // Get the field index after '.'
                        var inputFields = $('#addprogram').find('[name^="' + fieldName + '["]').eq(fieldIndex);
                    } else {
                        var inputFields = $('#addprogram').find('[name="' + key + '"]');
                    }

                    // Check if the element is a Select2 dropdown
                    if (inputFields.hasClass('select2')) {
                        inputFields.next('.select2-container').after('<div class="invalid-feedback d-block">' + value + '</div>');
                    } else {
                        // Apply validation styling to regular input elements
                        inputFields.addClass('is-invalid');
                        inputFields.after('<div class="invalid-feedback">' + value + '</div>');
                    }

                    inputFields.focus();

                });
            } else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $('#addprogram').find('button .save-loader').hide();
            $('#addprogram').find('button').prop("disabled", false);

        }
    }).done(function () {
        $('#addprogram').find('button .save-loader').hide();
        $('#addprogram').find('button').prop("disabled", false);

        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$(document).on('change input', '#updateprogram input,#updateprogram select,#addprogram input,#addprogram select', function (e) {
    // alert();
    $(this).removeClass('is-invalid');
    $(this).next('.invalid-feedback').remove();
    $(this).next().next('.invalid-feedback').remove();

});

$('#updateprogram').find('.is-invalid').removeClass('is-invalid');
// $('body').on('click', '#update_program', function (event) {
//     event.preventDefault();
//     for (instance in CKEDITOR.instances) {
//         CKEDITOR.instances[instance].updateElement();
//     }
//     if ($('#has_program_user_id').length > 0) {
//         swal({
//             title: "Update Program",
//             text: "Program has already been assigned to an instructor. Do you want to update?",
//             type: "warning",
//             showCancelButton: true,
//             confirmButtonText: "Yes!",
//             cancelButtonText: "No, cancel!",
//             reverseButtons: true
//         }).then(function (result) {
//             if (result.value) {
//                 performUpdateAjaxRequest();
//             }
//         });
//     } else {

//         performUpdateAjaxRequest();
//     }
// });

// function performUpdateAjaxRequest() {
//     $.ajaxSetup({
//         headers: {
//             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//         }
//     });
//     var delivery_type = $('#delivery_type').val();
//     var program_status = $('#program_status').val();

//     var url = APP_URL + '/update-program';
//     var formData = new FormData($('#updateprogram')[0]);

//     $.ajax({
//         url: url,
//         type: 'post',
//         processData: false,
//         contentType: false,
//         data: formData,
//         dataType: "json",
//         beforeSend: function () {
//             $("#overlay").fadeIn(300);
//             $('#updateprogram .is-invalid').removeClass('is-invalid');
//             $('#updateprogram .invalid-feedback').remove();
//             $('#updateprogram button .save-loader').show();
//             $('#updateprogram button').prop("disabled", true);
//         },
//         success: function (response) {
//             if (response.success) {
//                 alertify.success(response.message);
//                 if(delivery_type=='Online' && program_status=='Publish'){
//                     openAdminModal(response.redirect,'#common-admin-modal');
//                   }
//             }else{

//                 window.showAlert = function () {
//                     alertify.alert(response.message).setHeader('');
//                 }

//                 alertify.alert().setting('modal', true);

//                 window.showAlert();
//             }
//         },
//         error: function (data) {
//             if (data.status == 422) {
//                 handleValidationErrors(data,'#updateprogram');
//             } else {
//                 alertify.error(data.responseJSON.message, 'Error');
//             }
//         }
//     }).always(function () {
//         $('#updateprogram button .save-loader').hide();
//         $('#updateprogram button').prop("disabled", false);
//         setTimeout(function () {
//             $("#overlay").fadeOut(300);
//         }, 500);
//     });
// }

$('body').on('click', '#update_program', function (event) {
    event.preventDefault();
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }
    if ($('#has_program_user_id').length > 0) {
        // Initial confirmation
        swal({
            title: "Update Program",
            text: "Program has already been assigned to an instructor. Do you want to update?",
            icon: "warning",
            buttons: {
                cancel: "No, cancel!",
                confirm: "Yes!"
            },
            dangerMode: true
        }).then(function (result) {
            if (result) {
                // Check if further confirmation is needed
                $.ajax({
                    url: APP_URL + '/check-status', // URL without data
                    type: 'get',
                    data: { id: $('#program_id').val() }, // Data passed as query parameters
                    success: function (response) {
                        if (response.success) {
                            // alert(response);
                            // Confirm if program status is 'Publish'
                            if (response.needsConfirmation) {
                                swal({
                                    title: "Confirm Update",
                                    text: "Updating this program may affect the instructor's schedule. Do you want to proceed?",
                                    icon: "warning",
                                    buttons: {
                                        cancel: "No, cancel!",
                                        confirm: "Yes, update it!"
                                    },
                                    dangerMode: true
                                }).then(function (confirmUpdate) {
                                    if (confirmUpdate) {
                                        performUpdateAjaxRequest();
                                    }
                                });
                            } else {
                                performUpdateAjaxRequest();
                            }
                        } else {
                            alertify.error(response.message);
                        }
                    }
                });
            }
        });
    } else {
        var formData = new FormData($('#updateprogram')[0]);
        console.log(formData)
        $.ajax({
            url: APP_URL + '/check-status', // URL without data
            type: 'get',
            data: { id: $('#programid').val() }, // Data passed as query parameters
            success: function (response) {
                if (response.success) {
                    // alert(response);
                    // Confirm if program status is 'Publish'
                    if (response.needsConfirmation) {
                        swal({
                            title: "Confirm Update",
                            text: "Updating this program may affect the instructor's schedule. Do you want to proceed?",
                            icon: "warning",
                            buttons: {
                                cancel: "No, cancel!",
                                confirm: "Yes, update it!"
                            },
                            dangerMode: true
                        }).then(function (confirmUpdate) {
                            if (confirmUpdate) {
                                performUpdateAjaxRequest();
                            }
                        });
                    } else {
                        performUpdateAjaxRequest();
                    }
                } else {
                    alertify.error(response.message);
                }
            }
        });
    }
});

function performUpdateAjaxRequest() {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var delivery_type = $('#delivery_type').val();
    var program_status = $('#program_status').val();

    var url = APP_URL + '/update-program';
    var formData = new FormData($('#updateprogram')[0]);

    $.ajax({
        url: url,
        type: 'POST',
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300);
            $('#updateprogram .is-invalid').removeClass('is-invalid');
            $('#updateprogram .invalid-feedback').remove();
            $('#updateprogram button .save-loader').show();
            $('#updateprogram button').prop("disabled", true);
        },
        success: function (response) {
            if (response.success) {
                alertify.success(response.message);
                if (delivery_type === 'Online' && program_status === 'Publish') {
                    openAdminModal(response.redirect, '#common-admin-modal');
                }
            } else {
                alertify.error(response.message);
            }
        },
        error: function (data) {
            if (data.status === 422) {
                handleValidationErrors(data, '#updateprogram');
            } else {
                alertify.error(data.responseJSON.message, 'Error');
            }
        }
    }).always(function () {
        $('#updateprogram button .save-loader').hide();
        $('#updateprogram button').prop("disabled", false);
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
}


function handleValidationErrors(data,formid) {
    $.each(data.responseJSON.errors, function (key, value) {

        var inputFields = findInputFields(key,formid);

        if (inputFields.hasClass('select2')) {
            inputFields.next('.select2-container').after('<div class="invalid-feedback d-block">' + value + '</div>');
        } else {
            inputFields.addClass('is-invalid');
            inputFields.after('<div class="invalid-feedback">' + value + '</div>');
        }

        inputFields.focus();
    });
}

function findInputFields(key,formid) {
    if (key.includes('.')) {
        var fieldName = key.split('.')[0];
        var fieldIndex = key.split('.')[1];
        return $(formid+' [name^="' + fieldName + '["]').eq(fieldIndex);
    } else {
        return $(formid+' [name="' + key + '"]');
    }
}



function resetfunction(type) {
    $('#' + type)[0].reset();
}

// google.maps.event.addDomListener(window, 'load', initialize);
function initialize() {
    var input = document.getElementById('address');

    var autocomplete = new google.maps.places.Autocomplete(input);
    autocomplete.addListener('place_changed', function () {
        var place = autocomplete.getPlace();
        // place variable will have all the information you are looking for.

        document.getElementById("latitude").value = place.geometry['location'].lat();
        document.getElementById("longitude").value = place.geometry['location'].lng();
        var txt = place.geometry['location'].lat();
        var txt1 = place.geometry['location'].lng();
        var box = $("#address");
        box.val(box.val() + ', ' + txt);
        box.val(box.val() + ', ' + txt1);
    });
}






$('body').on('click', '.backgroundstatus', function () {

    var id = $(this).attr('data-id');
    var status = $(this).attr('data-status');
    var userid = $(this).attr('data-userid');

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/change_status_model';
    $.ajax({
        type: "POST",
        url: url,
        data: { 'id': id, 'status': status, 'userid': userid },
        dataType: "html",
        success: function (data) {

            $('#appendstatus').html(data);
            $('#statusmodel').modal('show');

        }
    });
})


$('body').on('change', '#status', function () {
    var option = $(this).find('option:selected').val();
    if (option == 2 || option == 0) {
        $('.noteclass').show();
    } else {
        $('.noteclass').hide();
    }
})
$('body').on('click', '#statusChange', function () {


    var option = document.getElementById('status').value;
    if (option.length == "") {
        $("#status").css("border-color", "red");
        //$('#experience_teaching_ages_error').html('Please Select Experience Teaching Ages');
        document.getElementById('status').value = "";
        document.getElementById('status').focus();
        return false;
    } else {
        $('#status_error').html('');
        $('#status').css('border-color', '#d8dadc');
    }
    var option = $('#status').find('option:selected').val();
    if (option == 2 || option == 0) {
        var note = document.getElementById('note').value;
        if (note.length == "") {
            $("#note").css("border-color", "red");

            document.getElementById('note').value = "";
            document.getElementById('note').focus();
            return false;
        } else {
            $('#note_error').html('');
        }
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/change_status';
    $.ajax({
        type: "POST",
        url: url,
        data: $('#statusid').serialize(), // serializes the form's elements.
        dataType: "json",
        success: function (data) {
            alertify.success(data.message);
            location.reload();
        }
    });
})


$('body').on('click', '#ratechangeSubmit', function () {


    var inpersonrate_chaeck = document.getElementById('inpersonrate_chaeck').value;
    if (inpersonrate_chaeck == 1) {
        var inpersonrate = document.getElementById('inpersonrate').value;
        if (inpersonrate) {
            $('#inpersonrate_error').html('');
            $('#inpersonrate').css('border-color', '#d8dadc');
        } else {
            $("#inpersonrate").css("border-color", "red");
            $('#inpersonrate_error').html('Enter inperson rate');
            document.getElementById('inpersonrate').value = "";
            document.getElementById('inpersonrate').focus();
            return false;
        }
    }
    var onlinerate_chaeck = document.getElementById('onlinerate_chaeck').value;
    if (onlinerate_chaeck == 2) {
        var onlinerate = document.getElementById('onlinerate').value;
        if (onlinerate) {
            $('#onlinerate_error').html('');
            $('#onlinerate').css('border-color', '#d8dadc');
        } else {
            $("#onlinerate").css("border-color", "red");
            $('#onlinerate_error').html('Enter online rate');
            document.getElementById('onlinerate').value = "";
            document.getElementById('onlinerate').focus();
            return false;
        }
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update_payrate';
    $.ajax({
        type: "POST",
        url: url,
        data: $('#ratechangeform').serialize(), // serializes the form's elements.
        dataType: "json",
        success: function (data) {
            alertify.success(data.message);
            location.reload();
        }
    });
})




$('body').on('click', '.viewnote', function () {
    $('#staticBackdropLabel').text('Note History');
    var id = $(this).attr('data-id');
    var userid = $(this).attr('data-userid');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/show_note_history';
    $.ajax({
        type: "POST",
        url: url,
        data: { 'id': id, 'userid': userid }, // serializes the form's elements.
        dataType: "html",
        success: function (data) {

            $('#appendstatus').html(data);
            $('#statusmodel').modal('show');

        }
    });
})


//training
$('body').on('click', '#add_training', function (event) {
    event.preventDefault()
    var training_type = $('#training_type').val();

    if (training_type == "") {
        $('#training_type_error').html('Please select type');
        $('#training_type').val('');
        $('#training_type').focus();
        return false;

    } else {
        $('#training_type_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-training';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addtraining')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#addtraining')[0].reset();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#update_training', function (event) {
    event.preventDefault()
    var training_type = $('#training_type').val();

    if (training_type == "") {
        $('#training_type_error').html('Please select type');
        $('#training_type').val('');
        $('#training_type').focus();
        return false;

    } else {
        $('#training_type_error').html('');
    }





    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update-training';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#updatetraining')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.delete_data_training', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-training';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})
//DISTRICT
//rubric
$('body').on('click', '#add_rubric', function (event) {
    event.preventDefault()
    var notes = $('#notes').val();

    if (notes == "") {
        $('#notes_error').html('Notes is required');
        $('#notes_type').val('');
        $('#notes_type').focus();
        return false;

    } else {
        $('#notes_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save_rubric';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addrubric')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);

            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})


$('body').on('change', '#profilestatus', function () {

    let selectedItem = $(this).children("option:selected").val();
    if (selectedItem) {

    } else {
        return;
    }
    $('#staticBackdropLabel').text('Profile Status Change');
    var userid = $('#user_id').val();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/show_status_model';
    $.ajax({
        type: "POST",
        url: url,
        data: { 'status': selectedItem, 'userid': userid }, // serializes the form's elements.
        dataType: "html",
        success: function (data) {
            $('#appendstatus').html(data);
            $('#statusmodel').modal('show');

        }
    });


})




$('body').on('click', '#profilestatusChange', function () {


    var option = document.getElementById('status').value;
    if (option.length == "") {
        $("#status").css("border-color", "red");
        document.getElementById('status').value = "";
        document.getElementById('status').focus();
        return false;
    } else {
        $('#status_error').html('');
        $('#status').css('border-color', '#d8dadc');
    }
    if (option == 21) {

        var inpersonrate_chaeck = document.getElementById('inpersonrate_chaeck').value;
        if (inpersonrate_chaeck == 1) {
            var inpersonrate = document.getElementById('inpersonrate').value;
            if (inpersonrate) {
                $('#inpersonrate_error').html('');
                $('#inpersonrate').css('border-color', '#d8dadc');
            } else {
                $("#inpersonrate").css("border-color", "red");
                $('#inpersonrate_error').html('Enter inperson rate');
                document.getElementById('inpersonrate').value = "";
                document.getElementById('inpersonrate').focus();
                return false;
            }
        }
        var onlinerate_chaeck = document.getElementById('onlinerate_chaeck').value;
        if (onlinerate_chaeck == 2) {
            var onlinerate = document.getElementById('onlinerate').value;
            if (onlinerate) {
                $('#onlinerate_error').html('');
                $('#onlinerate').css('border-color', '#d8dadc');
            } else {
                $("#onlinerate").css("border-color", "red");
                $('#onlinerate_error').html('Enter online rate');
                document.getElementById('onlinerate').value = "";
                document.getElementById('onlinerate').focus();
                return false;
            }
        }
    }

    let id = $('#user_id').val();
    let note = $('#note').val();
    // let inpersonrate =$('#inpersonrate').val();
    // let onlinerate =$('#onlinerate').val();
    if (inpersonrate) {

    } else {
        inpersonrate = 0;
    }

    if (onlinerate) {

    } else {
        onlinerate = 0;
    }
    status_update(id, note, onlinerate, inpersonrate)

})


//training
$('body').on('click', '#add_form', function (event) {
    event.preventDefault()
    var title = $('#title').val();

    if (title == "") {
        $('#title_error').html('Title is required');
        $('#title').val('');
        $('#title').focus();
        return false;

    } else {
        $('#title_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-form';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addform')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#addform')[0].reset();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#update_form', function (event) {
    event.preventDefault()
    var title = $('#title').val();

    if (title == "") {
        $('#title_error').html('Title is required');
        $('#title').val('');
        $('#title').focus();
        return false;

    } else {
        $('#title_error').html('');
    }





    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update-form';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#updateform')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.delete_data_form', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-form';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})

function status_update(id, note, onlinerate, inpersonrate) {
    var url = base_url + 'change-status-profile';

    var status = $('#profilestatus').val();


    confirm_message = 'Are you sure you want to change status ?';

    update_statusp(id, url, confirm_message, status, note, onlinerate, inpersonrate);
}

//form
$('body').on('click', '#add_background_form', function (event) {
    event.preventDefault()
    var state = $('#state').val();
    // var description = $('#description').val();
    var form = $('#form').val();
    var instructions = $('#instructions').val();
    var deadline = $('#deadline').val();
    var type = $('#type').val();
    var form_type = $('#form_type').val();
    var package_type = $('#package_type').val();


    if (state == "") {
        $('#state_error').html('State is required');
        $('#state').val('');
        $('#state').focus();
        return false;

    } else {
        $('#state_error').html('');
    }

    // if (description == "") {
    //     $('#description_error').html('Description is required');
    //     $('#description').val('');
    //     $('#description').focus();
    //     return false;

    // } else {
    //     $('#description_error').html('');
    // }
    if (type == 'background_check') {

        if (form == "") {
            $('#form_error').html('Form is required');
            $('#form').val('');
            $('#form').focus();
            return false;

        } else {
            $('#form_error').html('');
        }
    }
    if (instructions == "") {
        $('#instructions_error').html('Instructions is required');
        $('#instructions').val('');
        $('#instructions').focus();
        return false;

    } else {
        $('#instructions_error').html('');
    }
    if (form_type == "") {
        $('#form_type_error').html('Type is required');
        $('#form_type').val('');
        $('#form_type').focus();
        return false;

    } else {
        $('#form_type_error').html('');
    }
    if (form_type == 'Checkr') {

        if (package_type == "") {
            $('#package_type_error').html('Package is required');
            $('#package_type').val('');
            $('#package_type').focus();
            return false;

        } else {
            $('#package_type_error').html('');
        }
    }

    if (deadline == "") {
        $('#deadline_error').html('Deadline is required');
        $('#deadline').val('');
        $('#deadline').focus();
        return false;

    } else {
        $('#deadline_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/request-form';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#adbackgroundCheck')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300);
            $('#add_background_form').find('.save-loader').show();

        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#add_background_form').find('.save-loader').hide();

                $('#adbackgroundCheck')[0].reset();
                location.reload();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
            $('#add_background_form').find('.save-loader').hide();

        }, 500);
    });
})

$('body').on('click', '#assignSave', function (event) {
    event.preventDefault()

    var user_id = $('#user_id').val();
    var deadline = $('#deadline').val();
    var type = $('#type').val();
    let error = false;
    if (user_id == "") {
        $('#user_id_error').html(type + ' selection is required');
        $('#user_id').val('');
        $('#user_id').focus();
        error = true;


    } else {
        $('#deadline_error').html('');
    }
    if (deadline == "") {
        $('#deadline_error').html(' Invite Deadline is required');
        $('#deadline').val('');
        $('#deadline').focus();
        error = true;


    } else {
        $('#deadline_error').html('');
    }
    if (error) {
        return false;

    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-assign';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#assign_form')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                location.reload();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.filter', function () {
    $('#staticBackdropLabel').text('Filter');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/show_filter';
    $.ajax({
        type: "POST",
        url: url,
        dataType: "html",
        success: function (data) {

            $('#appendstatus').html(data);
            $('#statusmodel').modal('show');
        }
    });

})

$('body').on('click', '.filterins', function () {
    $('#staticBackdropLabel').text('Filter');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/show_filter_ins';
    $.ajax({
        type: "POST",
        url: url,
        dataType: "html",
        success: function (data) {

            $('#appendstatus').html(data);
            $('#statusmodel').modal('show');
            var modelElmBack = $(".modal-backdrop");
            var modelElm = $(".modal");
            modelElm.css("z-index", "1");
            modelElmBack.css("z-index", "0");
        }
    });

})


$('body').on('click', '#filtersubmit', function () {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/submitFilter';
    $.ajax({
        type: "POST",
        url: url,
        data: $('#filter').serialize(),
        dataType: "html",
        success: function (data) {
            $('#statusmodel').modal('hide');
            $('.filterdata').html(data);
            datatable();
        }
    });
})

$('body').on('click', '#filtersubmitins', function () {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/submitFilterins';
    $.ajax({
        type: "POST",
        url: url,
        data: $('#filter').serialize(),
        dataType: "html",
        success: function (data) {
            $('#statusmodel').modal('hide');
            $('.filterdata').html(data);
            datatable();
        }
    });
})


function datatable() {
    $('#datatables-column-search-text-inputs tfoot th,.datatable tfoot th').each(function () {
        var title = $(this).text();

        $(this).html('<input type="text" class="form-control" placeholder="Search ' + title + '" />');
    });
    // DataTables
    var table = $('#datatables-column-search-text-inputs').DataTable();
    // Apply the search
    table.columns().every(function () {
        var that = this;
        $('input', this.footer()).on('keyup change clear', function () {
            if (that.search() !== this.value) {
                that
                    .search(this.value)
                    .draw();
            }
        });
    });


}

// Sub-subject
$(document).ready(function () {
    var maxField = 100;
    var wrapper = $('.appendsubsubject');
    var x = 1;
    $('.addbtnn').click(function () {
        if (x < maxField) {
            x++;
            $(wrapper).last().after(
                '<div class="row appends appendsubsubject' + x + '"><div class="mb-3" id="permissionform"></div><div class="col-md-6 form-group"><input type="text" id="name" placeholder="Enter name" class="form-control" name="name[]"><span id="name_error" class="err"></span></div><div class="col-md-6 form-group">  <textarea  id="topic"  class="form-control " placeholder="Enter topic" name="topic[]"></textarea> <span id="topic_error" class="err"></span></div><div class="col-md-11 form-group"><textarea  class="form-control editor1" name="file_data_online[]"></textarea><span id="file_data_online_error" class="err"></span></div><input type="hidden" id="file_data_online_hidden" name="file_data_online_hidden[]"><div class="col-md-6 form-group" style="display:none;"><textarea  class="form-control editor1" name="file_data_in[]"></textarea><span id="file_data_in_error" class="err"></span></div><div class="col-md-1 form-group mt-4"><button type="button" name="add" id="add" class="btn btn-danger remove_button appbtn">-</button></div>'
            );
            const customEvent = new CustomEvent('RefreshCKeditor')
            document.dispatchEvent(customEvent);
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });
    $('body').on('click', '.remove_button', function (e) {
        $(this).parents(".after-add-more").remove();
    });
});

$('body').on('click', '#add_sub_subject', function () {
    var onlinerange = false;
    $('.appends').find('input[type=text]').each(function () {
        if (this.hasAttribute("name")) {
            if ($(this).val() == "" || $(this).length == "") {
                onlinerange = false;
                $(this).css("border-color", "red");
            } else {
                $(this).css("border-color", "");
                onlinerange = true;
            }
        }
    });
    if (onlinerange == true) {
        for (var instance in CKEDITOR.instances) {
            CKEDITOR.instances[instance].updateElement();
        }
        $('.editor1').each(function () {
            var editorData = $(this).val();
            $(this).next('input[type="hidden"]').val(editorData);
        });
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        var url = APP_URL + '/submit-sub-subject';
        $.ajax({
            url: url,
            type: 'post',
            processData: false,
            contentType: false,
            data: new FormData($('#addsubsubject')[0]),
            dataType: "json",
            beforeSend: function () {
                $('#addsubsubject').find('button .save-loader').show();
                $('#addsubsubject').find('button').prop("disabled", true);
                $("#overlay").fadeIn(300)
            },
            success: function (response) {
                if (response.success == true) {
                    $('#addsubsubject').find('button .save-loader').show();
                    $('#addsubsubject').find('button').prop("disabled", true);
                    alertify.success(response.message);
                    location.reload();
                }
            },
        }).done(function () {
            setTimeout(function () {
                $("#overlay").fadeOut(300);
            }, 500);
        });
    }
});


//resources
$('body').on('click', '#add_resources', function (event) {
    event.preventDefault()
    var resource_type = $('#resource_type').val();
    var title = $('#title').val();

    if (resource_type == "") {
        $('#resource_type_error').html('Please Resource type');
        $('#resource_type').val('');
        $('#resource_type').focus();
        return false;

    } else {
        $('#resource_type_error').html('');
    }

    if (title == "") {
        $('#title_error').html('Please enter title');
        $('#title').val('');
        $('#title').focus();
        return false;

    } else {
        $('#title_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-resources';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addresource')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#addresource')[0].reset();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#update_resources', function (event) {
    event.preventDefault()



    var resource_type = $('#resource_type').val();
    var title = $('#title').val();

    if (resource_type == "") {
        $('#resource_type_error').html('Please Resource type');
        $('#resource_type').val('');
        $('#resource_type').focus();
        return false;

    } else {
        $('#resource_type_error').html('');
    }

    if (title == "") {
        $('#title_error').html('Please enter title');
        $('#title').val('');
        $('#title').focus();
        return false;

    } else {
        $('#title_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/update-resources';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#updateresources')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '.delete_data_resources', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-resources';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})

$('body').on('click', '#edit_instructor', function (event) {
    event.preventDefault()
    var first_name = $('#first_name').val();
    if (first_name == "") {
        $('#first_name_error').html('Please enter first name');
        $('#first_name').val('');
        $('#first_name').focus();
        return false;

    } else {
        $('#first_name_error').html('');
    }
    var last_name = $('#last_name').val();
    if (last_name == "") {
        $('#last_name_error').html('Please enter last Name');
        $('#last_name').val('');
        $('#last_name').focus();
        return false;

    } else {
        $('#last_name_error').html('');
    }

    var profilestatusedit = $('#profilestatusedit').val();
    if (profilestatusedit == "") {
        $('#profilestatusedit_error').html('Please select profile status');
        $('#profilestatusedit').val('');
        $('#profilestatusedit').focus();
        return false;

    } else {
        $('#profilestatusedit_error').html('');
    }
    if (profilestatusedit == '16' || profilestatusedit == '20') {
        var onlinerate = $('#onlinerate').val();
        if (onlinerate == "") {
            $('#onlinerate_error').html('Please enter online rate');
            $('#onlinerate').val('');
            $('#onlinerate').focus();
            return false;

        } else {
            $('#onlinerate_error').html('');
        }
    }
    if (profilestatusedit == '17' || profilestatusedit == '20') {
        var inpersonrate = $('#inpersonrate').val();
        if (inpersonrate == "") {
            $('#inpersonrate_error').html('Please enter in-person rate');
            $('#inpersonrate').val('');
            $('#inpersonrate').focus();
            return false;

        } else {
            $('#inpersonrate_error').html('');
        }
    }
    // var city = $('#city').val();
    // if (city == "") {
    //     $('#city_error').html('Please enter city');
    //     $('#city').val('');
    //     $('#city').focus();
    //     return false;

    // } else {
    //     $('#city_error').html('');
    // }


    // var state = $('#state').val();
    // if (state == "") {
    //     $('#state_error').html('Please select state');
    //     $('#state').val('');
    //     $('#state').focus();
    //     return false;

    // } else {
    //     $('#state_error').html('');
    // }

    // var zipcode = $('#zipcode').val();
    // if (zipcode == "") {
    //     $('#zipcode_error').html('Please enter zipcode');
    //     $('#zipcode').val('');
    //     $('#zipcode').focus();
    //     return false;

    // } else {
    //     $('#zipcode_error').html('');
    // }

    var words = $('#specify').val();
    if (words) {
    var wordCount = words ? words.length : 0;

      $('#wordCount').text('Words: ' + wordCount);

      // Perform validation
      if (wordCount > 501) {
        $('#specify_error').html('Maximum 500 characters allowed.');
        $('#specify').focus();
        //alert('Maximum 100 words allowed.');
        return false;
      }else {
        $('#specify_error').html('');
    }
}



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/instructor-update';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#editinstructor')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                window.location.href='/instructor-list';
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('body').on('click', '#appassignSave', function (event) {
    event.preventDefault()

    var user_id = $('#user_id').val();
    var type = $('#type').val();

    if (user_id == "") {
        $('#user_id_error').html(type + ' selection is required');
        $('#user_id').val('');
        $('#user_id').focus();
        return false;

    } else {
        $('#user_id_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-app-assign';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#appassign_form')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                location.reload();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$(document).ready(function () {
    var pathArray = window.location.pathname.split('/');
    if (pathArray[1] == 'instructor-chat' || pathArray[1] == 'new-application-list' || pathArray[1] == 'adminchat' || pathArray[1] == 'admin-dashboard') {
        var url = APP_URL + '/get_user_admin';
        $.ajax({
            type: 'GET',
            url: url,
            dataType: "json",

            success: function (data) {
                // firebaseauth(data.email,data.password);
                firebasesignin(data.email, data.password);

            },
        })


    }
});



function firebaseauth(email, password) {
    auth.createUserWithEmailAndPassword(email, password)
        .then((userCredential) => {
            // User signed in successfully
            const user = userCredential.user;
        })
        .catch((error) => {
            // Handle login errors
        })
}

function updateRequest(url, message = "Please ensure and then confirm!", title = "Update Request") {

    swal({
        title: title,
        text: message,
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                type: 'POST',
                url: url,
                data: {},
                dataType: "json",

                success: function (res) {

                    if (res.status == true) {
                        alertify.success(res.message);
                        drawAdminTables();

                        if (res.reload) {

                            location.reload();
                        }
                    } else {
                        alertify.error(res.message);
                    }

                },
            }).done(function () {

            });

        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
}

function firebasesignin(email, password) {
    auth.signInWithEmailAndPassword(email, password)
        .then((userCredential) => {
            // User signed in successfully
            const user = userCredential.user;
        })
        .catch((error) => {
            // Handle login errors
            console.error('Login error:', error.message);
        });
}

$('body').on('click', '.modelmail', function () {
    var id = $(this).attr("data-id");
    $('#staticBackdropLabel').text('Send mail');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/show_mail_form/' + id;
    $.ajax({
        type: "GET",
        url: url,
        dataType: "html",
        success: function (data) {

            $('#appendstatus').html(data);
            $('#statusmodel').modal('show');
            var modelElmBack = $(".modal-backdrop");
            var modelElm = $(".modal");
            modelElm.css("z-index", "1");
            modelElmBack.css("z-index", "0");
        }
    });

})

$('body').on('click', '.delete_data_review', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/deletereview';
            $.ajax({
                type: 'POST',
                url: url,
                data: {
                    id: id
                },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');

                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);

            });

        } else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})
$('body').on('click', '.notification_delete', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            var url = APP_URL + '/delete-notification';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    currentRow.parents('tr').remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
})


$('body').on('click', '#add_program_background_form', function (event) {
    event.preventDefault()
    var state = $('#state').val();
    var description = $('#description').val();
    var form = $('#form').val();
    var instructions = $('#instructions').val();
    var deadline = $('#deadline').val();
    var type = $('#type').val();
    var form_type = $('#form_type').val();
    var package_type = $('#package_type').val();


    if (state == "") {
        $('#state_error').html('State is required');
        $('#state').val('');
        $('#state').focus();
        return false;

    } else {
        $('#state_error').html('');
    }

    // if (description == "") {
    //     $('#description_error').html('Description is required');
    //     $('#description').val('');
    //     $('#description').focus();
    //     return false;

    // } else {
    //     $('#description_error').html('');
    // }
    if (type == 'background_check') {

        if (form == "") {
            $('#form_error').html('Form is required');
            $('#form').val('');
            $('#form').focus();
            return false;

        } else {
            $('#form_error').html('');
        }
    }
    if (instructions == "") {
        $('#instructions_error').html('Instructions is required');
        $('#instructions').val('');
        $('#instructions').focus();
        return false;

    } else {
        $('#instructions_error').html('');
    }
    if (form_type == "") {
        $('#form_type_error').html('Type is required');
        $('#form_type').val('');
        $('#form_type').focus();
        return false;

    } else {
        $('#form_type_error').html('');
    }
    if (form_type == 'Checkr') {

        if (package_type == "") {
            $('#package_type_error').html('Package is required');
            $('#package_type').val('');
            $('#package_type').focus();
            return false;

        } else {
            $('#package_type_error').html('');
        }
    }

    if (deadline == "") {
        $('#deadline_error').html('Deadline is required');
        $('#deadline').val('');
        $('#deadline').focus();
        return false;

    } else {
        $('#deadline_error').html('');
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/request-program-form';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addprogrambackgroundCheck')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300);
            $('#add_program_background_form').find('.save-loader').show();

        },
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                $('#add_program_background_form').find('.save-loader').hide();

                $('#addprogrambackgroundCheck')[0].reset();
                location.reload();
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
            $('#add_program_background_form').find('.save-loader').hide();

        }, 500);
    });
})

// $('body').on('click', '.admin_payment_delete', function () {
//     var id = $(this).attr("data-id");
//     currentRow = $(this);
//     swal({
//         title: "Delete?",
//         text: "Please ensure and then confirm!",
//         type: "warning",
//         showCancelButton: !0,
//         confirmButtonText: "Yes, delete it!",
//         cancelButtonText: "No, cancel!",
//         reverseButtons: !0
//     }).then(function (e) {
//         if (e.value === true) {
//             $.ajaxSetup({
//                 headers: {
//                     'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//                 }
//             });
//             var url = APP_URL + '/delete-payment-admin';
//             $.ajax({
//                 type: 'POST',
//                 url: url,
//                 data: { id: id },
//                 dataType: "json",
//                 beforeSend: function () {
//                     $(".loader").removeClass('d-none');
//                 },
//                 success: function (data) {
//                     currentRow.parents('tr').remove();
//                     $(".loader").addClass('d-none');
//                     if (data.success == true) {
//                         alertify.success(data.message);
//                     } else {
//                         alertify.error(data.message);
//                     }

//                 },
//             }).done(function () {
//                 setTimeout(function () {
//                     $(".loader").addClass('d-none');
//                 }, 500);
//             });
//         }
//         else {
//             e.dismiss;
//         }
//     }, function (dismiss) {
//         return false;
//     })
// })

// $('body').on('click', '.admin_payment_delete', function () {
//     var id = $(this).attr("data-id");
//     currentRow = $(this);

//     // First confirmation: "Are you sure?"
//     swal({
//         title: "Delete?",
//         text: "Are you sure you want to delete this payment?",
//         type: "warning",
//         showCancelButton: true,
//         confirmButtonText: "Yes, delete it!",
//         cancelButtonText: "No, cancel!",
//         reverseButtons: true
//     }).then(function (result) {
//         if (result.value === true) {
//             // Second prompt: Ask for the reason for deletion
//             swal({
//                 title: "Reason for Deletion",
//                 input: 'text',
//                 inputPlaceholder: 'Enter the reason for deletion...',
//                 showCancelButton: true,
//                 confirmButtonText: "Submit",
//                 cancelButtonText: "Cancel"
//             }).then(function (reasonResult) {
//                 if (reasonResult.value) {
//                     var reason = reasonResult.value;

//                     $.ajaxSetup({
//                         headers: {
//                             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//                         }
//                     });

//                     var url = APP_URL + '/delete-payment-admin';

//                     $.ajax({
//                         type: 'POST',
//                         url: url,
//                         data: { id: id, reason: reason }, // Send reason to the server
//                         dataType: "json",
//                         beforeSend: function () {
//                             $(".loader").removeClass('d-none');
//                         },
//                         success: function (data) {
//                             currentRow.parents('tr').remove();
//                             $(".loader").addClass('d-none');
//                             if (data.success == true) {
//                                 alertify.success(data.message);
//                             } else {
//                                 alertify.error(data.message);
//                             }
//                         },
//                         error: function (error) {
//                             alertify.error("An error occurred.");
//                         }
//                     }).done(function () {
//                         setTimeout(function () {
//                             $(".loader").addClass('d-none');
//                         }, 500);
//                     });
//                 }
//             });
//         }
//     });
// });



$('body').on('click', '.admin_payment_delete', function () {
    var id = $(this).attr("data-id");
    currentRow = $(this);

    // First confirmation: "Are you sure?"
    swal({
        title: "Delete?",
        text: "Are you sure you want to delete this payment?",
        type: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: true
    }).then(function (result) {
        if (result.value === true) {
            // Open the modal for the reason
            $('#deleteReasonModal').modal('show');
            console.log('h')
            $('#deleteReasonModal').find('#delete_payment_id').val(id); // Set the payment ID in the hidden input
        }
    });
});

// Handle form submission when the modal is submitted
$('#deleteReasonForm').on('submit', function (e) {
    e.preventDefault();
    var reason = $('#delete_reason').val();
    var id = $('#delete_payment_id').val();

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    var url = APP_URL + '/delete-payment-admin';

    $.ajax({
        type: 'POST',
        url: url,
        data: { id: id, reason: reason }, // Send reason and id to the server
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {
            currentRow.parents('tr').remove();
            $('#deleteReasonModal').modal('hide'); // Hide the modal after successful submission
            $(".loader").addClass('d-none');
            if (data.success == true) {
                alertify.success(data.message);
            } else {
                alertify.error(data.message);
            }
        },
        error: function (error) {
            alertify.error("An error occurred.");
        }
    }).done(function () {
        setTimeout(function () {
            $(".loader").addClass('d-none');
        }, 500);
    });
});


// MARKETPLACE PROGRAM
$('body').on('click', '#add_marketplace_program', function (event) {
    event.preventDefault();

    var delivery_type = $('#delivery_type').val();
    var program_status = $('#program_status').val();


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    for (instance in CKEDITOR.instances) {
        CKEDITOR.instances[instance].updateElement();
    }
    var url = APP_URL + '/admin/k12connections/save-program';
    var formData = new FormData($('#addprogram')[0]);

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300);
            $('#addprogram').find('.is-invalid').removeClass('is-invalid');
            $('#addprogram').find('.invalid-feedback').remove();
            $('#addprogram').find('button .save-loader').show();
            $('#addprogram').find('button').prop("disabled", true);

        },
        success: function (response) {
            if (response.success == true) {
                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].setData('');
                }
                alertify.success(response.message);
                $('#addprogram')[0].reset();
                $('.select2').val('').trigger("change");
              if(delivery_type=='Online' && program_status=='Publish'){
                openAdmink12Modal(response.redirect,'#common-admin-modal');
                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].setData('');
                }
              }
            } if (response.success == false) {
                alertify.error(response.message);
            }
            $('#addprogram').find('button .save-loader').hide();
            $('#addprogram').find('button').prop("disabled", false);

        }, error: function (data) {
            if (data.status == 422) {
                /*  $.each(data.responseJSON.errors, function (key, value) {
                     $('#addprogram').find('[name="' + key + '"]').addClass('is-invalid').focus();
                     $('#addprogram').find('[name="' + key + '"]').after('<div class="invalid-feedback">' + value + '</div>');
                 }); */
                $.each(data.responseJSON.errors, function (key, value) {

                    if (key.includes('.')) {
                        var fieldName = key.split('.')[0]; // Get the field name before '.'
                        var fieldIndex = key.split('.')[1]; // Get the field index after '.'
                        var inputFields = $('#addprogram').find('[name^="' + fieldName + '["]').eq(fieldIndex);
                    } else {
                        var inputFields = $('#addprogram').find('[name="' + key + '"]');
                    }

                    // Check if the element is a Select2 dropdown
                    if (inputFields.hasClass('select2')) {
                        inputFields.next('.select2-container').after('<div class="invalid-feedback d-block">' + value + '</div>');
                    } else {
                        // Apply validation styling to regular input elements
                        inputFields.addClass('is-invalid');
                        inputFields.after('<div class="invalid-feedback">' + value + '</div>');
                    }

                    inputFields.focus();

                });
            } else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $('#addprogram').find('button .save-loader').hide();
            $('#addprogram').find('button').prop("disabled", false);

        }
    }).done(function () {
        $('#addprogram').find('button .save-loader').hide();
        $('#addprogram').find('button').prop("disabled", false);

        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

function openAdmink12Modal(url, modalId = "#common-admin-modal", data = {}) {

    $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeSelect2();
            adminDataTable();
        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });

}

