function Toggle() {


    var changeTextId = $('#changeTextId').html();

    if (changeTextId == 'Reveal') {
        $('#changeTextId').html('Hide');
    } else {
        $('#changeTextId').html('Reveal');
    }
    $('.pass-hints').toggle('slow');
}

function openpassword() {


    $('#eyeclose').css('display', 'none');
    $('#eyeopen').css('display', 'block');
    $('.passid').attr("type", "text");
    $('.p').attr("type", "text");
}

function openpassword2() {

    $('#eyeclose').css('display', 'block');
    $('#eyeopen').css('display', 'none');
    $('.passid').attr("type", "password");
    $('.p').attr("type", "password");

}

function openpassword1() {


    $('#eyeclose1').css('display', 'none');
    $('#eyeopen2').css('display', 'block');
    $('.cp').attr("type", "text");
}

function openpassword22() {

    $('#eyeclose1').css('display', 'block');
    $('#eyeopen2').css('display', 'none');
    $('.cp').attr("type", "password");
}


$('body').on('click', '#submitverify', function () {

    var loading = $(this).attr('data-loading-text');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var $this = $(this);
    var url = APP_URL + '/verify_email';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#verifyemail').serialize(),
        dataType: "json",
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);

        },
        success: function (data) {

            if (data.success == true) {
                $this.html('Resend Email');
                $this.prop("disabled", false);

                $("#verifyemail")[0].reset();
                alertify.success(data.message);

            } else {
                $this.html('Resend Email');
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $this.html('Resend Email');
        $this.prop("disabled", false);
    });
})

$('body').on('click', '#pendingsubmitverify', function () {

    var loading = $(this).attr('data-loading-text');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var $this = $(this);
    var url = APP_URL + '/verify_email';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#pendingverifyemail').serialize(),
        dataType: "json",
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);

        },
        success: function (data) {

            if (data.success == true) {
                $this.html('Resend Email');
                $this.prop("disabled", false);


                alertify.success(data.message);
                window.location.href = APP_URL + '/verify';
            } else {

                $this.html('Resend Email');
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $this.html('Resend Email');
        $this.prop("disabled", false);
        window.location.href = APP_URL + '/verify';
    });
})

$('body').on('click', '#singup', function () {
    var loading = $(this).attr('data-loading-text');
    var email = document.getElementById('email').value;
    var password = document.getElementById('password').value;
    var first_name = document.getElementById('first_name').value;
    var last_name = document.getElementById('last_name').value;
    var forgot = document.getElementById('forgot').value;
    if (email == "") {
        $("#email").css("border-color", "red");
        $("#email_error").html('Please enter valid email address');
    }
    if (password == "") {
        $("#password").css("border-color", "red");
        $("#password_error").html('Please enter your password');
    }
    if (first_name == "") {
        $("#first_name").css("border-color", "red");
        $("#first_name_error").html('Please enter your first name');
    }
    if (last_name == "") {
        $("#last_name").css("border-color", "red");
        $("#last_name_error").html('Please enter your last name');
    }
    if (password != "") {
        const messages = checkPasswordValidity(password);
        if (!messages) {
        } else {
            $('#password_error').html('<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>');
            $("#password").css("border-color", "red");
        }
    }

    if ($("#forgot").is(":checked") == false) {

        $('#forgot').css({ "height": "15px;" });
        $('#forgot').css('border-radius', '21px');
        $('#forgot').css('border', '5px solid red');
        $('#forgot').css('outline-color', 'red');
        $('#forgot').css('outline-style', 'auto');

    }
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        // $('#email_error').html('Please enter valid email address');
        $("#email").css("border-color", "red");
        $("#email").focus();

        return false;
    } else {
        // $('#email_error').html('');
        $("#email").css("border-color", "#d8dadc");
    }

    var password = document.getElementById('password').value;
    if (password.length == "") {
        // $('#password_error').html('Please Enter Your Password');
        $("#password").css("border-color", "red");
        document.getElementById('password').value = "";
        document.getElementById('password').focus();
        return false;
    } else {
        $('#password_error').html('');

        $('#password').css('border-color', '#d8dadc');
    }

    const message = checkPasswordValidity(password);
    if (!message) {
    } else {
        $("#password").css("border-color", "red");
        // Must contain at least one number one symbol one uppercase and one lowercase letter, and at least 10 or more characters
        $('#password_error').html('<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>');
        // document.getElementById('password').value = "";
        document.getElementById('password').focus();
        return false;
    }
    var first_name = document.getElementById('first_name').value;
    if (first_name.length == "") {
        // $('#first_name_error').html('Please Enter Your First Name');
        $("#first_name").css("border-color", "red");
        document.getElementById('first_name').value = "";
        document.getElementById('first_name').focus();
        return false;
    } else {
        $('#first_name_error').html('');
        $('#first_name').css('border-color', '#d8dadc');
    }
    var last_name = document.getElementById('last_name').value;
    if (last_name.length == "") {
        // $('#last_name_error').html('Please Enter Your Last Name');
        $("#last_name").css("border-color", "red");
        document.getElementById('last_name').value = "";
        document.getElementById('last_name').focus();
        return false;
    } else {
        $('#last_name_error').html('');
        $('#last_name').css('border-color', '#d8dadc');
    }
    if ($("#forgot").is(":checked") == false) {
        $('#forgot').css({ "height": "15px;" });
        $('#forgot').css('border-radius', '21px');
        $('#forgot').css('border', '5px solid red');
        $('#forgot').css('outline-color', 'red');
        $('#forgot').css('outline-style', 'auto');
        return false;
    } else {
        $('#agree_error').html('');
        $('#agree_error').css('border-color', '#d8dadc');
    }
    var $this = $(this);
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/create_user';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#signupid').serialize(),
        dataType: "json",
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {

            if (data.success == true) {
                $("#signupid")[0].reset();
                window.location.href = APP_URL + '/verify';
                $this.html('Sign Up');
                $this.prop("disabled", false);
            } else if (data.success == 'already') {
                $("#email").css("border-color", "red");
                document.getElementById('email').focus();
                $('#email_error').html(data.message);
                $this.html('Sign Up');
                $this.prop("disabled", false);

            }
            else {
                $this.html('Sign Up');
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $this.html('Sign Up');
        $this.prop("disabled", false);


    });

})
function checkPasswordValidity(value) {
    const isNonWhiteSpace = /^\S*$/;
    if (!isNonWhiteSpace.test(value)) {
        return "Password must not contain Whitespaces.";
    }

    const isContainsUppercase = /^(?=.*[A-Z]).*$/;
    if (!isContainsUppercase.test(value)) {
        return "Password must have at least one Uppercase Character.";
    }

    const isContainsLowercase = /^(?=.*[a-z]).*$/;
    if (!isContainsLowercase.test(value)) {
        return "Password must have at least one Lowercase Character.";
    }

    const isContainsNumber = /^(?=.*[0-9]).*$/;
    if (!isContainsNumber.test(value)) {
        return "Password must include at least 1 number.";
    }

    const isContainsSymbol =
        /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/;
    if (!isContainsSymbol.test(value)) {
        return "Password must include at least 1 special character.";
    }

    const isValidLength = /^.{10,20}$/;
    if (!isValidLength.test(value)) {
        return "Password must be 10-20 Characters Long.";
    }

    return null;
}



function checkRadio(value) {
    if (value == '0') {
        $('.firststepyes').hide();
        $('#firststepno').show();
        $('.firststepyes1').hide();
        $('#reside_united_states_error').hide();

    } else {
        $('#firststepno').hide();
        $('.firststepyes').show();
        $('.firststepyes1').show();
        var i_am_authorized = document.querySelector('input[name="i_am_authorized"]:checked').value;

        if (i_am_authorized == 'no') {
            $('.authyesno').hide();
            $('.firststepyes1').attr("style", "display: none !important");
        } else {
            $('.authyesno').show();
        }
    }
}
function checkRadioAuth(value) {
    if (value == '0') {
        $('.authyesno').hide();

        $('.firststepyes1').attr("style", "display: none !important");
        $('.iamauthoerror').show();
    } else {

        $('.authyesno').show();
        $('.firststepyes1').attr("style", "display: block !important");
        $('.iamauthoerror').hide();
    }
}

function savefirststep(type, _this) {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    if ($("#reside_united_states_yes").is(":checked") == false) {

        $('#reside_united_states_yes').css('outline-color', 'red');
        $('#reside_united_states_yes').css('outline-style', 'solid');
        $('#reside_united_states_yes').css('outline-width', 'thin');
        $('#reside_united_states_yes').css('height', '18px');
    }
    if ($("#reside_united_states_no").is(":checked") == false) {

        $('#reside_united_states_no').css('outline-color', 'red');
        $('#reside_united_states_no').css('outline-style', 'solid');
        $('#reside_united_states_no').css('outline-width', 'thin');
        $('#reside_united_states_no').css('height', '18px');
    }
    var city = document.getElementById('city').value;
    if (city == "") {
        $("#city").css("border-color", "red");
    }
    var state = document.getElementById('state').value;
    if (state == "") {
        $("#state").css("border-color", "red");
    }
    var zip_code = document.getElementById('zip_code').value;
    if (zip_code == "") {
        $("#zip_code").css("border-color", "red");
    }


    var specify_you_work_authorization = document.getElementById('specify_you_work_authorization').value;
    if (specify_you_work_authorization == "") {
        $("#specify_you_work_authorization").css("border-color", "red");
    }


    if (!document.getElementById("reside_united_states_yes").checked && !document.getElementById("reside_united_states_no").checked) {
        // $('#reside_united_states_error').html('Please select do you reside in the United States');
        $('#reside_united_states_yes').css('outline-color', 'red');
        $('#reside_united_states_yes').css('outline-style', 'solid');
        $('#reside_united_states_yes').css('outline-width', 'thin');
        $('#reside_united_states_yes').css('height', '18px');
        $('#reside_united_states_no').css('outline-color', 'red');
        $('#reside_united_states_no').css('outline-style', 'solid');
        $('#reside_united_states_no').css('outline-width', 'thin');
        $('#reside_united_states_no').css('height', '18px');
        document.getElementsByName('reside_united_states')[0].focus();
        return false;
    } else {
        $('#reside_united_states_yes').css('outline-color', '');
        $('#reside_united_states_yes').css('outline-style', '');
        $('#reside_united_states_yes').css('outline-width', '');

        $('#reside_united_states_no').css('outline-color', '');
        $('#reside_united_states_no').css('outline-style', '');
        $('#reside_united_states_no').css('outline-width', '');

        $('#reside_united_states_error').html('');
    }



    var residevalue = document.querySelector('input[name="reside_united_states"]:checked').value;
    if (residevalue == 'yes') {
        var city = document.getElementById('city').value;
        if (city.length == "") {
            // $('#city_error').html('Please Enter Your City');
            $("#city").css("border-color", "red");
            document.getElementById('city').value = "";
            document.getElementById('city').focus();
            return false;
        } else {
            $('#city_error').html('');
            $('#city').css('border-color', '#d8dadc');
        }
        var state = document.getElementById('state').value;
        if (state.length == "") {
            // $('#state_error').html('Please Enter Your State');
            $("#state").css("border-color", "red");
            document.getElementById('state').value = "";
            document.getElementById('state').focus();
            return false;
        } else {
            $('#state_error').html('');
            $('#state').css('border-color', '#d8dadc');
        }
        var zip_code = document.getElementById('zip_code').value;
        if (zip_code.length == "") {
            // $('#zip_code_error').html('Please Enter Your Zip Code');
            $("#zip_code").css("border-color", "red");
            document.getElementById('zip_code').value = "";
            document.getElementById('zip_code').focus();
            return false;
        } else {
            $('#zip_code_error').html('');
            $('#zip_code').css('border-color', '#d8dadc');
        }
        if (!document.getElementById("i_am_authorized_yes").checked && !document.getElementById("i_am_authorized_no").checked) {
            $('#i_am_authorized_yes').css('outline-color', 'red');
            $('#i_am_authorized_yes').css('outline-style', 'solid');
            $('#i_am_authorized_yes').css('outline-width', 'thin');
            $('#i_am_authorized_yes').css('height', '18px');
            $('#i_am_authorized_no').css('outline-color', 'red');
            $('#i_am_authorized_no').css('outline-style', 'solid');
            $('#i_am_authorized_no').css('outline-width', 'thin');
            $('#i_am_authorized_no').css('height', '18px');
            //$('#i_am_authorized_error').html('Please select I am Authorized to work in the United States');
            document.getElementsByName('i_am_authorized')[0].focus();
            return false;
        } else {
            $('#i_am_authorized_yes').css('outline-color', '');
            $('#i_am_authorized_yes').css('outline-style', '');
            $('#i_am_authorized_yes').css('outline-width', '');
            $('#i_am_authorized_yes').css('height', '');
            $('#i_am_authorized_no').css('outline-color', '');
            $('#i_am_authorized_no').css('outline-style', '');
            $('#i_am_authorized_no').css('outline-width', '');
            $('#i_am_authorized_no').css('height', '');
            $('#i_am_authorized_error').html('');
        }




        var authorized = document.querySelector('input[name="i_am_authorized"]:checked').value;
        if (authorized == 'yes') {
            var specify_you_work_authorization = document.getElementById('specify_you_work_authorization').value;
            if (specify_you_work_authorization.length == "") {

                $("#specify_you_work_authorization").css("border-color", "red");
                // $('#specify_you_work_authorization_error').html('Please Enter Your Please specify your work authorization ');
                document.getElementById('specify_you_work_authorization').value = "";
                document.getElementById('specify_you_work_authorization').focus();
                return false;
            } else {
                $('#specify_you_work_authorization_error').html('');
                $('#specify_you_work_authorization').css('border-color', '#d8dadc');
            }
        }


    }



    //  $('.firststepbtn').addClass('next');

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/submitFirstStep';
    var th = _this;
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#firststepid').serialize(),
        dataType: "json",
        beforeSend: function () {
            $('.firststepbtn').prop("disabled", true);
            th.innerHTML = loading;
        },
        success: function (data) {
            // $("#firststepid")[0].reset();
            if (data.success == true) {
                //alertify.success(data.message);
                // $("#signupid").reset();
                if (type == 'save_continue') {
                    $('#step2-tab').removeClass('disableClick');
                    click();
                    th.innerHTML = 'Save &amp; Continue';
                    $('.firststepbtn').prop("disabled", false);
                  
                    $('#step1').removeAttr('style');
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    alertify.success(data.message);
                    th.innerHTML = 'Save';
                    $('.firststepbtn').prop("disabled", false);
                    // window.location.href = APP_URL + "/submit";
                }

            } else {
                if (type == 'save_continue') {
                    th.innerHTML = 'Save &amp; Continue';
                } else {
                    th.innerHTML = 'Save';
                }
                $('.firststepbtn').prop("disabled", false);
                alertify.error(data.message);
            }
        },
    })
}

function click() {
    const nextTabLinkEl = $(".nav-tabs .active")
        .closest("li")
        .next("li")
        .find("a")[0];
    const nextTab = new bootstrap.Tab(nextTabLinkEl);
    nextTab.show();
}
$('body').on('click', '.previous', function () {
    var tab = $(this).attr('data-tab');
    var ctab = $(this).attr('data-ctab');
    $('#' + tab).show();
    $('#' + ctab).hide();
    $('.fade').removeAttr('style');
  
})

$('body').on('click', '.errow', function () {

    $("#about option").show();
    // $('#about').select(); // grab the input (jQuery)

})
$(".errow").on("click", function () {
    $('#about').mobiscroll().select();
});



$("select.ptype").change(function () {

    let selectedItem = $(this).children("option:selected").val();

    if (selectedItem == 'Other') {
        $('.other_specify').css('display', 'block');
    } else {
        $('.other_specify').css('display', 'none');
    }
});

$('.filecomp').click(function () {
    $('#myFileb').trigger('click');
    $('#myFileb').change(function () {
        var filename = $('#myFileb').val();
        if (filename.substring(3, 11) == 'fakepath') {
            filename = filename.substring(12);
        } // Remove c:\fake at beginning from localhost chrome

        $('.comfiles').attr('placeholder', filename);
        $('.comfiles').css('border-color', '')
    });
});

$('.imgprofile').click(function () {
    $('#myFile_image').trigger('click');
    $('body').on('change', '#myFile_image', function () {

        var input = this;
        var url = $(this).val();
        var ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
        if (input.files && input.files[0] && (ext == "gif" || ext == "png" || ext == "jpeg" || ext == "jpg")) {
            var reader = new FileReader();

            reader.onload = function (e) {
                $('.preview-1').attr('src', e.target.result);
            }
            reader.readAsDataURL(input.files[0]);
        }
        else {
            $('.preview-1').attr('src', '/assets/no_preview.png');
        }
    });
});

$('.ad_upresume').click(function () {
    $(this).parent('.login__form').find('input.myFile').trigger('click');
    $('body').on('change', '.myFile', function () {

        var filename = $('.myFile').val();
        if (filename.substring(3, 11) == 'fakepath') {
            filename = filename.substring(12);
        } 


        $(this).parent('.login__form').find('.second').attr('placeholder', filename);
        $(this).parent('.login__form').find('.second').css('border-color', '')
    });
});
$('body').on('click', '.prooffile', function () {

    var id = $(this).attr('data-id');

    $('.myinputfile' + id).trigger('click');
    $('body').on('change', '.myinputfile' + id, function () {
        //     $(this).parents('.login__form').find('.myinputfile').change(function() {
        var filename = $('.myinputfile' + id).val();
        if (filename.substring(3, 11) == 'fakepath') {
            filename = filename.substring(12);
        } // Remove c:\fake at beginning from localhost chrome

        // $('.myinputfile'+id).val(filename);
        $('.appendfile' + id).val(filename);
        $('.appendfile' + id).attr('placeholder', filename);
        $('.appendfile' + id).css('border-color', '')
        $(this).attr('data-values', filename);
    });
});
$('.upresume').click(function () { $('#myFile').trigger('click'); });
$('.one').click(function () { $('.file_teaching_one').trigger('click'); });
$('.two').click(function () { $('.file_teaching_two').trigger('click'); });
$('.three').click(function () { $('.file_teaching_three').trigger('click'); });
$('.introfile').click(function () { $('.intro_file').trigger('click'); });
$('.classroomfiles').click(function () { $('.classroomfile').trigger('click'); });

function submitsecondstep(type, _this) {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    if ($("#certification_yes").is(":checked") == false) {

        $('#certification_yes').css('outline-color', 'red');
        $('#certification_yes').css('outline-style', 'solid');
        $('#certification_yes').css('outline-width', 'thin');
        $('#certification_yes').css('height', '18px');
    }
    if ($("#certification_no").is(":checked") == false) {

        $('#certification_no').css('outline-color', 'red');
        $('#certification_no').css('outline-style', 'solid');
        $('#certification_no').css('outline-width', 'thin');
        $('#certification_no').css('height', '18px');
    }
    // var specify = document.getElementById('specify').value;
    // if (specify == "") {
    //     $("#specify").css("border-color", "red");
    // }
    var teaching_certification_year = document.getElementById('teaching_certification_year').value;
    if (teaching_certification_year == "") {
        $("#teaching_certification_year").css("border-color", "red");
    }
    // if ($("#certified_special_education").is(":checked") == false) {

    //     $('#certified_special_education').css('outline-color', 'red');
    //     $('#certified_special_education').css('outline-style', 'solid');
    //     $('#certified_special_education').css('outline-width', 'thin');
    //     $('#certified_special_education').css('height', '18px');
    // }



    var teaching_certification_year = document.getElementById('teaching_certification_year').value;
    if (teaching_certification_year == "") {
        $("#teaching_certification_year").css("border-color", "red");
    }
    var teaching_certification_states = document.getElementById('teaching_certification_states').value;
    if (teaching_certification_states == "") {
        $("#teaching_certification_states").next().children().children().addClass('brederror');


    }

    var profile_type = document.getElementById('profile_type').value;
    if (profile_type == "") {
        $("#profile_type").css("border-color", "red");
    }
    var teaching_since = document.getElementById('teaching_since').value;
    if (teaching_since == "") {
        $("#teaching_since").css("border-color", "red");
    }
    var experience_teaching_ages = document.getElementById('experience_teaching_ages').value;
    if (experience_teaching_ages == "") {

        $("#experience_teaching_ages").next().children().children().addClass('brederror');
    }

    var certified_special_education = document.getElementById('certified_special_education').value;
    if (certified_special_education == "") {

        $("#certified_special_education").next().children().children().addClass('brederror');
    }
    var highest_level_of_education = document.getElementById('highest_level_of_education1').value;
    if (highest_level_of_education == "") {
        $("#highest_level_of_education1").css("border-color", "red");
    }
    var month_and_year_graduation = document.getElementById('month_and_year_graduation1').value;
    if (month_and_year_graduation == "") {
        $("#month_and_year_graduation1").css("border-color", "red");
    }
    var GPA = document.getElementById('GPA1').value;
    if (GPA == "") {
        $("#GPA1").css("border-color", "red");
    }


    var resume_value = document.getElementById("resume_value").value
    if (resume_value == "") {
        var myFile = document.getElementById("myFile").value;
        if (myFile == "") {
            $(".resumefile").css("border-color", "red");
        }
    }




    if (!document.getElementById("certification_yes").checked && !document.getElementById("certification_no").checked) {
        // $('#certification_error').html('Please select I am a certified teacher');
        $('#certification_yes').css('outline-color', 'red');
        $('#certification_yes').css('outline-style', 'solid');
        $('#certification_yes').css('outline-width', 'thin');
        $('#certification_yes').css('height', '18px');
        $('#certification_no').css('outline-color', 'red');
        $('#certification_no').css('outline-style', 'solid');
        $('#certification_no').css('outline-width', 'thin');
        $('#certification_no').css('height', '18px');

        document.getElementsByName('certification')[0].focus();
        return false;
    } else {
        $('#certification_yes').css('outline-color', '');
        $('#certification_yes').css('outline-style', '');
        $('#certification_yes').css('outline-width', '');
        $('#certification_yes').css('height', '');
        $('#certification_no').css('outline-color', '');
        $('#certification_no').css('outline-style', '');
        $('#certification_no').css('outline-width', '');
        $('#certification_no').css('height', '');

        $('#certification_error').html('');
    }
    var certification = document.querySelector('input[name="certification"]:checked').value;
    if (certification == 'yes') {
        /*var specify = document.getElementById('specify').value;
        if (specify.length == "") {
            // $('#specify_error').html('Please Specify ');
            $("#specify").css("border-color", "red");
            document.getElementById('specify').value = "";
            document.getElementById('specify').focus();
            return false;
        } else {
            $('#specify_error').html('');
            $('#specify').css('border-color', '#d8dadc');
        }*/
        var teaching_certification_year = document.getElementById('teaching_certification_year').value;
        if (teaching_certification_year.length == "") {
            $("#teaching_certification_year").css("border-color", "red");
            // $('#teaching_certification_year_error').html('Please Enter Your Teaching certification ');
            document.getElementById('teaching_certification_year').value = "";
            document.getElementById('teaching_certification_year').focus();
            return false;
        } else {
            $('#teaching_certification_year_error').html('');
            $('#teaching_certification_year').css('border-color', '#d8dadc');
        }
        var teaching_certification_states = document.getElementById('teaching_certification_states').value;
        if (teaching_certification_states.length == "") {

            $("#teaching_certification_states").css("border-color", "red");
            // $('#teaching_certification_states_error').html('Please Enter Teaching Certification');
            document.getElementById('teaching_certification_states').value = "";
            document.getElementById('teaching_certification_states').focus();
            return false;
        } else {
            $('#teaching_certification_states_error').html('');
            $('#teaching_certification_states').css('border-color', '#d8dadc');
        }
        if (certified_special_education.length == "") {
            $("#certified_special_education").next().children().children().addClass('brederror');
            document.getElementById('certified_special_education').focus();
            return false;
        } else {
            $("#certified_special_education").next().children().children().removeClass('brederror');
        }

        var certi = $('#certified_special_education').children('option:selected').toArray().map(item => item.value)
        if (jQuery.inArray("Other", certi) != -1) {
            var certifications_other = document.getElementById('certifications_other').value;
            if (certifications_other.length == "") {
                $("#certifications_other").css("border-color", "red");
                document.getElementById('certifications_other').value = "";
                document.getElementById('certifications_other').focus();
                return false;
            } else {

                $('#certifications_other').css('border-color', '#d8dadc');
            }

        } else {
            $('#certifications_other').css('border-color', '#d8dadc');
            $('.Certificationsother').hide();
        }
    } else {
        var profile_type = document.getElementById('profile_type').value;
        if (profile_type.length == "") {

            // $('#profile_type_error').html('Please Enter Profile Type');
            $("#profile_type").css("border-color", "red");
            document.getElementById('profile_type').value = "";
            document.getElementById('profile_type').focus();
            return false;
        } else {
            $('#profile_type_error').html('');
            $('#profile_type').css('border-color', '#d8dadc');
        }

        if (profile_type == 'Other') {
            var other_specify = document.getElementById('other_specify').value;
            if (other_specify.length == "") {

                // $('#profile_type_error').html('Please Enter Profile Type');
                $("#other_specify").css("border-color", "red");
                document.getElementById('other_specify').value = "";
                document.getElementById('other_specify').focus();
                return false;
            } else {
                $('#other_specify_error').html('');
                $('#other_specify').css('border-color', '#d8dadc');
            }
        }
    }



    var teaching_since = document.getElementById('teaching_since').value;
    if (teaching_since.length == "") {
        $("#teaching_since").css("border-color", "red");
        // $('#teaching_since_error').html('Please Enter Teaching since');
        document.getElementById('teaching_since').value = "";
        document.getElementById('teaching_since').focus();
        return false;
    } else {
        $('#teaching_since_error').html('');
        $('#teaching_since').css('border-color', '#d8dadc');
    }

    var experience_teaching_ages = document.getElementById('experience_teaching_ages').value;
    if (experience_teaching_ages.length == "") {
        $("#experience_teaching_ages").css("border-color", "red");
        //$('#experience_teaching_ages_error').html('Please Select Experience Teaching Ages');
        document.getElementById('experience_teaching_ages').value = "";
        document.getElementById('experience_teaching_ages').focus();
        return false;
    } else {
        $('#experience_teaching_ages_error').html('');
        $('#experience_teaching_ages').css('border-color', '#d8dadc');
    }

    var highest_level_of_education = document.getElementById('highest_level_of_education1').value;
    if (highest_level_of_education.length == "") {
        $("#highest_level_of_education1").css("border-color", "red");
        // $('#highest_level_of_education_error').html('Please Select Highest level of education');
        document.getElementById('highest_level_of_education1').value = "";
        document.getElementById('highest_level_of_education1').focus();
        return false;
    } else {
        $('#highest_level_of_education_error').html('');
        $('#highest_level_of_education1').css('border-color', '#d8dadc');
    }
    var month_and_year_graduation = document.getElementById('month_and_year_graduation1').value;
    if (month_and_year_graduation.length == "") {
        $("#month_and_year_graduation1").css("border-color", "red");
        // $('#month_and_year_graduation_error').html('Please Enter Month And Year Graducation');
        document.getElementById('month_and_year_graduation1').value = "";
        document.getElementById('month_and_year_graduation1').focus();
        return false;
    } else {
        $('#month_and_year_graduation_error').html('');
        $('#month_and_year_graduation').css('border-color', '#d8dadc');
    }
    var GPA = document.getElementById('GPA1').value;
    if (GPA.length == "") {
        $("#GPA1").css("border-color", "red");
        // $('#GPA_error').html('Please Enter GPA');
        document.getElementById('GPA1').value = "";
        document.getElementById('GPA1').focus();
        return false;
    } else {
        $('#GPA_error').html('');
        $('#GPA').css('border-color', '#d8dadc');
    }
    var resume_value = document.getElementById("resume_value").value;
    var myFile = document.getElementById("myFile").value;
    var ext = myFile.split('.').pop().toLowerCase();
    if (resume_value == "") {


        if (myFile == "") {
            // $('#resume_error').text('Please Select Your File');
            $(".resumefile").css("border-color", "red");
            document.getElementById("myFile").focus();
            return false;
        }
        else if (myFile != "") ['xls', 'png', 'jpg', 'jpeg']
        {
            if ($.inArray(ext, ["doc", "docx", "ppt", "pdf"]) == -1) {
                $(".resumefile").css("border-color", "red");
                //$('#resume_error').text('Wrong File Format!..Please Select File');
                document.getElementById("myFile").value = '';
                document.getElementById("myFile").focus();
                return false;
            }
        }
    } else {
        if (myFile != "") {
            if ($.inArray(ext, ["doc", "docx", "ppt", "pdf"]) == -1) {
                $(".resumefile").css("border-color", "red");
                //$('#resume_error').text('Wrong File Format!..Please Select File');
                document.getElementById("myFile").value = '';
                document.getElementById("myFile").focus();
                return false;
            }
        }
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/submitSecondStep';
    var formData = new FormData($("#secondstep")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $('.secondloading').prop("disabled", true);
            _this.innerHTML = loading;
        },
        success: function (data) {

            if (data.success == true) {

                if (type == 'save_continue') {
                    $('#step3-tab').removeClass('disableClick');
                    _this.innerHTML = 'Save &amp; Continue';
                    $('.secondloading').prop("disabled", false);
                    click();
                    $('#step2').removeAttr('style');
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  
                    

                } else {

                    _this.innerHTML = 'Save';
                    $('.secondloading').prop("disabled", false);
                    alertify.success(data.message);
                    // window.location.href = APP_URL + "/submit";
                }

            } else {
                if (type == 'save_continue') {
                    _this.innerHTML = 'Save &amp; Continue';
                } else {
                    _this.innerHTML = 'Save';
                }
                $('.secondloading').prop("disabled", false);
                alertify.error(data.message);
            }
        },
    })
}

$('.myFile').change(function () {

    $('.resumefiles').text(this.files && this.files.length ? this.files[0].name : '');

    if (this.files != "") {
        $('.resumefile').css("border-color", "");
    }
    if (this.files.length == 0) {
        $('.resumefiles').text('Upload Video');
    }

})

$('body').on('change', '.comfile', function () {

    $('.comfiles').attr('placeholder', this.files && this.files.length ? this.files[0].name : '');
    $('.comfiles').css('border-color', '')

})
$('body').on('change', '#myFile', function () {

    $(this).next().attr('placeholder', this.files && this.files.length ? this.files[0].name : '');
    $(this).next().css('border-color', '')

})

$('body').on('change', '.authfiles ', function () {

    $(this).next().attr('placeholder', this.files && this.files.length ? this.files[0].name : '');
    $(this).next().css('border-color', '')
    $(this).attr('data-values', this.files && this.files.length ? this.files[0].name : '')

})

function onlyAlphabets(e, t) {
    try {
        if (window.e) {
            var charCode = window.e.keyCode;
        }
        else if (e) {
            var charCode = e.which;
        }
        else { return true; }
        if ((charCode > 64 && charCode < 91) || (charCode > 96 && charCode < 123))
            return true;
        else
            return false;
    }
    catch (err) {
        alert(err.Description);
    }
}

$('body').on('keypress', '.key', function (e) {
    try {
        if (window.e) {
            var charCode = window.e.keyCode;
        }
        else if (e) {
            var charCode = e.which;
        }
        else { return true; }
        if ((charCode > 64 && charCode < 91) || (charCode > 96 && charCode < 123))
            return true;
        else
            return false;
    }
    catch (err) {
        alert(err.Description);

    }
});


$('body').on('change', '.certificatefile', function () {

    var id = $(this).attr('data-id');

    $('.certificate_' + id + '_text').text(this.files && this.files.length ? this.files[0].name : '');
})
$('body').on('change', '.distinctionsfiles', function () {

    var id = $(this).attr('data-id');
    $('.distinctions_' + id + '_text').text(this.files && this.files.length ? this.files[0].name : '');
})


$('.intro_file').change(function () {

    $('.intro_filetext').text(this.files && this.files.length ? this.files[0].name : '');
    if (this.files != "") {
        $('.intro_files').css("border-color", "");
    }
    if (this.files.length == 0) {
        $('.intro_filetext').text('Upload Video');
    }

})
$('.file_teaching_one').change(function () {
    //$('#title').val(this.value ? this.value.match(/([\w-_]+)(?=\.)/)[0] : '');

    $('.file_teaching_ones_text').text(this.files && this.files.length ? this.files[0].name : '');
    //$('#file_teaching_one_name').text(this.files && this.files.length ? this.files[0].name.split('.')[0] : '');
    if (this.files != "") {
        $('.file_teaching_ones').css("border-color", "");
    }
    if (this.files.length == 0) {
        $('.file_teaching_ones_text').text('Upload Video');
    }
})

$('body').on('change', '.awardfile', function () {
    var id = $(this).attr('data-id');
    $('.award_' + id + '_text').text(this.files && this.files.length ? this.files[0].name : '');
})




$('body').on('change', '.fileinput', function () {
    var name = $(this).attr('name');

    var id = $(this).attr('data-id');
    $('.file_teaching_' + id + '_text').text(this.files && this.files.length ? this.files[0].name : '');
    //$('#title').val(this.value ? this.value.match(/([\w-_]+)(?=\.)/)[0] : '');
    // $('#file_teaching_two_name').text(this.files && this.files.length ? this.files[0].name.split('.')[0] : '');
    if (this.files != "") {
        jQuery('input[name="' + name + '"]').css("border-color", "");
        $('.file_teaching_' + id).css("border-color", "");
    }
    if (this.files.length == 0) {

        $('.file_teaching_' + id + '_text').text('Upload Video');
    }
})
$('.file_teaching_three').change(function () {
    $('.file_teaching_three_text').text(this.files && this.files.length ? this.files[0].name : '');
    //$('#title').val(this.value ? this.value.match(/([\w-_]+)(?=\.)/)[0] : '');
    //$('#file_teaching_three_name').text(this.files && this.files.length ? this.files[0].name.split('.')[0] : '');
    if (this.files != "") {
        $('.file_teaching_three').css("border-color", "");
    }

    if (this.files.length == 0) {
        $('.file_teaching_three_text').text('Upload Video');
    }
})
$('body').on('change', '.classroomfile', function () {
    var id = $(this).attr('data-id');
    $('.classroom' + id).text(this.files && this.files.length ? this.files[0].name : '');

    if (this.files != "") {
        $('.classroomfile').css("border-color", "");
    } else {
        $('.classroom' + id).text('Upload Video')
    }
})
$('body').on('change', '.classroomfile', function () {
    $(this).parents('.removeclassromrow2').find('.file_teaching_ones_text').text(this.files && this.files.length ? this.files[0].name : '');

})


function checkcertificateRadio(value) {
    if (value == '0') {
        $('.secondyes').hide();
        $('.secondno').show();
        $('.certificationyes').hide();
        $('.certificationno').show();
        var other = $('#profile_type').find(":selected").val();
        if (other == 'Other') {
            $('.other_specify').show();
        }

        $('#LessonPlanning').hide();

    } else {
        $('.certificationyes').show();
        $('.certificationno').hide();
        $('.secondno').hide();
        $('.other_specify').hide();
        $('.secondyes').show();
        $('#LessonPlanning').show();
    }
}
function customRange(val, value) {

};


var oneSelectedddw = false;
function CheckValuessss() {

    $('#thirdstepid .selectRound').each(function (k, v) {

      
        if ($(v).children('option:selected').val() != 0 || $(v).children('option:selected').val() != "") {
        
            oneSelectedddw = true;

        }

    });

    return oneSelectedddw;
}


function submitthirdstep(type, _this) {

    var selectedArray = '<option value="" data-value="">Select Subject</option>';
    var arraysubject = [];
    $('.displayblock .selectRound').each(function () {

        var value = $(this).find('option').filter(':selected').val();
        var text = $(this).find('option').filter(':selected').text();
        var datavalue = $(this).find('option').filter(':selected').attr('data-value');
        // if(value!='Other'){
        if (value == 'Other') {
            var othervalue = $(this).attr('data-id');
            var othersubj = $('.subject_other_' + othervalue).val();
            arraysubject.push({ 'value': value, 'data-value': datavalue, 'text': text + ':' + othersubj, 'data-id': '' });
            selectedArray += '<option value="' + value + '" data-value="' + datavalue + '">' + text + ': ' + othersubj + '</option>';
        } else {
            var othervalue = $(this).attr('data-id');
            var subsubjval = $(this).parents('.displayblock').find('.subsubject' + othervalue).find('option').filter(':selected').val();
            var subsubj = $(this).parents('.displayblock').find('.subsubject' + othervalue).find('option').filter(':selected').text();
            selectedArray += '<option value="' + value + '" data-value="' + datavalue + '">' + text + ': ' + subsubj + '</option>';
            arraysubject.push({ 'value': value, 'data-value': datavalue, 'text': text + ': ' + subsubj, 'data-id': subsubjval });
        }

        // }

    });
  
    $('#subject_data_teaching').html(selectedArray)

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var select_button_text = $('#i_prefer_to_teach option:selected')
        .toArray().map(item => item.value);

    if (select_button_text.length == "0") {
        $('#i_prefer_to_teach').next().children().children().css("border-color", "red");
        document.getElementById('i_prefer_to_teach').focus();
    }
    var subone = document.getElementById('subone').value;
    if (subone.length == "") {
        $("#subone").css("border-color", "red");
    } else {
        $("#subone").css("border-color", "");
    }

    if ($(".displayblock")[0]) {
        var onlinerange1 = false;
        $('.displayblock .selectRound').each(function () {

            if ($(this).find('option').filter(':selected').val() == "" || $(this).find('option').filter(':selected').val() == "0") {
                onlinerange1 = false;

                $(this).css("border-color", "red");
            } else {

                if ($(this).find('option').filter(':selected').val() == 'Other') {
                    var o = $(this).attr('data-id');
                
                    $('.subject_other_' + o).css("border-color", "red");

                    onlinerange1 = false;

                    if ($('.subject_other_' + o).val() != "") {
                        onlinerange1 = true;
                        $('.subject_other_' + o).css("border-color", "");
                    }
                }
                else {

                    $('.displayblock .subsubject_slect').each(function () {
                        var subselectoption = $(this).find('option').filter(':selected')
                            .toArray().map(item => item.value);

                        if ($(this).val() == "") {
                            $(this).css("border-color", "red");
                            $(this).next().children().children().css("border-color", "red");
                            onlinerange1 = false;
                        } else {
                            $(this).css("border-color", "");
                            $(this).next().children().children().css("border-color", "");

                            onlinerange1 = true;
                        }
                    })

                    $(this).removeAttr("style");
                    // onlinerange1 = true;
                }
            }

        });
    } else {
        if ($('#subone').find('option').filter(':selected').val() == 'Other') {
            var p = $("#subone").attr('data-id');
            if ($('.subject_other_' + p).val() == "") {
                $('.subject_other_1').css("border-color", "red");
                onlinerange1 = false;
            } else {
                onlinerange1 = true;
            }
        } else {
            var subselectoptiond = $('.subsubject_slect').find('option').filter(':selected').val()


            if (subselectoptiond == '') {
                $('.subsubject_slect').css("border-color", "red");
                //$('.subsubject_slect').next().children().children().css("border-color", "red");
                onlinerange1 = false;
            } else {
                $('.subsubject_slect').css("border-color", "");
                onlinerange1 = true
            }
        }


    }

    var onlinerdange1 = false;
    $('.subsubject_slect').each(function () {
        var subselectoption = $(this).find('option').filter(':selected').val()
        // .toArray().map(item => item.value);

        if (subselectoption == '') {
            $(this).css("border-color", "red");
            //$(this).next().children().children().css("border-color", "red");
            onlinerdange1 = false;
        } else {
            $(this).css("border-color", "");
            $(this).next().children().children().css("border-color", "");

            onlinerdange1 = true;
        }
    })


    if ($("#formatone").is(":checked") == false && $("#formatwo").is(":checked") == false && $("#formathree").is(":checked") == false) {

        $('#formatone').css('outline-color', 'red');
        $('#formatone').css('outline-style', 'solid');
        $('#formatone').css('outline-width', 'thin');
        $('#formatone').css('height', '18px');
        $('#formatwo').css('outline-color', 'red');
        $('#formatwo').css('outline-style', 'solid');
        $('#formatwo').css('outline-width', 'thin');
        $('#formatwo').css('height', '18px');
        $('#formatwo').css('outline-color', 'red');
        $('#formathree').css('outline-style', 'solid');
        $('#formathree').css('outline-width', 'thin');
        $('#formathree').css('height', '18px');
        $('#formathree').css('outline-color', 'red');
    }



    if ($("#scheduleone").is(":checked") == false && $("#scheduletwo").is(":checked") == false) {

        $('#scheduleone,#scheduletwo').css('outline-color', 'red');
        $('#scheduleone,#scheduletwo').css('outline-style', 'solid');
        $('#scheduleone,#scheduletwo').css('outline-width', 'thin');
        $('#scheduleone,#scheduletwo').css('height', '18px');
    }



    var i_prefer_to_teach = $("#i_prefer_to_teach").find("option:selected").val();


    if (select_button_text.length == "0") {
        // $('#i_prefer_to_teach_error').html('Please Enter I Prefer To Teach');
        $(".fluid").css("border-color", "red");
        document.getElementById('i_prefer_to_teach').value = "";

        return false;
    } else {
        $('#i_prefer_to_teach_error').html('');
        $('.fluid').css('border-color', '#d8dadc');
    }


    var format = document.querySelector('input[name="format[]"]:checked');
    var formatElm = document.querySelector('input[type="checkbox"][name="format[]"]');

    if (!format && formatElm) {


        return false;
    } else {
        $('#formatone').css('outline-color', '');
        $('#formatone').css('outline-style', '');
        $('#formatone').css('outline-width', '');
        $('#formatone').css('height', '');

        $('#formatwo').css('outline-color', '');
        $('#formatwo').css('outline-style', '');
        $('#formatwo').css('outline-width', '');
        $('#formatwo').css('height', '18px');

        $('#formathree').css('outline-color', '');
        $('#formathree').css('outline-style', '');
        $('#formathree').css('outline-width', '');
        $('#formathree').css('height', '');
        $('#formathree').css('outline-color', '');
        $('#format_error').html('');
    }

    var format = document.querySelector('input[name="schedule[]"]:checked');
    var formatElm = document.querySelector('input[type="checkbox"][name="schedule[]"]');

    if (!format && formatElm) {
        // $('#schedule_error').html('Please Select Schedule');
        return false;
    } else {

        $('#scheduleone').css('outline-color', '');
        $('#scheduleone').css('outline-style', '');
        $('#scheduleone').css('outline-width', '');
        $('#scheduleone').css('height', '');

        $('#scheduletwo').css('outline-color', '');
        $('#scheduletwo').css('outline-style', '');
        $('#scheduletwo').css('outline-width', '');
        $('#scheduletwo').css('height', '');

        $('#schedule_error').html('');
    }
    var subj = $('#subject_data_teaching').html();
    if ($('#select_subject').val() == 'yes') {
        $('#subject1').html(subj);
    }

    if (onlinerange1 == true) {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        var url = APP_URL + '/submitThirdStep';
        $.ajax({
            type: 'POST',
            url: url,
            data: $('#thirdstepid').serialize(),
            dataType: "json",
            beforeSend: function () {
                $('.thirdloading').prop("disabled", true);
                _this.innerHTML = loading;
            },
            success: function (data) {

                // $("#thirdstepid")[0].reset();
                if (data.success == true) {

                    $('.thirdloading').prop("disabled", false);
                    if (type == 'save_continue') {
                        var arraypush = [];
                        $('input[name="format[]"]').each(function (idx, el) {

                            if ($(el).is(':checked')) {
                                var selectedValue = $(el).val();
                                arraypush.push(selectedValue);

                            }
                        });


                        if (jQuery.inArray("online", arraypush) != -1 && jQuery.inArray("in-person", arraypush) != -1 && jQuery.inArray("hybrid", arraypush) != -1) {

                            $('.onlines').removeClass('displaynones');
                            $('.inperson').removeClass('displaynones');
                            $('#formate_value').val('3');
                        } else if (jQuery.inArray("online", arraypush) != -1 && jQuery.inArray("in-person", arraypush) != -1 && jQuery.inArray("hybrid", arraypush) == -1) {
                            $('.onlines').removeClass('displaynones');
                            $('.inperson').removeClass('displaynones');
                            $('#formate_value').val('3');
                        } else if (jQuery.inArray("online", arraypush) != -1 && jQuery.inArray("in-person", arraypush) == -1 && jQuery.inArray("hybrid", arraypush) == -1) {
                            $('.onlines').removeClass('displaynones');
                            $('.inperson').addClass('displaynones');
                            $('#formate_value').val('1');
                        } else if (jQuery.inArray("online", arraypush) == -1 && jQuery.inArray("in-person", arraypush) != -1 && jQuery.inArray("hybrid", arraypush) == -1) {
                            $('.inperson').removeClass('displaynones');
                            $('.onlines').addClass('displaynones');
                            $('#formate_value').val('2');
                        } else if (jQuery.inArray("online", arraypush) == -1 && jQuery.inArray("in-person", arraypush) != -1 && jQuery.inArray("hybrid", arraypush) != -1) {
                            $('.onlines').removeClass('displaynones');
                            $('.inperson').removeClass('displaynones');
                            $('#formate_value').val('3');
                        } else if (jQuery.inArray("online", arraypush) != -1 && jQuery.inArray("in-person", arraypush) == -1 && jQuery.inArray("hybrid", arraypush) != -1) {
                            $('.onlines').removeClass('displaynones');
                            $('.inperson').removeClass('displaynones');
                            $('#formate_value').val('3');
                        } else if (jQuery.inArray("online", arraypush) == -1 && jQuery.inArray("in-person", arraypush) == -1 && jQuery.inArray("hybrid", arraypush) != -1) {
                            $('.onlines').removeClass('displaynones');
                            $('.inperson').removeClass('displaynones');
                            $('#formate_value').val('3');
                        }


                        $("#teach").val(arraypush)
                        $('#step4-tab').removeClass('disableClick');
                        click();
                      
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                        _this.innerHTML = 'Save &amp; Continue';
                    } else {
                        _this.innerHTML = 'Save';
                        alertify.success(data.message);
                        // window.location.href = APP_URL + "/submit";
                    }

                } else {
                    if (type == 'save_continue') {
                        _this.innerHTML = 'Save &amp; Continue';
                    } else {
                        _this.innerHTML = 'Save';
                    }
                    $('.thirdloading').prop("disabled", false);
                    alertify.error(data.message);
                }
            },
        })
    }
    teachingsubject(arraysubject);
}
function teachingsubject(arraysubject) {

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var $this = $(this);
    var url = APP_URL + '/filter_subject';
    $.ajax({
        type: 'POST',
        url: url,
        data: { 'subj': arraysubject },
        dataType: "html",

        success: function (data) {
            $('#subject_data_teaching').html(data);
            if ($('#select_subject').val() == 'yes') {
                $('.setsubject').html(data);
            }

        },
    })
}

function submitfourthstep(type, _this) {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    // var techtype = document.getElementById('teach').value;
    // if (techtype == "online") {
    //     $('#online_class').css({'outline-color':'red','outline-style':'solid','outline-width':'thin'});
    //     return false
    // }else{
    //     $('#online_class').css({'outline-color':'','outline-style':'','outline-width':''});
    // }

    // if ($("#online_class").is(":checked") == false) {

    //     $('#online_class').css('outline-color', 'red');
    //     $('#online_class').css('outline-style', 'solid');
    //     $('#online_class').css('outline-width', 'thin');
    //     $('#online_class').css('height', '18px');
    // }

    // if ($("#in_person_classes").is(":checked") == false) {

    //     $('#in_person_classes').css('outline-color', 'red');
    //     $('#in_person_classes').css('outline-style', 'solid');
    //     $('#in_person_classes').css('outline-width', 'thin');
    //     $('#in_person_classes').css('height', '18px');
    // }
    var formate_value = document.getElementById('formate_value').value

    if (formate_value == "1" || formate_value == "3") {

        var online_class_price = document.getElementById('online_class_price').value;
        if (online_class_price == "") {
            $("#online_class_price").css("border-color", "red");
        }
    }
    if (formate_value == "2" || formate_value == "3") {
        var in_person_classes_price = document.getElementById('in_person_classes_price').value;
        if (in_person_classes_price == "") {
            $("#in_person_classes_price").css("border-color", "red");
        }
    }




    // var checked = document.forms["fourthid"]["online_class"].checked;
    // var inchecked = document.forms["fourthid"]["in_person_classes"].checked;
    // var i = 0;
    // $('.ads_Checkbox:checked').each(function () {
    //     if ($(this).val() != '') {
    //         i++
    //     }

    // });
    // if (i == 0) {
    //     $('#online_class').css('outline-color', 'red');
    //     $('#online_class').css('outline-style', 'solid');
    //     $('#online_class').css('outline-width', 'thin');
    //     $('#online_class').css('height', '18px');
    //     $('#in_person_classes').css('outline-color', 'red');
    //     $('#in_person_classes').css('outline-style', 'solid');
    //     $('#in_person_classes').css('outline-width', 'thin');
    //     $('#in_person_classes').css('height', '18px');

    //     document.getElementsByName('online_class')[0].focus();
    //     document.getElementsByName('in_person_classes')[0].focus();
    //     return false;
    // } else {
    //     $('#online_class').css('outline-color', '');
    //     $('#online_class').css('outline-style', '');
    //     $('#online_class').css('outline-width', '');
    //     $('#online_class').css('height', '');
    //     $('#in_person_classes').css('outline-color', '');
    //     $('#in_person_classes').css('outline-style', '');
    //     $('#in_person_classes').css('outline-width', '');
    //     $('#in_person_classes').css('height', '');
    //     $('#hourly_error').html('');
    //     $('#hourly_error').css('border-color', '#d8dadc');
    // }
    var formate_value = document.getElementById('formate_value').value

    if (formate_value == "1" || formate_value == "3") {

        // if (checked == true) {
        var online_class_price = document.getElementById('online_class_price').value;
        if (online_class_price.length == "") {
            $("#online_class_price").css("border-color", "red");
            //  $('#online_class_price_error').html('Please enter price');
            document.getElementsByName('online_class_price')[0].focus();
            return false;
        } else {
            $('#online_class_price_error').html('');
        }
    } else {

        $('#online_class_price_error').html(' ');
    }


    if (formate_value == "2" || formate_value == "3") {
        var in_person_classes_price = document.getElementById('in_person_classes_price').value;
        if (in_person_classes_price.length == "") {
            //  $('#in_person_classes_price_error').html('Please enter price');
            $("#in_person_classes_price").css("border-color", "red");
            document.getElementsByName('in_person_classes_price')[0].focus();
            return false;
        } else {
            $('#in_person_classes_price_error').html('');
        }
    } else {

        $('#in_person_classes_price_error').html(' ');
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/submitFourthStep';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#fourthid').serialize(),
        dataType: "json",
        beforeSend: function () {
            $('.fourloading').prop("disabled", true);
            _this.innerHTML = loading;
        },
        success: function (data) {
            // $("#fourthid")[0].reset();
            if (data.success == true) {


                $('.fourloading').prop("disabled", false);
                if (type == 'save_continue') {
                    // $('#step4').hide();
                    // $('#step5').show();
                    _this.innerHTML = 'Save &amp; Continue';
                    $('#step5-tab').removeClass('disableClick');
                    click();
                   
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    _this.innerHTML = 'Save';
                    alertify.success(data.message);
                    //window.location.href = APP_URL + "/submit";
                }
                var subj = $('#subject_data_teaching').html();
                if ($('#select_subject').val() == 'yes') {
                    $('#subject1').html(subj);
                }


            } else {
                if (type == 'save_continue') {
                    _this.innerHTML = 'Save &amp; Continue';
                } else {
                    _this.innerHTML = 'Save';
                }
                $('.fourloading').prop("disabled", false);
                alertify.error(data.message);
            }
        },
    })
}

function submitfivestep(type, _this) {

    var intro_value = document.getElementById("intro_value").value;
    var intro_file = document.getElementsByClassName("intro_file")[0].value;
    if (intro_value == "") {

        var ext = intro_file.split('.').pop().toLowerCase();
        if (intro_file == "") {
            // $('#intro_file_error').text('Please Select Your Video');
            $(".intro_files").css("border-color", "red");
            document.getElementsByClassName("intro_file")[0].focus();
            return false;
        }
        else if (intro_file != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".intro_files").css("border-color", "red");
                // $('#intro_file_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("intro_file")[0].value = '';
                document.getElementsByClassName("intro_file").focus();
                return false;
            }
        }
    } else {
        if (intro_file != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".intro_files").css("border-color", "red");
                // $('#intro_file_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("intro_file")[0].value = '';
                // document.getElementsByClassName("intro_file").focus();
                return false;
            }
        }
    }
    $('#teaching').click();


    var subject_one = document.getElementById('subject_one').value;
    if (subject_one == "") {
        $("#subject_one").css("border-color", "red");
    }
    var subject_two = document.getElementById('subject_two').value;
    if (subject_two == "") {
        $("#subject_two").css("border-color", "red");
    }
    var subject_three = document.getElementById('subject_three').value;
    if (subject_three == "") {
        $("#subject_three").css("border-color", "red");
    }

    var teaching_value_one = document.getElementById("teaching_value_one").value;
    if (teaching_value_one == "") {
        var file_teaching_one = document.getElementsByClassName('file_teaching_one')[0].value;
        if (file_teaching_one == "") {
            $(".vone").css("border-color", "red");
        } else {
            $(".vone").css("border-color", "");
        }
    }
    // var teaching_value_two = document.getElementById("teaching_value_two").value;
    // if (teaching_value_two == "") {
    //     var file_teaching_two = document.getElementsByClassName('file_teaching_two')[0].value;
    //     if (file_teaching_two == "") {
    //         $(".vtwo").css("border-color", "red");
    //     } else {
    //         $(".vtwo").css("border-color", "");
    //     }
    // }
    // var teaching_value_three = document.getElementById("teaching_value_three").value;
    // if (teaching_value_three == "") {
    //     var file_teaching_three = document.getElementsByClassName('file_teaching_three')[0].value;
    //     if (file_teaching_three == "") {
    //         $(".vthree").css("border-color", "red");
    //     } else {
    //         $(".vthree").css("border-color", "");
    //     }
    // }


    var subject_one = document.getElementById('subject_one').value;
    if (subject_one.length == "") {
        $("#subject_one").css("border-color", "red");
        // $('#subject_one_error').html('Please Enter Subject');
        document.getElementById('subject_one').value = "";
        document.getElementById('subject_one').focus();
        return false;
    } else {
        $('#subject_one_error').html('');
        $('#subject_one').css('border-color', '#d8dadc');
    }
    var teaching_value_one = document.getElementById("teaching_value_one").value;
    var file_teaching_one = document.getElementsByClassName("file_teaching_one")[0].value;
    if (teaching_value_one == "") {

        var ext = file_teaching_one.split('.').pop().toLowerCase();
        if (file_teaching_one == "") {
            $(".file_teaching_ones").css("border-color", "red");
            // $('#file_teaching_one_error').text('Please Select Your Video');
            document.getElementsByClassName("file_teaching_one")[0].focus();
            return false;
        }
        else if (file_teaching_one != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
        {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".file_teaching_ones").css("border-color", "red");
                // $('#file_teaching_one_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("file_teaching_one")[0].value = '';
                document.getElementsByClassName("file_teaching_one").focus();
                return false;
            } else {

                $(".vone").css("border-color", "");

            }
        }
    } else {
        if (file_teaching_one != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".file_teaching_ones").css("border-color", "red");
                document.getElementsByClassName("file_teaching_one")[0].value = '';
                document.getElementsByClassName("file_teaching_one").focus();
                return false;
            } else {

                $(".vone").css("border-color", "");

            }
        }
    }


    var subject_two = document.getElementById('subject_two').value;
    if (subject_two.length == "") {
        // $('#subject_two_error').html('Please Enter Subject');
        $("#subject_two").css("border-color", "red");
        document.getElementById('subject_two').value = "";
        document.getElementById('subject_two').focus();
        return false;
    } else {
        $('#subject_two_error').html('');
        $('#subject_two').css('border-color', '#d8dadc');
    }
    var teaching_value_two = document.getElementById("teaching_value_two").value;
    var file_teaching_two = document.getElementsByClassName("file_teaching_two")[0].value;
    if (teaching_value_two == "") {
        var ext = file_teaching_two.split('.').pop().toLowerCase();
        if (file_teaching_two == "") {
            $(".file_teaching_twos").css("border-color", "red");
            // $('#file_teaching_two_error').text('Please Select Your Video');
            document.getElementsByClassName("file_teaching_two")[0].focus();
            return false;
        }
        else if (file_teaching_two != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
        {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".file_teaching_twos").css("border-color", "red");
                $('#file_teaching_two_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("file_teaching_two")[0].value = '';
                document.getElementsByClassName("file_teaching_two").focus();
                return false;
            } else {
                $(".vtwo").css("border-color", "");
            }
        }
    } else {
        if (file_teaching_two != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".file_teaching_twos").css("border-color", "red");
                $('#file_teaching_two_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("file_teaching_two")[0].value = '';
                document.getElementsByClassName("file_teaching_two").focus();
                return false;
            } else {
                $(".vtwo").css("border-color", "");
            }
        }
    }

    var subject_three = document.getElementById('subject_three').value;
    if (subject_three.length == "") {
        // $('#subject_three_error').html('Please Enter Subject');
        $("#subject_three").css("border-color", "red");
        document.getElementById('subject_three').value = "";
        document.getElementById('subject_three').focus();
        return false;
    } else {
        $('#subject_three_error').html('');
        $('#subject_three').css('border-color', '#d8dadc');
    }


    var teaching_value_three = document.getElementById("teaching_value_three").value;
    var file_teaching_three = document.getElementsByClassName("file_teaching_three")[0].value;
    if (teaching_value_three == "") {


        var ext = file_teaching_three.split('.').pop().toLowerCase();
        if (file_teaching_three == "") {
            $(".file_teaching_threes").css("border-color", "red");
            // $('#file_teaching_three_error').text('Please Select Your Video');
            document.getElementsByClassName("file_teaching_three")[0].focus();
            return false;
        }
        else if (file_teaching_three != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
        {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".file_teaching_threes").css("border-color", "red");
                $('#file_teaching_three_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("file_teaching_three")[0].value = '';
                document.getElementsByClassName("file_teaching_three")[0].focus();
                return false;
            } else {
                $(".vthree").css("border-color", "");
            }
        }
    } else {
        if (file_teaching_three != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".file_teaching_threes").css("border-color", "red");
                $('#file_teaching_three_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("file_teaching_three")[0].value = '';
                document.getElementsByClassName("file_teaching_three")[0].focus();
                return false;
            } else {
                $(".vthree").css("border-color", "");
            }
        }
    }
    $('#classroom').click();
    var classroomfile = document.getElementsByClassName('classroomfile')[0].value;
    if (classroomfile == "") {
        $(".cfile").css("border-color", "red");
    }
    var class_value = document.getElementById("class_value").value;
    var classroomfile = document.getElementsByClassName("classroomfile")[0].value;
    if (class_value == "") {
        var ext = classroomfile.split('.').pop().toLowerCase();
        if (classroomfile == "") {
            $(".cfile").css("border-color", "red");
            //$('#classroomfile_error').text('Please Select Your Video');
            document.getElementsByClassName("classroomfile")[0].focus();
            return false;
        }
        else if (classroomfile != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
        {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".cfile").css("border-color", "red");
                $('#classroomfile_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("classroomfile")[0].value = '';
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        }
    } else {
        if (classroomfile != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm']) == -1) {
                $(".cfile").css("border-color", "red");
                $('#classroomfile_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("classroomfile")[0].value = '';
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        }
    }
    $('#quiz').click();

    // var numItems = $('.que').length
    // var total = 0;
    // $('.que').each(function (i, obj) {
    //     if ($('input[name="option' + i + '"]:checked').val() == undefined || $('input[name="option' + i + '"]:checked').val() == null) {
    //         $('#option' + i + '_error').html('Please select question');
    //         document.getElementsByName('option' + i)[0].focus();
    //          return false;
    //         total++
    //     } else {
    //         $('#option' + i + '_error').html('');
    //     }

    // });
    var i;
    var flag = "no";
    $('.que').each(function (ii, obj) {
        var rname = "option" + ii;
        for (i = 0; i <= (document.getElementsByName(rname).length); i++) {

            if (typeof document.getElementsByName(rname)[i] != 'undefined' && document.getElementsByName(rname)[i].checked) {
                // alert(Number(i+1));
                flag = "yes";
            }
        }
    })

    if (flag == "no") {
        window.scrollTo({ top: 0, behavior: 'smooth' });
        return false;
    } else {
       
    }



    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/submitFiveStep';
    var formData = new FormData($("#fivestepid")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $(".loader").removeClass('d-none');
        },
        success: function (data) {

            if (data.success == true) {
                // alertify.success(data.message);
                // $("#signupid").reset();
                window.location.href = APP_URL + "/submit";
            } else {
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $(".loader").addClass('d-none');
    });
}
function getUserTimezone() {
    var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return timezone;
  }

$('body').on('click', '#loginbtn', function () {
    var loading = $(this).attr('data-loading-text');


    var email = document.getElementById('email').value;
    var password = document.getElementById('password').value;

    if (email == "") {
        $("#email").css("border-color", "red");

    }
    if (password == "") {
        $("#password").css("border-color", "red");
    }

    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);

    if (regemailid == false) {
        $("#email").css("border-color", "red");
        $('#email').val() = "";
        // $('.email_error').html('Please enter a valid Email');
        $("#email").focus();
        return false;
    } else {
        $('.email_error').html('');
        $('#email').css('border-color', '#d8dadc');
    }

    var password = $('#password').val();
    if (password == "") {
        $("#password").css("border-color", "red");
        // $('.password_error').html('Please enter your password');
        $('#password').val() = "";
        $('#password').focus();
        return false;
    } else {
        $('.password_error').html('');
        $('#password').css('border-color', '#d8dadc');
    }
    var $this = $(this);
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/loginwithemailschool';

    var formData = $('#loginid').serialize() + '&timezone=' + encodeURIComponent(getUserTimezone());

    $.ajax({
        type: 'POST',
        url: url,
        dataType: "json",
        data: formData,
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            $(".loader").addClass('d-none');
            if (data.success == true) {
                alertify.success(data.message);
                $this.html('Login');
                $this.prop("disabled", false);
                window.location.href = data.redirect;
            } else if (data.success == 'deactivate') {
                alertify.success(data.message);
                $this.html('Login');
                $this.prop("disabled", false);
                window.location.href = data.redirect;
            }
            else {
                $this.html('Login');
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $this.html('Login');
            $this.prop("disabled", false);
        }, 500);
    });
});


$(".month_and_year_graduation").datepicker({
    format: "mm-yyyy",
    viewMode: "months",
    minViewMode: "months"
});

$('body').on('focus', ".month_and_year_graduation", function () {
    $(this).datepicker({
        format: "mm-yyyy",
        viewMode: "months",
        minViewMode: "months"
    })
})

// $("#date").datepicker({
//     format: "yyyy-mm-dd",
//     // viewMode: "months",
//     // minViewMode: "months"
// });
function validate(evt) {
    var theEvent = evt || window.event;

    // Handle paste
    if (theEvent.type === 'paste') {
        key = event.clipboardData.getData('text/plain');
    } else {
        // Handle key press
        var key = theEvent.keyCode || theEvent.which;
        key = String.fromCharCode(key);
    }
    var regex = /[0-9]|\./;
    if (!regex.test(key)) {
        theEvent.returnValue = false;
        if (theEvent.preventDefault) theEvent.preventDefault();
    }
}

$('body').on('click', '#forgotbtn', function () {
    var loading = $(this).attr('data-loading-text');


    var email = document.getElementById('email').value;


    if (email == "") {
        $("#email").css("border-color", "red");

    }

    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);

    if (regemailid == false) {
        $("#email").css("border-color", "red");
        $("#email").focus();

        return false;
    } else {
        $('.email_error').html('');
        $('#email').css('border-color', '#d8dadc');
    }


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var $this = $(this);

    var url = APP_URL + '/forgot-password-frm-school';
    $.ajax({
        type: 'POST',
        url: url,
        dataType: "json",
        data: $('#forgotid').serialize(),
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            $("#forgotid")[0].reset();
            if (data.success == true) {
                $this.html('Send');
                $this.prop("disabled", false);
                // alertify.success(data.message);
                $("#danger").hide()
                $("#success").show().html('<strong>Success!</strong> ' + data.message)

                // .delay(5000).queue(function(n) {
                //     $(this).hide(); n();
                //   });
                //  window.location.href = data.redirect;
            } else {
                $("#success").hide()
                $("#danger").show().html('<strong>Danger!</strong> ' + data.message)

                // .delay(5000).queue(function(n) {
                //     $(this).hide(); n();
                //   });

                // alertify.error('Incorrect Email');
                $this.html('Send');
                $this.prop("disabled", false);

            }
        },
    })
});


$('body').on('click', '#resetbtn', function () {
    var loading = $(this).attr('data-loading-text');
    var password = document.getElementById('password').value;

    if (password == "") {
        $("#password").css("border-color", "red");

    }
    var cpassword = document.getElementById('cpassword').value;

    if (cpassword == "") {
        $("#cpassword").css("border-color", "red");

    }
    if (password != "") {
        const message12 = checkPasswordValidity(new_password);
        if (!message12) {
        
        } else {
            $('.password_error').html('<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>');

            $("#password").css("border-color", "red");
        }
    }
    if (cpassword != "") {
        const message11 = checkPasswordValidity(confirm_password);
        if (!message11) {
         
        } else {
            $('.confirm_password_error').html('<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>');

            $("#cpassword").css("border-color", "red");
        }
    }


    var new_password = $('#password').val();

    if (new_password == "") {

        $("#password").css("border-color", "red");
        $('#password').focus();
        return false;
    } else {
        $("#password").css("border-color", "");
        $('.password_error').html('');
    }

    const message = checkPasswordValidity(new_password);
    if (!message) {
     
    } else {
        $('.password_error').html('<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>');

        $("#password").css("border-color", "red");
        // document.getElementById('password').value = "";
        document.getElementById('password').focus();
        return false;
    }

    var confirm_password = $('#cpassword').val();
    if (confirm_password == "") {
        $("#cpassword").css("border-color", "red");
        $('#cpassword').focus();
        return false;
    } else {
        $("#cpassword").css("border-color", "");
        $('.confirm_password_error').html('');
    }
    const message1 = checkPasswordValidity(confirm_password);
    if (!message1) {
      
    } else {
        $('.confirm_password_error').html('<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>');

        $("#cpassword").css("border-color", "red");
        // document.getElementById('password').value = "";
        document.getElementById('cpassword').focus();
        return false;
    }
    if (new_password != confirm_password) {
        $("#cpassword").css("border-color", "red");
        $('.confirm_password_error').html('The password and confirmation password do not match.');
        $('#cpassword').focus();
        return false;
    } else {
        $("#cpassword").css("border-color", "");
        $('.confirm_password_error').html('');
    }
    var $this = $(this);
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/reset-password-frm-school';
    $.ajax({
        type: 'POST',
        url: url,
        dataType: "json",
        data: $('#resetid').serialize(),
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {

            if (data.success == true) {
                $("#resetid")[0].reset();
                $this.html('Submit');
                $this.prop("disabled", false);
                alertify.success(data.message);
                window.location.href = data.redirect;
            } else {
                $this.html('Submit');
                $this.prop("disabled", false);
                alertify.error('Incorrect Email');
            }
        },
    })
});


$("input").keypress(function () {

    if ($(this).val() != "") {
        $(this).css("border-color", "");

    }
    if ($(this).attr('name') == 'online_class_price' || $(this).attr('name') == 'in_person_classes_price') {
        $('#in_person_classes_price').css("border-color", "");
        $('#online_class_price').css("border-color", "");
    }
    if ($(this).attr('name') == 'password') {
        $('.password_error').html("");

    }
    if ($(this).attr('name') == 'cpassword') {

        $('.confirm_password_error').html("");
    }
    if ($(this).attr('name') == 'email') {
        $('#email_error').html("");
    }
    if ($(this).attr('name') == 'password') {
        $('#password_error').html("");
    }
    if ($(this).attr('name') == 'first_name') {
        $('#first_name_error').html("");
    }
    if ($(this).attr('name') == 'last_name') {
        $('#last_name_error').html("");
    }



})
$("#GPA").keypress(function () {
    if ($(this).val() != "") {
        $(this).css("border-color", "");
    }
})
$("input").change(function () {

    if ($(this).val() != "") {
        $(this).css("border-color", "");
    }

    if ($(this).attr('name') == 'online_class_price' || $(this).attr('name') == 'in_person_classes_price') {
        $('#in_person_classes_price').css("border-color", "");
        $('#online_class_price').css("border-color", "");
    }
})
$("select").change(function () {
    if ($(this).val() != "") {
        $(this).css("border-color", "");
    }
    if ($(this).attr('name') == 'about') {
        $('#about_error').html("");
    }
    if ($(this).attr('id') == 'teaching_certification_states' || $(this).attr('id') == 'experience_teaching_ages' || $(this).attr('id') == 'certified_special_education') {
        // $(this).parent('.multiple ').css("border-color", "");
        $(this).next().children().children().removeClass('brederror');
    }


})
$('input[type=radio]').click(function () {
    var name = $(this).attr('name');
    $('.' + name).css('border-color', '');
})

$('body').on('change', '.subsubject_slect', function () {

    $(this).next().children().children().css("border-color", "#ced4da");
})
$(function () {
    $('input[type=checkbox]').change(function () {
        if ($(this).attr('name') == 'format[]') {

            $('#formatone').css({ 'outline-color': '', 'outline-style': '', 'outline-width': '', 'height': '' });
            $('#formatwo').css({ 'outline-color': '', 'outline-style': '', 'outline-width': '', 'height': '' });
            $('#formathree').css({ 'outline-color': '', 'outline-style': '', 'outline-width': '', 'height': '' });
        }

        if ($(this).is(':checked')) {

            $(this).css('outline-color', '');
            $(this).css('outline-style', '');
            $(this).css('outline-width', '');
            $(this).css('height', '');

        }
    });
    $('input[type=checkbox]').change(function () {
        if ($(this).attr('name') == 'schedule[]' || $(this).attr('name') == 'online_class' || $(this).attr('name') == 'in_person_classes') {
            $('#scheduleone').css({ 'outline-color': '', 'outline-style': '', 'outline-width': '', 'height': '' });
            $('#scheduletwo').css({ 'outline-color': '', 'outline-style': '', 'outline-width': '', 'height': '' });
            $('#online_class').css({ 'outline-color': '', 'outline-style': '', 'outline-width': '', 'height': '' });
            $('#in_person_classes').css({ 'outline-color': '', 'outline-style': '', 'outline-width': '', 'height': '' });
        }

        if ($(this).is(':checked')) {
            $(this).css('outline-color', '');
            $(this).css('outline-style', '');
            $(this).css('outline-width', '');
            $(this).css('height', '');

        }
    });
});



function savefiveintro(type, _this) {

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading..";


    var intro_value = document.getElementById("intro_value").value;
    var intro_file = document.getElementsByClassName("intro_file")[0].value;
    var ext = intro_file.split('.').pop().toLowerCase();
    if (intro_value == "") {


        if (intro_file == "") {
            // $('#intro_file_error').text('Please Select Your Video');
            $(".intro_files").css("border-color", "red");
            document.getElementsByClassName("intro_file")[0].focus();
            return false;
        }
        else if (intro_file != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                $(".intro_files").css("border-color", "red");
                // $('#intro_file_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("intro_file")[0].value = '';
                document.getElementsByClassName("intro_file").focus();
                return false;
            }
        }
    } else {
        if (intro_file != "") {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                $(".intro_files").css("border-color", "red");
                // $('#intro_file_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("intro_file")[0].value = '';
                // document.getElementsByClassName("intro_file").focus();
                return false;
            }
        }
    }
    $('.teach').removeClass('disableClick');

    var url = APP_URL + '/submit_intro';
    var formData = new FormData($("#fivestepid")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {

            $('.savefiveintro').prop("disabled", true);
            _this.innerHTML = loading;

        },
        success: function (data) {

            if (data.success == true) {
                if (type == "save_continue") {
                    _this.innerHTML = 'Save &amp; Continue';

                    $('#teaching').click();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    _this.innerHTML = 'Save';
                    alertify.success(data.message);
                }
                $('.savefiveintro').prop("disabled", false);

                $('.topic').css('padding-top', '16px !important');

            } else {
                if (type == "save_continue") {
                    _this.innerHTML = 'Save &amp; Continue';
                } else {
                    _this.innerHTML = 'Save';
                }
                $('.savefiveintro').prop("disabled", false);
                alertify.error('Incorrect Email');
            }
        },
    })

}
var oneSelected = false;

function CheckValue() {

    $('#fivesecondid').find('select').each(function (k, v) {
        if (this.hasAttribute("name")) {


            if ($(v).children('option:selected').val() == "" || $(v).children('option:selected').length == "") {
                oneSelected = false;
                $(this).css("border-color", "red");
            } else {

                if ($(v).children('option:selected').val() == 'Other') {

                    if ($(v).parents('.classfile').find('.topic').val() == "") {
                        oneSelected = false;
                        $(v).parents('.classfile').find('.topic').css("border-color", "red");
                    } else {
                        oneSelected = true;
                        $(v).parents('.classfile').find('.topic').css("border-color", "");
                    }


                } else {
                    $(this).css("border-color", "");
                    oneSelected = true;
                }
            }

        }
    });
    return oneSelected;
}

var oneSelectedtopic = false;
function CheckValuetopic() {
    $('#fivesecondid').find('.topic').each(function (k, v) {
    
        if ($(v).val() == "" || $(v).length == "") {
            oneSelectedtopic = false;
            $(this).css("border-color", "red");
        } else {
            oneSelectedtopic = true;
            $(this).css("border-color", " ");
        }

    });


    return oneSelectedtopic;
}
$('body').on('change', 'select[name="i_prefer_to_teach[]"]', function () {
    var grade = $('#i_prefer_to_teach option:selected')
        .toArray().map(item => item.value);
    if (grade.length > 0) {
        $('#i_prefer_to_teach').next().children().children().css("border-color", "");
    }
})

$('.txtOnly').bind('keydown', function (event) {
    var key = event.which;
    if (key >= 48 && key <= 57) {
        event.preventDefault();
    }
});



$('body').on('change', 'select[name="ass_subject[]"]', function () {
    $(this).css("border-color", "");
    var id = $(this).find('option').filter(':selected').val();

    var dataid = $(this).find('option').filter(':selected').attr('data-id');
    //  $(this).parents('.classfile').find('.subsubjectteaching').show();
    //     $.ajaxSetup({
    //         headers: {
    //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    //         }
    //     });
    //     var url = APP_URL + '/get_subsubject_teaching';
    //    var $this=$(this);
    //     $.ajax({
    //         url: url,
    //         type: 'post',
    //         data: {'id':dataid},
    //         dataType: "html",
    //         beforeSend: function () {
    //             $("#overlay").fadeIn(300)
    //         },
    //         success: function (response) {

    //             $this.parents('.classfile').find('.subsubjectteaching').html(response);
    //            $('.sub_subject_teaching').select2({
    //              theme: 'bootstrap4',
    //              width: 'style',
    //              placeholder: 'Select Sub Subject',
    //              allowClear: Boolean($(this).data('allow-clear')),
    //            });
    //         },
    //     })



});

$('select[name="subject[]"]').on('change', function () {
    $('#select_subject').val('yes')
    var id = $(this).attr('id');
    var step = $(this).attr('data-id');
    $(this).parents('.classfile').find('.subsubjectteaching').hide();

    if ($(this).find('option').filter(':selected').val() == 'Other') {

        $('.subsubject' + step).hide();
        if (id == 'subone') { $('.subject_other_one').removeAttr('style').removeClass('displaynones') }
        if (id == 'subtwo') { $('.subject_other_two').removeAttr('style').removeClass('displaynones') }
        if (id == 'subthree') { $('.subject_other_three').removeAttr('style').removeClass('displaynones') }
        if (id == 'subfour') { $('.subject_other_four').removeAttr('style').removeClass('displaynones') }
        if (id == 'subfive') { $('.subject_other_five').removeAttr('style').removeClass('displaynones') }
        if (id == 'subsix') { $('.subject_other_six').removeAttr('style').removeClass('displaynones') }
        if (id == 'subseven') { $('.subject_other_seven').removeAttr('style').removeClass('displaynones') }
    } else {
        if (id == 'subone') { $('.subject_other_one').addClass('displaynones') }
        if (id == 'subtwo') { $('.subject_other_two').addClass('displaynones') }
        if (id == 'subthree') { $('.subject_other_three').addClass('displaynones') }
        if (id == 'subfour') { $('.subject_other_four').addClass('displaynones') }
        if (id == 'subfive') { $('.subject_other_five').addClass('displaynones') }
        if (id == 'subsix') { $('.subject_other_six').addClass('displaynones') }
        if (id == 'subseven') { $('.subject_other_seven').addClass('displaynones') }
        var ids = $(this).find('option').filter(':selected').attr('data-value');
        $('.subsubject' + step).show();

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        var url = APP_URL + '/get_subsubject';
        var $this = $(this);
        $.ajax({
            url: url,
            type: 'post',
            data: { 'id': ids, 'step': step },
            dataType: "html",
            beforeSend: function () {
                $("#overlay").fadeIn(300)
            },
            success: function (response) {

                $('.subsubject' + step).html(response);
                //    $('.subsubject_slect').select2({
                //      theme: 'bootstrap4',
                //      width: 'style',
                //      placeholder: 'Select Sub Subject',
                //      allowClear: Boolean($(this).data('allow-clear')),
                //    });
            },
        })

    }


})

// $('.subsubject_slect').select2({
//     theme: 'bootstrap4',
//     width: 'style',
//     placeholder: 'Select Sub Subject',
//     allowClear: Boolean($(this).data('allow-clear')),
//   });

// $(document).ready(function() {
//     $('.myTextarea').on('input', function() {
//       var words = $(this).val().match(/\S+/g);
//       var wordCount = words ? words.length : 0;

//       $('#wordCount').text('Words: ' + wordCount);

//       // Perform validation
//       if (wordCount > 100) {
//         $(this).css('border-color','red');
//         //alert('Maximum 100 words allowed.');
//         // You can also trim the text if you want
//         // $(this).val($(this).val().split(/\s+/).slice(0, 100).join(' '));
//       }
//     });
//   });

$('body').on('change', 'select[name="ass_subject[]"]', function () {
    var id = $(this).attr('data-id');

    if ($(this).find('option').filter(':selected').val() == 'Other') {
        //  $(this).parents('.classfile').find('.subsubjectteaching').hide();
        // $('.teaching_other_specify_'+id).removeAttr('style').removeClass('displaynones')

    } else {
        //$('.teaching_other_specify_'+id).addClass('displaynones')

    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var dataid = $(this).find('option').filter(':selected').attr('data-sub-id');

    var url = APP_URL + '/get_subject_details';
    var $this = $(this);

    if ($(this).find('option').filter(':selected').val() == 'Other') {
        $this.parents('.classfile').find('.topic').removeAttr('style');
        $this.parents('.classfile').find('.topic').removeClass('displaynones');
        $this.parents('.classfile').find('.note-text').addClass('displaynones');
        $this.parents('.classfile').find('.topic').val('');
        $this.parents('.classfile').find('.topic').removeAttr('readonly');
        $this.parents('.classfile').find('.topic').prop("readonly", false);

        $this.parents('.classfile').find('.topic').removeClass('whiteborder').css({
            'overflow': 'hidden',
            'padding': '8px 13px',
            'outline': 0
        });

        $this.parents('.classfile').find('.topic').removeClass('condition');

    } else {

        $this.parents('.classfile').find('.note-text').removeClass('displaynones');
        //    $this.parents('.classfile').find('.topic').removeAttr('style');
        $this.parents('.classfile').find('.topic').addClass('displaynones');
        $this.parents('.classfile').find('.topic').css('padding-top', '0px;');
        $this.parents('.classfile').find('.topic').addClass('whiteborder').addClass('condition').css("border-color", "");
        $this.parents('.classfile').find('.topic').prop('readonly', true);
        $.ajax({
            url: url,
            type: 'post',
            data: { 'id': dataid },
            dataType: "html",
            beforeSend: function () {
                $("#overlay").fadeIn(300)
            },
            success: function (response) {
                $this.parents('.classfile').find('.note-text').text(response);

                $this.parents('.classfile').find('.topic').val(response);
                $this.parents('.classfile').find('.topic').prop('readonly', true);

            },
        })
    }



})


var oneSelectedfile = false;

function CheckValuefile() {
    var p = 0;
    $('#fivesecondid').find('.fileinput').each(function (k, v) {
        p++;
        var nam = 't' + p;

        var hite = 'teaching_value_' + p;
        var teaching_value_one = document.getElementById(hite).value;
        var file_teaching_one = document.getElementsByClassName(nam)[0].value;
        var ext = file_teaching_one.split('.').pop().toLowerCase();

      
        var id = p;
        if (teaching_value_one == "") {


            if (file_teaching_one == "") {
                $(".teac" + p).css("border-color", "red");
                oneSelectedfile = false;
            }
            else if (file_teaching_one != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
            {
                if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                    $(".teac" + p).css("border-color", "red");

                    oneSelectedfile = false;
                } else {
                    oneSelectedfile = true;
                    $(".teac" + p).css("border-color", "");

                }
            }
        } else {

            if (file_teaching_one != "") {
                if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                    $(".teac" + p).css("border-color", "red");

                    oneSelectedfile = false;
                } else {

                    oneSelectedfile = true;
                    $(".teac" + p).css("border-color", "");
                }
            } else {
                $(".teac" + p).css("border-color", "");
                oneSelectedfile = true;
            }
        }

    });
    return oneSelectedfile;
}

function savefiveteaching(type, _this) {

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var i;
    var flag = "no";
    var $this = $(this);
    var check = CheckValue();
   
    var fil = CheckValuefile();
    
    var oneSelecteds = false;
    $('#fivesecondid .setsubject').each(function (k, v) {

        if ($(v).children('option:selected').val() != 0 || $(v).children('option:selected').val() != "") {


            var id = $(this).attr('data-id');
            var nam = 't' + id;

            var hite = 'teaching_value_' + id;
            var teaching_value_one = document.getElementById(hite).value;
            var file_teaching_one = document.getElementsByClassName(nam)[0].value;
            var ext = file_teaching_one.split('.').pop().toLowerCase();

            if (teaching_value_one == "") {


                if (file_teaching_one == "") {
                    $(".teac" + id).css("border-color", "red");
                    oneSelecteds = false;
                }
                else if (file_teaching_one != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
                {
                    if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                        $(".teac" + id).css("border-color", "red");

                        oneSelecteds = false;
                    } else {
                        oneSelecteds = true;
                        $(".teac" + id).css("border-color", "");

                    }
                }
            } else {

                if (file_teaching_one != "") {
                    if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                        $(".teac" + id).css("border-color", "red");

                        oneSelecteds = false;
                    } else {

                        oneSelecteds = true;
                        $(".teac" + id).css("border-color", "");
                    }
                } else {
                    $(".teac" + id).css("border-color", "");
                    oneSelecteds = true;
                }
            }


        }

    });
   
    $('.class').removeClass('disableClick');
    if (oneSelecteds == true && check == true) {

        var url = APP_URL + '/submit_teaching';
        var formData = new FormData($("#fivesecondid")[0]);
        $.ajax({
            type: 'POST',
            url: url,
            data: formData,
            dataType: "json",
            processData: false,
            contentType: false,
            beforeSend: function () {
                $('.savefiveteaching').prop("disabled", true);
                _this.innerHTML = loading;


            },
            success: function (data) {

                if (data.success == true) {
                    if (type == "save_continue") {
                        _this.innerHTML = 'Save &amp; Continue';

                        $('#classroom').click();
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    } else {
                        _this.innerHTML = 'Save';
                        alertify.success(data.message);
                    }
                    $('.savefiveteaching').prop("disabled", false);
                    // alertify.success(data.message);


                } else {
                    if (type == "save_continue") {
                        _this.innerHTML = 'Save &amp; Continue';
                    } else {
                        _this.innerHTML = 'Save';
                    }
                    $('.savefiveteaching').prop("disabled", false);
                    // alertify.error('Incorrect Email');
                }
            },
        })
    }
}

function savefiveclassroom(type, _this) {

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var classroomfile = document.getElementsByClassName('classroomfile')[0].value;
    if (classroomfile == "") {
        $(".cfile").css("border-color", "red");
    }
    var class_value = document.getElementById("class_value").value;
    var classroomfile = document.getElementsByClassName("classroomfile")[0].value;
    var ext = classroomfile.split('.').pop().toLowerCase();
    if (class_value == "") {

        if (classroomfile == "") {
            $(".cfile").css("border-color", "red");
            //$('#classroomfile_error').text('Please Select Your Video');
            document.getElementsByClassName("classroomfile")[0].focus();
            return false;
        }
        else if (classroomfile != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
        {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                $(".cfile").css("border-color", "red");
                $('#classroomfile_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("classroomfile")[0].value = '';
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        }
    } else {
        if (classroomfile != "") {

            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                $(".cfile").css("border-color", "red");
                $('#classroomfile_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("classroomfile")[0].value = '';
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        } else {
            $(".cfile").css("border-color", "");
        }
    }
    var $this = $(this);
    $('.quiz').removeClass('disableClick');
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var url = APP_URL + '/submit_classrorm';
    var formData = new FormData($("#fivethirddid")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $('.savefiveclassroom').prop("disabled", true);
            _this.innerHTML = loading;

        },
        success: function (data) {

            if (data.success == true) {
                if (type == "save_continue") {
                    _this.innerHTML = 'Save &amp; Continue';

                    $('#quiz').click();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    _this.innerHTML = 'Save';
                }
                $('.savefiveclassroom').prop("disabled", false);


            } else {
                if (type == "save_continue") {
                    _this.innerHTML = 'Save &amp; Continue';
                } else {
                    _this.innerHTML = 'Save';
                }
                $('.savefiveclassroom').prop("disabled", false);
                alertify.error('Incorrect Email');
            }
        },
    })
}
function savefiveclassrooms(_this) {

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var classroomfile = document.getElementsByClassName('classroomfile')[0].value;
    if (classroomfile == "") {
        $(".cfile").css("border-color", "red");
    }
    var class_value = document.getElementById("class_value").value;
    var classroomfile = document.getElementsByClassName("classroomfile")[0].value;
    var ext = classroomfile.split('.').pop().toLowerCase();
    if (class_value == "") {

        if (classroomfile == "") {
            $(".cfile").css("border-color", "red");
            //$('#classroomfile_error').text('Please Select Your Video');
            document.getElementsByClassName("classroomfile")[0].focus();
            return false;
        }
        else if (classroomfile != "") ['pdf', 'doc', 'docx', 'xls', 'png', 'jpg', 'jpeg']
        {
            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                $(".cfile").css("border-color", "red");
                $('#classroomfile_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("classroomfile")[0].value = '';
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        }
    } else {
        if (classroomfile != "") {

            if ($.inArray(ext, ['m4v', 'avi', 'mpg', 'mp4', 'webm', 'mov']) == -1) {
                $(".cfile").css("border-color", "red");
                $('#classroomfile_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("classroomfile")[0].value = '';
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        } else {
            $(".cfile").css("border-color", "");
        }
    }
    var $this = $(this);

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var url = APP_URL + '/submit_classrorm';
    var formData = new FormData($("#fivethirddid")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $('.savefiveclassroom').prop("disabled", true);
            _this.innerHTML = loading;

        },
        success: function (data) {

            if (data.success == true) {

                _this.innerHTML = 'Submitted';
                $('.savefiveclassroom').prop("disabled", false);


            } else {
                _this.innerHTML = 'Submit';
                $('.savefiveclassroom').prop("disabled", false);
                alertify.error('Incorrect Email');
            }
        },
    })

}

function validateRadio(radio) {
    var retval = false
    for (var i = 0; i < radio.length; i++) {
        if (radio[i].checked) retval = true;

    }
    return retval;
}

// to make sure all questions are answered
//  function validateForm(theForm) // to make sure all questions are answered
//  {
//     var retval = true;
//     var unanswered = "";
//     if ( !validateRadio(theForm.option1) ){retval = false;unanswered += "Question1\n";}
//     if ( !validateRadio(theForm.option2) ){retval = false;unanswered += "Question2\n";}
//     // if ( !validateRadio(theForm.option3) ){retval = false;unanswered += "Question3\n";}
//     if ( !retval ) alert("Answer all of the questions\nThe following were unanswered:\n" + unanswered);
//     return retval;
//  }

function savequiz(type, _this, _theForm) {

    var isValid = true;
    $('.que').each(function (index, q) {
        // $.each(questions, function(index, q) {
        // Question validation
        if (!$("input[name='option" + index + "']:checked").val()) {
            $('.option' + index).css('border-color', 'red')
            //  $("#q" + index + "Error").text("Please select an answer");
            isValid = false;
        } else {
            $('.option' + index).css('border-color', '')
        }
    });

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var $this = $(this);

    if (isValid == false) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
        return false;
    } else {

    }

    if (isValid == true) {
        var url = APP_URL + '/save_quiz';
        $.ajax({
            type: 'POST',
            url: url,
            dataType: "json",
            data: $('#fivefourid').serialize(),
            beforeSend: function () {
                $this.html(loading);
                $this.prop("disabled", true);

            },
            success: function (data) {

                if (data.success == true) {
                    if (type == "save_continue") {
                        $this.html('Submit');
                        $this.prop("disabled", false);
                        window.location.href = data.redirect;
                    } else {
                        _this.innerHTML = 'Save';
                        alertify.success(data.message);
                    }


                } else {
                    $this.html('Submit');
                    $this.prop("disabled", false);
                    alertify.error('Incorrect Email');
                }
            },
        })
    }

}

function sumbitquiz(type, _this) {
    var isValid = true;
    $('.que').each(function (index, q) {
        // $.each(questions, function(index, q) {
        // Question validation
        if (!$("input[name='option" + index + "']:checked").val()) {
            $('.option' + index).css('border-color', 'red')
            //  $("#q" + index + "Error").text("Please select an answer");
            isValid = false;
        } else {
            $('.option' + index).css('border-color', '')
        }
    });





    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var $this = $(this);
    if (isValid == false) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
        return false;
    } else {
    }

    if (isValid == true) {
        var url = APP_URL + '/submit_quiz';
        $.ajax({
            type: 'POST',
            url: url,
            dataType: "json",
            data: $('#fivefourid').serialize(),
            beforeSend: function () {
                $this.html(loading);
                $this.prop("disabled", true);

            },
            success: function (data) {

                if (data.success == true) {
                    if (type == "save_continue") {
                        $this.html('Submit');
                        $this.prop("disabled", false);
                        window.location.href = data.redirect;
                    } else {
                        _this.innerHTML = 'Save';
                        alertify.success(data.message);
                    }


                } else {
                    $this.html('Submit');
                    $this.prop("disabled", false);
                    alertify.error('Incorrect Email');
                }
            },
        })
    }

}
// $('body').on('change','#subone',function(){

//     var se=$(this).find('option').filter(':selected').val();

//     var text=$(this).find('option').filter(':selected').text();

//     if(se=="Other" || text=="Select Subjects"){

//     }else{ 

//     $("#subtwo >option[value='"+se+"']").remove();
//     $("#subthree >option[value='"+se+"']").remove();
//     $("#subfour >option[value='"+se+"']").remove();
//     }

//   });
//   $('body').on('change','#subtwo',function(){

//     var se=$(this).find('option').filter(':selected').val();
//     var text=$(this).find('option').filter(':selected').text();

//     if(se=="Other" || text=="Select Subjects"){}else{ 
//     $("#subthree >option[value='"+se+"']").remove();
//     $("#subone >option[value='"+se+"']").remove();
//     $("#subfour >option[value='"+se+"']").remove();
//     }
//   });
//   $('body').on('change','#subthree',function(){

//     var se=$(this).find('option').filter(':selected').val();
//     var text=$(this).find('option').filter(':selected').text();
//     if(se=="Other" || text=="Select Subjects"){}else{ 
//     $("#subone >option[value='"+se+"']").remove();
//     $("#subtwo >option[value='"+se+"']").remove();
//     $("#subfour >option[value='"+se+"']").remove();
//     }
//   });
//   $('body').on('change','#subfour',function(){

//     var se=$(this).find('option').filter(':selected').val();
//     var text=$(this).find('option').filter(':selected').text();
//     if(se=="Other" || text=="Select Subjects"){}else{ 
//     $("#subone >option[value='"+se+"']").remove();
//     $("#subtwo >option[value='"+se+"']").remove();
//     $("#subthree >option[value='"+se+"']").remove();
//     }
//   });

//   $('#i_prefer_to_teach').on('change', function () {

// $.ajaxSetup({
//     headers: {
//         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//     }
// });
// var se=$(this).find('option').filter(':selected').val();

// var select_button_text = $('#i_prefer_to_teach option:selected')
// .toArray().map(item => item.value);

// var url = APP_URL + '/subject_get';
// $.ajax({
//     type: 'POST',
//     url: url,
//     dataType: "html",
//     data: {'id':select_button_text},
//     success: function (data) {


//     //    $('.selectRound').html(data)
//     //    $('.setsubject').html(data)
//     },
// }).done(function () {
//     setTimeout(function () {

//     }, 500);
// });
//   });


$('#subject1').on('change', function () {
    var se = $(this).find('option').filter(':selected').val();

    var text = $(this).find('option').filter(':selected').text();

    if (text == "Other" || text == "Select Subjects") { } else {
        // $("#subject2 >option[value='"+se+"']").remove();
        // $("#subject3 >option[value='"+se+"']").remove();
    }

});
$('#subject2').on('change', function () {
    var se = $(this).find('option').filter(':selected').val();

    var text = $(this).find('option').filter(':selected').text();

    if (text == "Other" || text == "Select Subjects") { } else {
        // $("#subject3 >option[value='"+se+"']").remove();
        // $("#subject1 >option[value='"+se+"']").remove();
    }




});
$('#subject3').on('change', function () {
    var se = $(this).find('option').filter(':selected').val();

    var text = $(this).find('option').filter(':selected').text();

    if (text == "Other" || text == "Select Subjects") { } else {
        // $("#subject1 >option[value='"+se+"']").remove();
        // $("#subject2 >option[value='"+se+"']").remove();
    }


});
var pathArray = window.location.pathname.split('/');
if (pathArray[1] == 'teaching-preferences' || pathArray[1] == 'onboarding-step' || pathArray[1] == 'onboarding-re-step') {
    const sliderInput = document.querySelector(".input-slider");
    const numberLabel = document.querySelector(".number--label");
    const slider = document.getElementById("input-sliderr")
    sliderInput.addEventListener("input", () => {
        const { value, min, max, offsetWidth } = sliderInput;
        const percent = ((value - min) / (max - min)) * 100;
        const newPosition = percent * (offsetWidth / 100);
        //   numberLabel.style.transform = `translateX(${newPosition}px)`;
        // numberLabel.textContent = value;
        $('.number--label').text(value)

        slider.style.background = 'linear-gradient(to right, rgb(60 137 251) 0%, rgb(60 137 251) ' + percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';

    });

    const sliderInput2 = document.querySelector(".input-slider2");
    const numberLabel2 = document.querySelector(".number--label2");
    const slider2 = document.getElementById("input-slider2")
    sliderInput2.addEventListener("input", () => {

        const { value, min, max, offsetWidth } = sliderInput2;
        const percent = ((value - min) / (max - min)) * 100;
        const newPosition = percent * (offsetWidth / 100);
        //   numberLabel2.style.transform = `translateX(${newPosition}px)`;
        numberLabel2.textContent = value;
        slider2.style.background = 'linear-gradient(to right, #FC697D 0%, #FC697D ' + percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
    });


    const sliderInput3 = document.querySelector(".input-slider3");
    const numberLabel3 = document.querySelector(".number--label3");
    const slider3 = document.getElementById("input-slider3")
    sliderInput3.addEventListener("input", () => {
        const { value, min, max, offsetWidth } = sliderInput3;
        const percent = ((value - min) / (max - min)) * 100;
        const newPosition = percent * (offsetWidth / 100);
        //   numberLabel3.style.transform = `translateX(${newPosition}px)`;
        numberLabel3.textContent = value;
        slider3.style.background = 'linear-gradient(to right, #7EC7A9 0%, #7EC7A9 ' + percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
    });
    const sliderInput4 = document.querySelector(".input-slider4");
    const numberLabel4 = document.querySelector(".number--label4");
    const slider4 = document.getElementById("input-slider4")
    sliderInput4.addEventListener("input", () => {
        const { value, min, max, offsetWidth } = sliderInput4;
        const percent = ((value - min) / (max - min)) * 100;
        const newPosition = percent * (offsetWidth / 100);
        //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
        numberLabel4.textContent = value;
        slider4.style.background = 'linear-gradient(to right, #FC9F43 0%, #FC9F43 ' + percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
    });


    const sliderInput5 = document.querySelector(".input-slider5");
    const numberLabel5 = document.querySelector(".number--label5");
    const slider5 = document.getElementById("input-slider5")
    sliderInput5.addEventListener("input", () => {
        const { value, min, max, offsetWidth } = sliderInput5;
        const percent = ((value - min) / (max - min)) * 100;
        const newPosition = percent * (offsetWidth / 100);
        //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
        numberLabel5.textContent = value;
        slider5.style.background = 'linear-gradient(to right, rgb(60 137 251) 0%, rgb(60 137 251) ' + percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
    });

    const sliderInput6 = document.querySelector(".input-slider6");
    const numberLabel6 = document.querySelector(".number--label6");
    const slider6 = document.getElementById("input-slider6")
    sliderInput6.addEventListener("input", () => {
        const { value, min, max, offsetWidth } = sliderInput6;
        const percent = ((value - min) / (max - min)) * 100;
        const newPosition = percent * (offsetWidth / 100);
        //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
        numberLabel6.textContent = value;
        slider6.style.background = 'linear-gradient(to right, #FC697D 0%, #FC697D ' + percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
    });

    const sliderInput7 = document.querySelector(".input-slider7");
    const numberLabel7 = document.querySelector(".number--label7");
    const slider7 = document.getElementById("input-slider7")
    sliderInput7.addEventListener("input", () => {
        const { value, min, max, offsetWidth } = sliderInput7;
        const percent = ((value - min) / (max - min)) * 100;
        const newPosition = percent * (offsetWidth / 100);
        //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
        numberLabel7.textContent = value;
        slider7.style.background = 'linear-gradient(to right, #fc9f43 0%, #fc9f43 ' + percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
    });
}
$('.label.ui.dropdown')
    .dropdown();

$('.no.label.ui.dropdown')
    .dropdown({
        useLabels: false
    });

$('.ui.button').on('click', function () {
    $('.ui.dropdown')
        .dropdown('restore defaults')
})

$('body').on('click', '.add_button', function () {
    $('.field_wrapper .displaynone:first').find('select').css("border-color", "");
    $('.field_wrapper .displaynone').find('select').css("border-color", "");
    $('.field_wrapper .displaynone:first').removeClass('displaynone').addClass('displayblock');

    if ($('.field_wrapper .displaynone:first').length == 0) {
        $('.add_button').hide();

    } else {
        $('.add_button').show();
        checkTheDropdowns();
    }


})

$('body').on('click', '.remove', function () {
    var id = $(this).attr('data-id');

    var subid = $(this).attr('data-subid');
    $('.flex' + id).removeClass('displayblock').addClass('displaynone');
    if ($('.field_wrapper .displaynone:first').length != 0) {
        $('.add_button').show();
    }
    $('.subj' + subid + ' :selected').prop('selected', false);
    $('.subject_other' + id).val('');
    $('.removesubjother' + id).hide();

});

// $('body').on('click','.add_buttons',function(){
//     $('.field_wrappers .displaynones:first').removeClass('displaynones').addClass('displayblocke');
//     if($('.field_wrappers .displaynones:first').length==0){
//         $('.add_buttons').hide();
//     }else{
//         $('.add_buttons').show();
//     }
// })

// $('body').on('click', '.removee', function(){
// var id=$(this).attr('data-id');
// $('.flexx'+id).removeClass('displayblocke').addClass('displaynones');
// // jQuery('.flex'+id).attr('style','display:none !important');
// if($('.field_wrappers .displaynones:first').length!=0){
//     $('.add_buttons').show();
// }
// });



$(document).ready(function () {
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.add_button_education'); //Add button selector
    var wrapper = $('.appendedcation'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if ($('#educationid').val() == "" || $('#educationid').val() == "0") {
        var b = 1;
    } else {
        var b = $('#educationid').val();
    }
    // Once add button is clicked
    $(addButton).click(function () {

        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            b++;
            var education = $('#hig').html();


            var fieldHTML = '<div class="removeeducation' + b + ' row"><div class="col-xl-6 col-md-6"><div class="login__form"><label class="form__label">Education*</label><select class="common__login__input form-select" type="text" placeholder="Experience teaching ages" name="highest_level_of_education[]" id="highest_level_of_education' + b + '" data-id="' + b + '">' + education + '</select></div><span id="highest_level_of_education_error" class="error rederror"></span></div><div class="col-xl-6 col-md-6"><div class="login__form"><label class="form__label">Month and Year of Graduation*</label><input class="common__login__input entervalue month_and_year_graduation" type="text" placeholder="Month and Year of Graduation" name="month_and_year_graduation[]" id="month_and_year_graduation' + b + '" value="" data-id="' + b + '"></div><span id="month_and_year_graduation_error" class="error rederror"></span></div><div class="col-xl-6 col-md-6"><div class="login__form"><label class="form__label">GPA*</label><input class="common__login__input entervalue" type="text" placeholder="Enter GPA" name="GPA[]" id="GPA' + b + '" onkeypress="validate(event)" data-id="' + b + '"></div><span id="GPA_error" class="error rederror"></span></div><div class="addremovemargin"><a href="javascript:void(0);" class="removeeducation" data-id="' + b + '" data-subid="1"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div></div>'; //New input field html 
            $(wrapper).append(fieldHTML); //Add field html
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $(wrapper).on('click', '.removeeducation', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');

        $('.removeeducation' + id).remove(); //Remove field html
        x--; //Decrease field counter
    });
});
$(document).ready(function () {
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.add_button_education_step'); //Add button selector
    var wrapper = $('.appendedcation'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if ($('#educationid').val() == "" || $('#educationid').val() == "0") {
        var b = 1;
    } else {
        var b = $('#educationid').val();
    }
    // Once add button is clicked
    $(addButton).click(function () {

        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            b++;
            var education = $('#highest_level_of_education1').html();
            var fieldHTML = '<div class="col-xl-6 col-md-6 removeeducation' + b + '"><div class="login__form"><h4 class="form-heading" style="opacity: 0;">Education</h4><label class="form__label">Highest Level of Education*</label><select class="common__login__input form-select" type="text" placeholder="Experience teaching ages" name="highest_level_of_education[]" id="highest_level_of_education1">' + education + '</select></div></div><div class="col-xl-6 col-md-6 removeeducation' + b + '"><div class="login__form"><h4 class="form-heading" style="opacity:0">Teaching Experience</h4><label class="form__label">Month and Year of Graduation*</label><input class="common__login__input month_and_year_graduation" type="text" placeholder="Month and Year of Graduation" name="month_and_year_graduation[]" id="month_and_year_graduation1" value=""></div></div><div class="col-xl-6 col-md-6 removeeducation' + b + '"><div class="login__form"><label class="form__label">Enter GPA*</label><input class="common__login__input" type="text" placeholder="Enter GPA" name="GPA[]" id="GPA1" onkeypress="validate(event)" value=""></div></div><div class="addremovemargin removeeducation' + b + '"><a href="javascript:void(0);" class="removeeducation" data-id="' + b + '"><div class="addremovemargin"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div><div class="clearfix"></div>'; //New input field html 
            $(wrapper).append(fieldHTML); //Add field html
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $(wrapper).on('click', '.removeeducation', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');

        $('.removeeducation' + id).remove(); //Remove field html
        x--; //Decrease field counter
    });
});



$(document).ready(function () {
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.add_buttons'); //Add button selector
    var wrapper = $('.field_wrappers'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if ($('#teachloopid').val() != "") {

        var xx = $('#teachloopid').val();
    } else {
        var xx = 1;
    }

    // Once add button is clicked
    $(addButton).click(function () {
        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            xx++;
            checkTheDropdownssetsubject();
            var subject = $('#subject_data_teaching').html();
            var fieldHTML = '<div class="row d-flex flexx' + xx + ' classfile" data-id="' + xx + '"><div class="col-xl-6 col-md-6"><div class="login__form select sel" style="position:relative"><select class="form-select setsubject" aria-label="Algebra 1" name="ass_subject[]" id="subject' + xx + '" data-id="' + xx + '">' + subject + '</select></div><span id="subject_two_error" class="error rederror"></span><div class="login__form sel subsubjectteaching" style="display:none"></div><div class="login__form"> <p class="note-text notetextplace" ></p><input readonly="readonly" class="displaynones common__login__input topic entervalue whiteborder" name="topic[]" placeholder="Please choose a topic of your choice to record your teaching video."></div><div class="login__form teachingother teaching_other_specify_' + xx + '" style="display:none"><input class="common__login__input entervalue" type="text" placeholder="Enter Subject" name="teach_other_specify[]" id="teach_other_specify" value=""></div><div class="addremovemargin"><a href="javascript:void(0);" class="removee" data-id="' + xx + '"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div></div><div class="col-xl-6 col-md-6"><div class="mb-3"><input type="file" id="myFile" style="top:13px" name="file[]" data-id="' + xx + '" id="file_teaching_two" class="vtwo teac' + xx + ' form-control fileinput file_teaching_' + xx + ' file_teaching_two t' + xx + '" accept="video/mp4,video/x-m4v,video/*"><div class="text-icon" style="z-index:0"><span class="text file_teaching_' + xx + '_text gray">Upload Video</span></div><span class="text-icon-2"><span class="icon selectfile"><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></span></div><div class="login__form" style="position:relative"></div><span id="file_teaching_one_name"></span><span id="file_teaching_one_error" class="error rederror"></span><input type="hidden" id="teaching_value_' + xx + '" class="teaching_value_' + xx + '" name="tfile[]" value=""></div></div>'; //New input field html 
            $(wrapper).append(fieldHTML); //Add field html
            checkTheDropdownssetsubject();
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $(wrapper).on('click', '.removee', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');
        $(this).parents('.d-flex').remove();
        //$('.flexx'+id).remove(); //Remove field html
        x--; //Decrease field counter
        xx--;
    });
});
$(document).ready(function () {
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.add_button_classroom'); //Add button selector
    var wrapper = $('.field_wrapperc'); //Input field wrapper
    //New input field html 
    var xe = 1; //Initial field counter is 1

    var loop = $('#classroomloop').val();
    if (loop != 0) {
        var x = loop;
    } else {
        var x = 1;
    }

    // Once add button is clicked
    $(addButton).click(function () {
        //Check maximum number of input fields
        if (xe < maxField) {
            x++;
            xe++; //Increase field counter
            var fieldHTML = '<div class="row removeclassromrow' + xe + ' reclassroom"><div class="col-xl-6 col-md-12 classfile"><div class="mb-3"><input type="file" id="myFile" style="top:13px" data-id="' + x + '" name="file[classroom][]" id="classroomfile" class="classroomfile form-control fileinput" accept="video/mp4,video/x-m4v,video/*"><div class="addremovemargin"><a href="javascript:void(0);" class="remove_button selectfile" data-id="' + x + '"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div><div class="text-icon" style="z-index:0"><span class="text classroom' + x + ' gray">Upload Video</span></div><span class="text-icon-2"><span class="icon selectfile"><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></span></div><div class="login__form" style="position:relative"></div><span id="classroomfile_name"></span><span id="classroomfile_error" class="error rederror"></span><input type="hidden" id="class_value" value=""></div></div>';
            $(wrapper).append(fieldHTML); //Add field html
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $(wrapper).on('click', '.remove_button', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');
        $('.removeclassromrow' + id).remove(); //Remove field html
        xe--; //Decrease field counter
    });
});

// $(document).ready(function(){
//     var maxField = 10; //Input fields increment limitation
//     var addButton = $('.add_button_classroom'); //Add button selector
//     var wrapper = $('.field_wrapperc'); //Input field wrapper
//    //New input field html 
//     var x = 1; //Initial field counter is 1

//     // Once add button is clicked
//     $(addButton).click(function(){
//         //Check maximum number of input fields
//         if(x < maxField){ 
//             x++; //Increase field counter
//              var fieldHTML = '<div class="row removeclassromrow'+x+'"><div class="col-xl-6 col-md-12 classfile"><div class="mb-3"><input type="file" id="myFile" style="top:13px" name="file[classroom][]" id="classroomfile" class="classroomfile form-control fileinput" accept="video/mp4,video/x-m4v,video/*"><div class="addremovemargin"><a href="javascript:void(0);" class="remove_button selectfile" data-id="'+x+'"><svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1537_1838)"><mask id="mask0_1537_1838" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="31" height="31"><path d="M31 0H0V31H31V0Z" fill="white"/></mask><g mask="url(#mask0_1537_1838)"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 0C6.944 0 0 6.944 0 15.5C0 24.056 6.944 31 15.5 31C24.056 31 31 24.056 31 15.5C31 6.944 24.056 0 15.5 0Z" fill="#F70000"/><path fill-rule="evenodd" clip-rule="evenodd" d="M21.95 17.3H17.3V21.95C17.3 22.8025 16.6025 23.5 15.75 23.5C14.8975 23.5 14.2 22.8025 14.2 21.95V17.3H9.55C8.6975 17.3 8 16.6025 8 15.75C8 14.8975 8.6975 14.2 9.55 14.2H14.2V9.55C14.2 8.6975 14.8975 8 15.75 8C16.6025 8 17.3 8.6975 17.3 9.55V14.2H21.95C22.8025 14.2 23.5 14.8975 23.5 15.75C23.5 16.6025 22.8025 17.3 21.95 17.3Z" fill="white"/><path d="M19 7H12V14.2H19V7Z" fill="#F70000"/><path d="M18 17.3008H11V24.3008H18V17.3008Z" fill="#F70000"/></g></g><defs><clipPath id="clip0_1537_1838"><rect width="31" height="31" fill="white"/></clipPath></defs></svg></a></div><div class="text-icon" style="z-index:0"><span class="text file_teaching_ones_text gray">Upload Video</span></div><span class="text-icon-2"><span class="icon selectfile"><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></span></div><div class="login__form" style="position:relative"></div><span id="classroomfile_name"></span><span id="classroomfile_error" class="error rederror"></span><input type="hidden" id="class_value" value=""></div></div>'; 
//             $(wrapper).append(fieldHTML); //Add field html
//         }else{
//             alert('A maximum of '+maxField+' fields are allowed to be added. ');
//         }
//     });

//     // Once remove button is clicked
//     $(wrapper).on('click', '.remove_button', function(e){
//         e.preventDefault();
//         var id=$(this).attr('data-id');
//         $('.removeclassromrow'+id).remove(); //Remove field html
//         x--; //Decrease field counter
//     });
// });

$('body').on('click', '.selectfile', function () {

    $(this).parents('.classfile').find('input[type="file"]').click();
})
$('body').on('click', '.awardicon', function () {

    $(this).parents('.awardfile').find('input[type="file"]').click();
})

$('body').on('click', '.certificateicon', function () {

    $(this).parents('.certificatefile').find('input[type="file"]').click();
})
$('body').on('click', '.distinctioncon', function () {
    $(this).parents('.distinctions').find('input[type="file"]').click();
})



var pathArray = window.location.pathname.split('/');
if (pathArray[1] == 'onboarding-step') {
    $(document).ready(function () {
        checkTheDropdowns();
        checkTheDropdownssetsubject();
    });
}

function checkTheDropdowns() {
    //     var arr  = $('.selectRound').find(':selected');
    //     $('.selectRound').find('option').show();
    //     $.each($('.selectRound'), function(){  
    //       var self = this;
    //       var selectVal = $(this).val();
    //       $.each(arr, function(){         
    //           if (selectVal !== $(this).val()){
    //             if($(this).val()!=='Other' ){
    //                   $(self).find('option[value="'+$(this).val()+'"]').hide()
    //             }
    //           } else {
    //                   $(self).find('option[value="'+$(this).val()+'"]').show()
    //           }
    //       });
    //    })
};

function checkTheDropdownssetsubject() {
    /*var arr  = $('.setsubject').find(':selected');
    
    $('.setsubject').find('option').show();
    $.each($('.setsubject'), function(){  
      var self = this;
      var selectVal = $(this).val();
      $.each(arr, function(){     
  
          if (selectVal !== $(this).val()){
            if($(this).val()!=='Other' ){
                  $(self).find('option[value="'+$(this).val()+'"]').hide()
            }
          } else {
                  $(self).find('option[value="'+$(this).val()+'"]').show()
          }
      });
   })*/
};


var oneSelecteaward = false;
function Checkaward() {
    $('#secondstep .awardfiles').each(function (k, v) {

        if ($(v).val() != "") {

            oneSelecteaward = true;

        }

    });

    return oneSelecteaward;
}


var oneSelecteaward = false;
function Checkaward() {
    $('#secondstep .awardfiles').each(function (k, v) {
        var id = $(v).attr('data-id');
        if ($(v).val() != "" || $('.award_value_' + id).val() != "") {

            oneSelecteaward = true;

        }


    });

    return oneSelecteaward;
}

var oneSelectecertificate = false;
function Checkcertificate() {
    $('#secondstep .certificatefile ').each(function (k, v) {

        if ($(v).val() != "") {

            oneSelectecertificate = true;

        }

    });

    return oneSelectecertificate;
}

var oneSelectenoteable = false;
function Checknoteable() {
    $('#secondstep .distinctionsfile ').each(function (k, v) {

        if ($(v).val() != "") {

            oneSelectenoteable = true;

        }

    });

    return oneSelectenoteable;
}


$(document).ready(function () {
    var maxField = 10; //Input fields increment limitation
    var addButton = $('.add_button_award'); //Add button selector
    var wrapper = $('.field_award'); //Input field wrapper
    //New input field html 
    var lastid = $('#awardlastid').val();
    var x = 1; //Initial field counter is 1
    if (lastid != '0') {
        var vv = lastid;
    } else {
        var vv = 1;
    }
    // Once add button is clicked
    $(addButton).click(function () {
        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            vv++;
            var fieldHTML = '<div class="row field_award awardremove' + vv + '"><div class="col-xl-6 col-md-12 awardfile"><div class="mb-3"><label class="form__label">Image</label><input type="file" id="myFile" style="top:13px" name="file[awards][]" id="awardfile" class="awardfile form-control fileinput awardfiles awar' + vv + '" data-id="' + vv + '" accept="image/png, image/gif, image/jpeg"><div class="addremovemargin"><a href="javascript:void(0);" class="removeaward" data-id="' + vv + '" data-subid="2"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div><div class="text-icon" style="z-index:0;z-index: 0; top: 50px !important;"><span class="text gray award_' + vv + '_text">Upload Image</span></div><span class="text-icon-2"><span class="icon awardicon"><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></span></div><div class="login__form" style="position:relative"></div><span id="award_name"></span><span id="award_error" class="error rederror"></span><input type="hidden" id="award_value_' + vv + '" class="award_value_' + vv + '" name="award_value[]" value=""></div><div class="col-xl-6 col-md-6"><div class="mb-3"><div class="login__form"><label class="form__label">Description</label><input class="common__login__input" type="text" placeholder="Enter Description" name="award_desc[]" id="award_desc" value=""></div></div></div></div>';

            $(this).parents('#secondstep').find('.field_award').last().after(fieldHTML);
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $(wrapper).on('click', '.removeaward', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');

        $('.awardremove' + id).remove(); //Remove field html
        x--; //Decrease field counter
    });
});

$('body').on('click', '.removeaward', function () {
    var id = $(this).attr('data-id');

    $('.awardremove' + id).remove(); //Remove field html
})



$(document).ready(function () {
    var maxField = 10; //Input fields increment limitation
    var addButton = $('.add_button_certificate'); //Add button selector
    var wrapper = $('.field_certificate'); //Input field wrapper
    //New input field html 
    var lastid = $('#certificatelastid').val();
    var x = 1; //Initial field counter is 1
    if (lastid != '0') {
        var tt = lastid;
    } else {
        var tt = 1;
    }
    // Once add button is clicked
    $(addButton).click(function () {
        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            tt++;
            var fieldHTML = '<div class="row field_certificate certificateremove' + tt + '"><div class="col-xl-6 col-md-6 certificatefile"><div class="mb-3"><label class="form__label">Image</label><input type="file" id="myFile" style="top:13px" name="file[certificate][]" id="certificatefile" class="certificatefile form-control fileinput cetificatefiles cer' + tt + '" data-id="' + tt + '" accept="image/png, image/gif, image/jpeg"><div class="addremovemargin"><a href="javascript:void(0);" class="removecertificate" data-id="' + tt + '" data-subid="2"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div><div class="text-icon" style="z-index: 0; top: 50px !important;"><span class="text gray certificate_' + tt + '_text">Upload Image</span></div><span class="text-icon-2"><span class="icon certificateicon"><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></span></div><input type="hidden" id="certificate_value' + tt + '" value="" class="certificate_value' + tt + '" name="certificate_value[]"></div><div class="col-xl-6 col-md-6"><div class="mb-3"><div class="login__form"><label class="form__label">Description</label><input class="common__login__input" type="text" placeholder="Enter Description" name="certificate_desc[]" id="certificate_desc" value=""></div></div></div></div>';

            $(this).parents('#secondstep').find('.field_certificate').last().after(fieldHTML);
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $('body').on('click', '.removecertificate', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');

        $('.certificateremove' + id).remove(); //Remove field html
        x--; //Decrease field counter
    });
});


$(document).ready(function () {
    var maxField = 10; //Input fields increment limitation
    var addButton = $('.add_button_distinctions'); //Add button selector
    var wrapper = $('.field_distinctions'); //Input field wrapper
    //New input field html 
    var lastid = $('#distinctionsid').val();
    var x = 1; //Initial field counter is 1
    if (lastid != '0') {
        var o = lastid;
    } else {
        var o = 1;
    }
    // Once add button is clicked
    $(addButton).click(function () {
        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            o++;
            var fieldHTML = '<div class="row field_distinctions distinctionsremove' + o + '"><div class="col-xl-6 col-md-6 distinctions"><div class="mb-3"><label class="form__label">Image</label><input type="file" id="myFile" style="top:13px" name="file[distinctions][]" id="distinctionsfile" class="distinctionsfile form-control fileinput distinctionsfiles dist' + o + '" data-id="' + o + '" accept="image/png, image/gif, image/jpeg"><div class="addremovemargin"><a href="javascript:void(0);" class="removedistions" data-id="' + o + '" data-subid="2"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div><div class="text-icon" style="z-index:0;top: 50px !important;"><span class="text gray distinctions_' + o + '_text">Upload Image</span></div><span class="text-icon-2"><span class="icon distinctioncon" data-id="' + o + '"><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></span></div><input type="hidden" id="distinction_value' + o + '" value="" class="distinction_value' + o + '" name="distinction_value[]"></div><div class="col-xl-6 col-md-6"><div class="mb-3"><div class="login__form"><label class="form__label">Description</label><input class="common__login__input" type="text" placeholder="Enter Description" name="notable_desc[]" id="notable_desc" value=""></div></div></div></div>';

            $(this).parents('#secondstep').find('.field_distinctions').last().after(fieldHTML);
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $('body').on('click', '.removedistions', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');

        $('.distinctionsremove' + id).remove(); //Remove field html
        x--; //Decrease field counter
    });
});

$('body').on('click', '.backgroundmodel', function () {
    var id = $(this).attr('data-id');
    $('#back_med_id').val(id);
})


$('body').on('click', '.savecompletion', function () {

    var loading = $(this).attr('data-loading-text');
    var reference = document.getElementById('reference').value;
    var date = document.getElementById('date').value;
    var file = document.getElementsByClassName("comfile")[0].value;

    if (reference == "") {
        $("#reference").css("border-color", "red");

    }
    if (date == "") {
        $("#date").css("border-color", "red");
    }
    if (file == "") {
        $(".comfiles").css("border-color", "red");
    }


    var reference = document.getElementById('reference').value;
    if (reference.length == "") {
        // $('#password_error').html('Please Enter Your Password');
        $("#reference").css("border-color", "red");
        document.getElementById('reference').value = "";
        document.getElementById('reference').focus();
        return false;
    } else {
        $('#reference_error').html('');

        $('#reference').css('border-color', '#d8dadc');
    }

    var date = document.getElementById('date').value;
    if (date.length == "") {
        // $('#first_name_error').html('Please Enter Your First Name');
        $("#date").css("border-color", "red");
        document.getElementById('date').value = "";
        document.getElementById('date').focus();
        return false;
    } else {
        $('#date_error').html('');
        $('#date').css('border-color', '#d8dadc');
    }
    var myFile = document.getElementById('myFileb').value;
    // var myFile = document.getElementsByClassName("comfile")[0].value;

    var ext = myFile.split('.').pop().toLowerCase();

    if (myFile == "") {
        $(".comfiles").css("border-color", "red");
        document.getElementsByClassName("comfile")[0].focus();
        return false;
    }



    var $this = $(this);
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/create_completion';

    var formData = new FormData($("#uponcompletionid")[0]);
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success('Submitted Successfully');
                location.reload();
                $("#uponcompletionid")[0].reset();
                $('#staticBackdrop').modal('hide');
                $this.html('Complete');
                $this.prop("disabled", false);
            } else {
                $this.html('Complete');
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $this.html('Complete');
        $this.prop("disabled", false);


    });
})

$('body').on('click', '.prevuewbackgroundmodel', function () {

    var id = $(this).attr('data-id');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/show_preview';
    $.ajax({
        type: "POST",
        url: url,
        data: { 'id': id }, // serializes the form's elements.
        dataType: "html",
        success: function (data) {

            $('#append').html(data);
            $('#previewmodel').modal('show');
        }
    });

})



//interview
$('body').on('click', '#save_timeslot', function (event) {
    event.preventDefault()
    // var link = $('#link').val();
    // if (link == "") {
    //     $('#link_error').html('Please enter link');
    //     $('#link').val('');
    //     $('#link').focus();
    //     return false;

    // } else {
    //     $('#link_error').html('');
    // }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/savetimeslot';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addtimeslot')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                window.location.href = response.redirect;
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})



























$('body').on('click', '.saveclassroom', function () {
    var id = $(this).attr('data-id');
    var value = $(this).attr('data-value');

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save_training';
    var $this = $(this);
    $.ajax({
        url: url,
        type: 'post',
        data: { 'id': id, 'value': value },
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                // $this.text('Quiz')
                $("#done_" + id).css('display', 'none');
                $("#quiz_" + id).css('display', 'block');
                // $this.removeClass('saveclassroom');
                $this.attr('class', 'btn btn-success next default__button text-success');
                // alertify.success('Video saved');
            } else {
                $this.text('Quiz')
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

//refrence
$('body').on('click', '#add_Reference', function (event) {
    event.preventDefault()
    var full_name = $('#full_name').val();
    var email = $('#email').val();
    var phone = $('#phone').val();
    var relationship = $('#relationship').val();

    // if (full_name == "") {
    //     $("#full_name").css("border-color", "red");
    //     document.getElementById('full_name').value = "";
    //     document.getElementById('full_name').focus();
    //     return false;
    // } else {
    //     $('#full_name_error').html('');
    //     $('#full_name').css('border-color', '#d8dadc');
    // }

    // if (email == "") {
    //     $("#email").css("border-color", "red");
    //     document.getElementById('email').value = "";
    //     document.getElementById('email').focus();
    //     return false;
    // } else {
    //     $('#email_error').html('');
    //     $('#email').css('border-color', '#d8dadc');
    // }

    // if (phone == "") {
    //     $("#phone").css("border-color", "red");
    //     document.getElementById('phone').value = "";
    //     document.getElementById('phone').focus();
    //     return false;
    // } else {
    //     $('#phone_error').html('');
    //     $('#phone').css('border-color', '#d8dadc');
    // }

    // if (relationship == "") {
    //     $("#relationship").css("border-color", "red");
    //     document.getElementById('relationship').value = "";
    //     document.getElementById('relationship').focus();
    //     return false;
    // } else {
    //     $('#relationship_error').html('');
    //     $('#relationship').css('border-color', '#d8dadc');
    // }


    var transcript = document.getElementById("transcript").value

    if (transcript == "") {
        var transcript = document.getElementById("transcript").value;
        if (transcript == "") {
            $(".transcript").css("border-color", "red");
        }
    }


    var teacher_type = document.getElementById("teacher_type").value
    if (teacher_type == 'yes') {
        var certifications = document.getElementById("certifications").value
        if (certifications == "") {
            var certifications = document.getElementById("certifications").value;
            if (certifications == "") {
                $(".certifications").css("border-color", "red");
            }
        }
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/save-reference';

    $.ajax({
        url: url,
        type: 'post',
        processData: false,
        contentType: false,
        data: new FormData($('#addReference')[0]),
        dataType: "json",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {
            if (response.success == true) {
                window.location.href = response.redirect;
            }
        },
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
})

$('#transcript').change(function () {

    $('.transcript').text(this.files && this.files.length ? this.files[0].name : '');

})

$('#certifications').change(function () {

    $('.certifications').text(this.files && this.files.length ? this.files[0].name : '');

})


$('#transcript_s').change(function () {

    $('.transcript_s').text(this.files && this.files.length ? this.files[0].name : '');

})

$('#certifications_s').change(function () {

    $('.certifications_s').text(this.files && this.files.length ? this.files[0].name : '');

})

$('body').on('click', '#forgot', function () {
    if ($(this).is(":checked") == false) {
        $('.sameas').show();
        $('.sameasonlineshow').hide();
        $('.adbtnrange').addClass('d-flex');
    } else {
        $('.adbtnrange').removeClass('d-flex');
        $('.sameas').hide();

        $('.sameasonlineshow').show();
    }
})
$('body').on('change', '.form-select', function () {

    if ($(this).filter(':selected').val() != "") {
        $(this).css("border-color", "");
    }
})

$('body').on('keyup keypress blur change', '.entervalue', function () {
    if ($(this).val() != "") {
        $(this).css("border-color", "");
        $(this).next().html('');
        $(this).removeClass('errorc')
    }
    if ($(this).is(':selected') == true) {
        $(this).css("border-color", "");
    }
})

$('body').on('keyup keypress blur change', '.from_date', function () {
    if ($(this).val() != "") {
        $(this).css("border-color", "");
        $(this).next().html('');
        $(this).removeClass('errorc')
    }
    if ($(this).is(':selected') == true) {
        $(this).css("border-color", "");
    }
})

$('body').on('keyup keypress blur change', '.to_date', function () {
    if ($(this).val() != "") {
        $(this).css("border-color", "");
        $(this).next().html('');
        $(this).removeClass('errorc')
    }
    if ($(this).is(':selected') == true) {
        $(this).css("border-color", "");
    }
})
$('body').on('keyup keypress blur change', '.time-zones', function () {
    if ($(this).val() != "") {
        $(this).css("border-color", "");
        $(this).next().html('');
        $(this).removeClass('errorc')
    }
    if ($(this).is(':selected') == true) {
        $(this).css("border-color", "");
    }
})





$('body').on('keyup keypress change', '.from_teach_in_person_location', function () {

    $(this).parents(".calendar").find('.map').attr('id', 'map' + $(this).attr('data-id'));

    initAutocompletemap($(this).attr('data-id'));


})
$('body').on('keyup keypress change', '.to_teach_in_person_location', function () {

    $(this).parents(".calendar").find('.map').attr('id', 'map' + $(this).attr('data-id'));

    initAutocompletemapto($(this).attr('data-id'));



})
function initAutocompletemapto(id) {

}

function initAutocompletemap(id) {

    const map = new google.maps.Map(document.getElementById("map" + id), {
        center: { lat: -33.8688, lng: 151.2195 },
        zoom: 13,
        mapTypeId: "roadmap",
    });
    // Create the search box and link it to the UI element.
    const input = document.getElementById("from_teach_in_person_location" + id);
    const searchBox = new google.maps.places.SearchBox(input);

    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);
    // Bias the SearchBox results towards current map's viewport.
    map.addListener("bounds_changed", () => {
        searchBox.setBounds(map.getBounds());
    });

    let markers = [];

    // Listen for the event fired when the user selects a prediction and retrieve
    // more details for that place.
    searchBox.addListener("places_changed", () => {
        const places = searchBox.getPlaces();

        if (places.length == 0) {
            return;
        }

        // Clear out the old markers.
        markers.forEach((marker) => {
            marker.setMap(null);
        });
        markers = [];

        // For each place, get the icon, name and location.
        const bounds = new google.maps.LatLngBounds();
      
        places.forEach((place) => {
            if (!place.geometry || !place.geometry.location) {
                return;
            }

            const icon = {
                url: place.icon,
                size: new google.maps.Size(71, 71),
                origin: new google.maps.Point(0, 0),
                anchor: new google.maps.Point(17, 34),
                scaledSize: new google.maps.Size(25, 25),
            };

            // Create a marker for each place.
            markers.push(
                new google.maps.Marker({
                    map,
                    icon,
                    title: place.name,
                    position: place.geometry.location,
                }),
            );

            if (place.geometry.viewport) {
                // Only geocodes have viewport.
                bounds.union(place.geometry.viewport);
            } else {
                bounds.extend(place.geometry.location);
            }

        });
        map.fitBounds(bounds);

    });

}

$(document).ready(function () {

    var cd = (new Date()).toISOString().split('T')[0];

    $(".from_date").each(function () {
        $(this).attr('min', cd);
    })

});





$('body').on('change', '#certified_special_education', function () {

    var certi = $(this).children('option:selected').toArray().map(item => item.value)
    if (jQuery.inArray("Other", certi) != -1) {
        $("#certified_special_education").each(function () {
            if ($(this).children('option:selected').val() != 'Other') {
                // $(this).children('option:selected').prop("selected", false);
                // $(this).children('option[value="Other"]').prop("selected", true);
                // $("#certified_special_education").trigger("change");
            }
        })
        // if($("#certified_special_education > option").val()!='Other'){
        // $("#certified_special_education > option").prop("selected", false);
        // $("#certified_special_education").trigger("change");
        // }



        //     $('#certified_special_education :selected').prop('selected', false);
        //     // $('#certified_special_education option:selected').prop('selected',false);
        // // $('#certified_special_education :selected').prop('selected', false);
        // $("#certified_special_education option:selected").removeAttr("selected");
        // $("#certified_special_education").next().children().children().children().find('.select2-selection__choice').each(function () {
        //     if($(this).attr('title')!='Other'){        
        //     $(this).remove()       
        // }
        // });

        $('.Certificationsother').show();
    } else {
        $('.Certificationsother').hide();
    }

})

$('body').on('click', '#submitadminis', function () {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Saving..";
    var onlinerange = false;
    $('.appenauth').find('input').each(function () {
        if (this.hasAttribute("name")) {

            if ($(this).attr('type') == 'file' && $(this).attr('name') == 'authoziation_file[]') {

                if ($(this).attr('data-values') == "") {
                    onlinerange = false;

                    $(this).next().css("border-color", "red");
                } else {
                    onlinerange = true;
                    $(this).next().css("border-color", "");
                }

            }

            if ($(this).attr('name') == 'discription[]') {

                if ($(this).val() == "") {
                    onlinerange = false;
                    $(this).css("border-color", "red");
                } else {
                    onlinerange = true;
                    $(this).css("border-color", "");
                }
            }

        }
    })


    var first_name = $('#first_name').val();
    var middle_name = $('#middle_name').val();
    var last_name = $('#last_name').val();
    var address = $('#address').val();
    var apt = $('#apt').val();
    var city = $('#city').val();
    var state = $('#state').val();
    var zip_code = $('#zip_code').val();
    var dob = $('#dob').val();
    var myFile = $('#myFile').val();
    var myFileb = $('#myFileb').val();

    if (first_name == "") {
        $("#first_name").css("border-color", "red");
        document.getElementById('first_name').value = "";
        document.getElementById('first_name').focus();

    } else {

        $('#first_name').css('border-color', '');
    }


    if (middle_name == "") {
        $("#middle_name").css("border-color", "red");
        document.getElementById('middle_name').value = "";
        document.getElementById('middle_name').focus();

    } else {

        $('#middle_name').css('border-color', '');
    }


    if (last_name == "") {
        $("#last_name").css("border-color", "red");
        document.getElementById('last_name').value = "";
        document.getElementById('last_name').focus();

    } else {

        $('#last_name').css('border-color', '');
    }


    if (address == "") {
        $("#address").css("border-color", "red");
        document.getElementById('address').value = "";
        document.getElementById('address').focus();

    } else {

        $('#address').css('border-color', '');
    }

    if (apt == "") {
        $("#apt").css("border-color", "red");
        document.getElementById('apt').value = "";
        document.getElementById('apt').focus();

    } else {

        $('#apt').css('border-color', '');
    }



    if (city == "") {
        $("#city").css("border-color", "red");
        document.getElementById('city').value = "";
        document.getElementById('city').focus();

    } else {

        $('#city').css('border-color', '');
    }



    if (state == "") {
        $("#state").css("border-color", "red");
        document.getElementById('state').value = "";
        document.getElementById('state').focus();

    } else {

        $('#state').css('border-color', '');
    }


    if (zip_code == "") {
        $("#zip_code").css("border-color", "red");
        document.getElementById('zip_code').value = "";
        document.getElementById('zip_code').focus();

    } else {

        $('#zip_code').css('border-color', '');
    }
    var email = $('#email').val();
    var mobile = $('#mobile').val();
    if (email == "") {
        $("#email").css("border-color", "red");
        document.getElementById('email').value = "";
        document.getElementById('email').focus();

    } else {

        $('#email').css('border-color', '');
    }
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        // $('#email_error').html('Please enter valid email address');
        $("#email").css("border-color", "red");
        $("#email").focus();


    } else {
        // $('#email_error').html('');
        $("#email").css("border-color", "");
    }


    if (mobile == "") {
        $("#mobile").css("border-color", "red");
        document.getElementById('mobile').value = "";
        document.getElementById('mobile').focus();

    } else {

        $('#mobile').css('border-color', '');
    }

    if (dob == "") {
        $("#dob").css("border-color", "red");
        document.getElementById('dob').value = "";
        document.getElementById('dob').focus();

    } else {

        $('#dob').css('border-color', '');
    }

    if ($("#myFile").attr('value') == "") {
        if (myFile == "") {
            $("#myFile").next().css("border-color", "red");
            document.getElementById('myFile').value = "";
            document.getElementById('myFile').focus();

        } else {

            $('#myFile').next().css('border-color', '');
        }
    }

    if ($("#myFileb").attr('value') == "") {
        if (myFileb == "") {
            $("#myFileb").next().css("border-color", "red");
            document.getElementById('myFileb').value = "";
            document.getElementById('myFileb').focus();

        } else {

            $('#myFileb').next().css('border-color', '');
        }
    }




    if (first_name == "") {
        $("#first_name").css("border-color", "red");
        document.getElementById('first_name').value = "";
        document.getElementById('first_name').focus();
        return false;
    } else {

        $('#first_name').css('border-color', '');
    }


    if (middle_name == "") {
        $("#middle_name").css("border-color", "red");
        document.getElementById('middle_name').value = "";
        document.getElementById('middle_name').focus();
        return false;
    } else {

        $('#middle_name').css('border-color', '');
    }


    if (last_name == "") {
        $("#last_name").css("border-color", "red");
        document.getElementById('last_name').value = "";
        document.getElementById('last_name').focus();
        return false;
    } else {

        $('#last_name').css('border-color', '');
    }


    if (address == "") {
        $("#address").css("border-color", "red");
        document.getElementById('address').value = "";
        document.getElementById('address').focus();
        return false;
    } else {

        $('#address').css('border-color', '');
    }

    if (apt == "") {
        $("#apt").css("border-color", "red");
        document.getElementById('apt').value = "";
        document.getElementById('apt').focus();
        return false;
    } else {

        $('#apt').css('border-color', '');
    }



    if (city == "") {
        $("#city").css("border-color", "red");
        document.getElementById('city').value = "";
        document.getElementById('city').focus();
        return false;
    } else {

        $('#city').css('border-color', '');
    }



    if (state == "") {
        $("#state").css("border-color", "red");
        document.getElementById('state').value = "";
        document.getElementById('state').focus();
        return false;
    } else {

        $('#state').css('border-color', '');
    }


    if (zip_code == "") {
        $("#zip_code").css("border-color", "red");
        document.getElementById('zip_code').value = "";
        document.getElementById('zip_code').focus();
        return false;
    } else {

        $('#zip_code').css('border-color', '');
    }
    var email = $('#email').val();
    var mobile = $('#mobile').val();

    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
    var regemailid = emailfilter.test($("#email").val());
    (regemailid);
    if (regemailid == false) {
        // $('#email_error').html('Please enter valid email address');
        $("#email").css("border-color", "red");
        $("#email").focus();

        return false;
    } else {
        // $('#email_error').html('');
        $("#email").css("border-color", "");
    }


    if (mobile == "") {
        $("#mobile").css("border-color", "red");
        document.getElementById('mobile').value = "";
        document.getElementById('mobile').focus();
        return false;
    } else {

        $('#mobile').css('border-color', '');
    }

    if (dob == "") {
        $("#dob").css("border-color", "red");
        document.getElementById('dob').value = "";
        document.getElementById('dob').focus();
        return false;
    } else {

        $('#dob').css('border-color', '');
    }

    if ($("#myFile").attr('value') == "") {
        if (myFile == "") {
            $("#myFile").next().css("border-color", "red");
            document.getElementById('myFile').value = "";
            document.getElementById('myFile').focus();
            return false;
        } else {

            $('#myFile').next().css('border-color', '');
        }
    }

    if ($("#myFileb").attr('value') == "") {
        if (myFileb == "") {
            $("#myFileb").next().css("border-color", "red");
            document.getElementById('myFileb').value = "";
            document.getElementById('myFileb').focus();
            return false;
        } else {

            $('#myFileb').next().css('border-color', '');
        }
    }


    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var _this = $(this);
    var url = APP_URL + '/submitAdministrative';
    var formData = new FormData($("#administrativeinformation")[0]);
    if (onlinerange == true) {
        $.ajax({
            type: 'POST',
            url: url,
            data: formData,
            dataType: "json",
            processData: false,
            contentType: false,
            dataType: "json",
            beforeSend: function () {
                $('#submitadminis').prop("disabled", true);
                _this.innerHTML = loading;
            },
            success: function (data) {

                if (data.success == true) {
                    alertify.success(data.message);
                    _this.prop("disabled", false);
                    _this.innerHTML = 'Save';
                    location.reload();


                } else {
                    _this.innerHTML = 'Save';
                    _this.prop("disabled", false);
                    alertify.error(data.message);
                }
            },
        }).done(function () {
            _this.innerHTML = 'Submit';
            _this.prop("disabled", false);
        });
    }

})


$(document).ready(function () {
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.add_auth'); //Add button selector
    var wrapper = $('.appenauth'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if ($('#authid').val() == "" || $('#authid').val() == "0") {
        var file = 1;
    } else {
        var file = $('#authid').val();
    }
    // Once add button is clicked
    $(addButton).click(function () {

        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            file++;

            var fieldHTML = '<div class="row removfiles' + file + '"><div class="col-xl-5 col-md-12"><div class="login__form" style="position:relative"><h4 class="form-heading"><input type="file" class="myinputfile' + file + '" id="myFileb" name="authoziation_file[]" data-values=""> <input readonly class="common__login__input second prooffile appendfile' + file + '" name="authoziation_files[]" data-id="' + file + '" type="text" placeholder="Upload File"><a href="javascript:void(0);" data-id="' + file + '" class="prooffile" style="top:13px;position:absolute;right:21px"><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></a></div></div><div class="col-xl-5 col-md-12"><div class="login__form" style="position:relative"><input class="common__login__input entervalue key" type="text" placeholder="Description" name="discription[]" id="discription" value=""></div></div><div class="col-xl-2 col-md-12"><div class="login__form" style="position:relative"><div class="cs-form addremovemargin"><a class="removefile" data-id="' + file + '" href="javascript:void(0);"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"/><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"/></svg></a></div></div></div></div>'; //New input field html 
            $(wrapper).append(fieldHTML); //Add field html
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $(wrapper).on('click', '.removefile', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');
        $('.removfiles' + id).remove(); //Remove field html
        x--; //Decrease field counter
        file--;
    });
});


$('body').on('click', '.saveprofile', function () {
    var loading = $(this).attr('data-loading-text');
    var words = $('#textAreaExample2').val().match(/\S+/g);
    var wordCount = words ? words.length : 0;

    if (wordCount > 100) {
        $('#wordCount').addClass('rederror').text('Maximum 100 words allowed');
        $("#textAreaExample2").focus();
        $('#textAreaExample2').css('border-color', 'red');
        return false;
    } else if (wordCount == 0) {
        $('#wordCount').addClass('rederror').text('Please enter profile description');
        $("#textAreaExample2").focus();
        $('#textAreaExample2').css('border-color', 'red');
        return false;
    } else {
        $('#wordCount').addClass('rederror').text('');
        $(this).css('border-color', '');
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var _this = $(this);
    var url = APP_URL + '/saveschoolprofile';
    var formData = new FormData($("#profileid")[0]);

    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $('.saveprofile').prop("disabled", true);
            _this.innerHTML = loading;
        },
        success: function (data) {

            if (data.success == true) {
                alertify.success(data.message);
                _this.prop("disabled", false);
                _this.innerHTML = 'Save';
                location.reload();


            } else {
                _this.innerHTML = 'Save';
                _this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        _this.innerHTML = 'Submit';
        _this.prop("disabled", false);
    });

})

$('input[type=checkbox]').change(function () {
    if ($(this).attr('name') == 'schoolnotification') {
        var value = $(this).attr('data-value');
        var check = $(this).is(":checked");

        var loading = $(this).attr('data-loading-text');
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        var $this = $(this);
        var url = APP_URL + '/schoolnotification';
        $.ajax({
            type: 'POST',
            url: url,
            data: { 'notification': check, 'value': value },
            dataType: "json",
            success: function (data) {

                if (data.success == true) {

                    alertify.success(data.message);

                } else {

                    alertify.error(data.message);
                }
            },
        })
    }
})
$('body').on('click', '.eye-icons-pass', function () {
    $(this).find('img.eye').toggle();
    let passwordField = $(this).prev('input.password-field');

    if ($(passwordField).attr("type") === 'password') {

        $(passwordField).attr("type", "text");

    } else {
        $(passwordField).attr("type", "password");

    }
});
$('body').on('submit', '#update-password', function (event) {
    event.preventDefault();
    changePasswordCleanup();

    let error = false;
    var password = document.getElementById('password').value;
    var old_password = document.getElementById('old_password').value;
    var password_confirmation = document.getElementById('password_confirmation').value;
    if (password_confirmation == "") {
        $("#password_confirmation").css("border-color", "red");
        $("#password_confirmation_error").html('Please enter confirm password');
        error = true;
    }
    if (password == "") {
        $("#password").css("border-color", "red");
        $("#password_error").html('Please enter new password');
        error = true;
    }
    if (old_password == "") {
        $("#old_password").css("border-color", "red");
        $("#old_password_error").html('Please enter old password');
        error = true;
        return true;
    }

    if (password != "") {
        const messages = checkPasswordValidity(password);
        if (!messages) {

        } else {
            $('#password_error').html('<div class="pmsg">Please enter valid password<div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div></div>');
            $("#password").css("border-color", "red");
            error = true;

        }
    }
    if (error) {
        return true;
    }
    if (password_confirmation == "") {
        $("#password_confirmation").css("border-color", "red");
        $("#password_confirmation_error").html('Please re-enter password');
        error = true;

    }
    if (password_confirmation != "" && password_confirmation != password) {


        $('#password_confirmation_error').html('Passwords do not match');
        $("#password_confirmation").css("border-color", "red");
        error = true;

    }
    if (error) {
        return true;
    }
    var _this = $('#update-password').find('button');
    $.ajax({
        type: 'POST',
        url: $('#update-password').attr('action'),
        data: $('#update-password').serialize(),
        dataType: "json",
        beforeSend: function () {
            $(_this).prop("disabled", true);
            _this.innerHTML = 'loading';
        },
        success: function (data) {

            if (data.success == true) {
                _this.innerHTML = 'Update';
                $(_this).prop("disabled", false);
                alertify.success(data.message);
                $('#update-password')[0].reset();
                changePasswordCleanup();

            }

            if (data.success == false) {
                $("#old_password").css("border-color", "red");
                $("#old_password_error").html(data.message);

            }
        }, error: function (data) {
            _this.innerHTML = 'Update';
            $(_this).prop("disabled", false);
        }
    }).done(function () {
        _this.innerHTML = 'Update';
        $(_this).prop("disabled", false);
    });
});


$('body').on('click', '#school-delete-acoount', function (event) {
    event.preventDefault();

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var _this = $(this);
    var url = APP_URL + '/school-delete-account';
    $.ajax({
        type: 'POST',
        url: url,
        data: {},
        dataType: "json",
        beforeSend: function () {
            $(_this).prop("disabled", true);
            _this.innerHTML = 'Loading';
        },
        success: function (data) {

            if (data.success == true) {
                _this.innerHTML = 'Delete Account';
                $(_this).prop("disabled", false);
                alertify.success(data.message);
                window.location.href = data.redirect;

            }
        }
    }).done(function () {
        _this.innerHTML = 'Delete Account';
        $(_this).prop("disabled", false);
    });
});

function toggleVideo(video) {
    if (video.paused || video.ended) {
        video.play();
    } else {
        video.pause();
    }
}
function toggleReading(element) {
    var paragraph = $(element).closest('.video-paragraph');
    paragraph.find('.truncated-text, .full-text').toggle();
    var isExpanded = paragraph.hasClass('expanded');
    paragraph.toggleClass('expanded');
    $(element).text(isExpanded ? 'Read More' : 'Read Less');
}


$('body').on('change', '#user-account-status', function (event) {
    event.preventDefault();

    var url = event.target.dataset.url;
    var status = $(this).is(":checked") ? 1 : 3;
    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        type: 'POST',
        url: url,
        data: {
            status: status,
        },
        dataType: "json",
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
            }
        }
    });
});

function changePasswordCleanup() {
    $('#old_password_error').html('');
    $("#old_password").css("border-color", "unset");

    $('#password_error').html('');
    $("#password").css("border-color", "unset");

    $('#password_confirmation_error').html('');
    $("#password_confirmation").css("border-color", "unset");
}