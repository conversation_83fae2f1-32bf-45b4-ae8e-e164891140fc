$( document ).ready(function() {

    $('.chat-list a:first').click();
})

///////////////////////////////user class///////////////////////////////////////
$('body').on('click','.userclass',function(){

  var id=$(this).attr('data-id');
  var name=$(this).attr('data-name');
  var email=$(this).attr('data-email');
  var img=$(this).attr('data-img');
  var from_id=$('#from_id').val();
  var to_id=$(this).attr('data-id');
  $('#to_id').val(to_id);
  var authname=  $('#authname').val();
  var name=$(this).attr('data-name');
  var img=$(this).attr('data-img');
  $('#to_name').val($(this).attr('data-name'));
  $('#img').val($(this).attr('data-img'));
  $(this).find('.right').removeClass('badge-success').text(' ');
  var headerhtml='<span class="chat-icon"><img class="img-fluid" src="https://mehedihtml.com/chatbox/assets/img/arroleftt.svg" alt="image title"></span><div class="flex-shrink-0"><img class="img-fluid" src="'+img+'" alt="user img" height="50px" width="50px"></div><div class="flex-grow-1 ms-3"><h3 id="nameid">'+name+'</h3></div>';
  $('.header').html(headerhtml);

  $('#send-container').removeAttr('style');
  $('#chat').html('');
  $('#email').val(email);

  appendchat(from_id,to_id)

  $('#message').focus();

})


//////////////append chat////////////////////////////

function appendchat(from_id,to_id){

  db.ref('Chats/'+from_id+'_'+to_id).off("child_added");
  db.ref('Chats/'+from_id+'_'+to_id).on("child_added", function(res){
  var messages='';
         if(res.val().message){
         messages='<p>'+res.val().message+'</p>';
         }

        if(res.val().imageUrl){
              var htmlTag;
              var url=res.val().imageUrl;
              var urlWithoutParams = url.split('?')[0];

              // Extract the file name from the URL without parameters
              var fileName = urlWithoutParams.substring(urlWithoutParams.lastIndexOf('/') + 1);

              // Extract the file extension from the file name
              var fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);

              switch (fileExtension) {
                case 'jpg':
                case 'jpeg':
                case 'png':
                  htmlTag = '<img src="' + res.val().imageUrl + '" alt="Preview Image" width="100px" height="100px">';
                  break;
                case 'pdf':
                  htmlTag = '<embed src="' + res.val().imageUrl + '" type="application/pdf" width="100px" height="100px">';
                  break;
                case 'txt':
                  htmlTag = '<iframe src="' + res.val().imageUrl + '" width="100px" height="100px"></iframe>';
                  break;
                default:
                  htmlTag = '<p>File type not supported for preview.</p>';
                  break;
              }

              var  msg='';
              if(from_id==res.val().from_userid){
              msg+='<li class=" repaly msg 1">'+messages+'<br>'+htmlTag+'<span class="time">'+getTimestamp(res.val().time)+'</span></li>';
              }else{
              msg+='<li class="sender msg cli 2">'+messages+'<br>'+htmlTag+'<span class="time">'+getTimestamp(res.val().time)+'</span></li>';
              }
       }else{

           var  msg='';
          if(from_id==res.val().from_userid){
           msg+=' <li class="repaly  msg 3">'+messages+'<span class="time">'+getTimestamp(res.val().time)+'</span> </li>';
           }else{
           msg+='<li class=" sender msg cli 4">'+messages+'<span class="time">'+getTimestamp(res.val().time)+'</span></li>';
           }

       }


         $('#chat').append(msg);
         $('#chat').scrollTop($('#chat')[0].scrollHeight);


     });
}
////////////////////////////////append image/////////////////////////////////////
function pushfilemsg(message,from_id,to_id,date,imageUrl,from_img,img,fileExtension){

  var p={
    message:message,
    from_userid:from_id,
    to_userid:to_id,
    time:date,
    imageUrl:imageUrl

  };

    dbRef.child('Chats/'+from_id+'_'+to_id+'/').push(p);
    dbRef.child('Chats/'+to_id+'_'+from_id+'/').push(p);

    var htmlTag;
    switch (fileExtension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
        htmlTag = '<img src="' + imageUrl + '" alt="Preview Image" width="100px" height="100px">';
        break;
      case 'pdf':
        htmlTag = '<embed src="' + imageUrl + '" type="application/pdf" width="100px" height="100px">';
        break;
      case 'txt':
        htmlTag = '<iframe src="' + imageUrl + '" width="100px" height="100px"></iframe>';
        break;
      default:
        htmlTag = '<p>File type not supported for preview.</p>';
        break;
    }


    var messages = '';
    if (message) {
        messages = '<p>' + message + '</p>';
    }

    var msg = '<li class=" repaly msg">'+messages+'<br>'+htmlTag+'<span class="time">'+getTimestamp(date)+'</span></li>';

    // $('#chat').append(msg);
    $('#chat').scrollTop($('#chat')[0].scrollHeight);

}

function convertToLocal(utcDateString) {
  // Get the local time zone offset in minutes
  var utcDate = new Date(utcDateString);

  // Get the local time zone offset in minutes
  var localOffset = new Date().getTimezoneOffset();

  // Calculate the UTC timestamp by subtracting the local time zone offset
  var utcTimestamp = utcDate.getTime() - (localOffset * 60000);

  // Create a new Date object using the adjusted UTC timestamp
  var localDate = new Date(utcTimestamp);

  // Return the local Date object
  return localDate;
}
///////////////////////////////////////////time//////////////////////////////////
function getTimestamp(time) {

  var now = new Date();
  var messageTime = new Date(time); // Replace with the actual message send time

  var timeDifference = now - messageTime;

  var minutes = Math.floor(timeDifference / 60000); // 1 minute = 60000 milliseconds

  if (minutes < 1) {
    return 'Just now';
  } else if (minutes < 60) {
    // Show minutes if the message was sent within the last hour
    return minutes + ' min ago';
  } else {
    var hours = Math.floor(minutes / 60);
    if (hours < 24) {
      // Show hours if the message was sent within the last day
      return hours + ' hours ago';
    } else {
      // Show days if the message was sent more than a day ago
      var days = Math.floor(hours / 24);

      return days + ' days ago';

    }
  }
}




function convertUTCDateToLocalDate(date) {

    var newDate = new Date(date.getTime()+date.getTimezoneOffset()*60*1000);

    var offset = date.getTimezoneOffset() / 60;
    var hours = date.getHours();

    newDate.setHours(hours - offset);

    return newDate;
}

function convertToTimeZone(dateString, targetTimeZone) {
  // Remove unnecessary parts from the date string
  var cleanedDateString = dateString.replace(/GMT[+-]\d{4}.*$/, '');

  // Parse the cleaned date string into a Date object
  var date = new Date(cleanedDateString);

  // Get local time zone offset in minutes
  var localOffset = date.getTimezoneOffset();

  // Calculate target time zone offset in minutes
  var targetOffset = targetTimeZone * 60;

  // Calculate the difference between local time zone and target time zone
  var offsetDiff = (targetOffset - localOffset) * 60000;

  // Apply the offset difference to the date
  date.setTime(date.getTime() + offsetDiff);

  // Return the converted date
  return date;
}
/////////////////////////////////send message//////////////////////////////////////



$('body').on('click','#sendmsg',function(){

  var file = $('#file').prop('files')[0];
  var message=$('#message').val();
  var from_id=$('#from_id').val();
  var to_id=$('#to_id').val();
  var email=$('#email').val();
  var authname=$('#authname').val();
  var from_img=$('#from_img').val();
  var from_email=$('#from_email').val();
  var name=$('#to_name').val();
  var img=$('#img').val();

  const d = new Date();
  // var date=" "+d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate()+" "+d.getHours()+":"+d.getMinutes()+":"+d.getSeconds();
  var date = new Date().toISOString()


  var message=document.getElementById('message').value;


  if (file) {

    var fileName = file.name;
    var fileExtension = fileName.split('.').pop().toLowerCase();
    // Upload image to Firebase Storage
    var storageRef = firebase.storage().ref('files/' + file.name);

    var sto=storageRef.put(file);
    sto.on('state_changed',  function(snapshot) {
      // Handle progress
      var progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
      //$('.fileupload').text('Upload is ' + Math.round(progress) + '% done');
      $('#chat').scrollTop($('#chat')[0].scrollHeight);
      var iurl= APP_URL ;
      $('#chat').append('<li class="repaly msg last-appended loadingimg"><img src="'+iurl+'/load.gif" style="width: 120px; height: auto;"></li>');

    },
    function(error) {
      // Handle errors
      console.error('Error uploading file:', error);
    },
    function() {
        $('#chat li').each(function() {

            $('.loadingimg').remove();
        });

      $('.preview-container').html('');


    },)
    sto.then(function(snapshot) {
      // Get the image URL after upload

      snapshot.ref.getDownloadURL().then(function(imageUrl) {
        // Push the message and image URL to Firebase

        pushfilemsg(message,from_id,to_id,date,imageUrl,from_img,img,fileExtension)

         $('#file').val('');

         $('#file').val(null);
      });
    });


  } else {

    if(message){
      var p={
        message:message,
        from_userid:from_id,
        to_userid:to_id,
        time:date
      }

      firebase.database().ref().child('Chats/'+from_id+'_'+to_id+'/').push(p);
      firebase.database().ref().child('Chats/'+to_id+'_'+from_id+'/').push(p);

     // appendchat(from_id,to_id)
    }
   }


   function convertToUTC(localDate) {


    // Get the local time zone offset in minutes
    var localOffset = localDate.getTimezoneOffset();


    // Get the UTC timestamp by subtracting the local time zone offset
    var utcTimestamp = localDate.getTime() - (localOffset * 60000);

    // Create a new Date object using the UTC timestamp
    var utcDate = new Date(utcTimestamp);

    // Return the UTC Date object
    return utcDate;
}


  lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email)
  $('#chat').scrollTop($('#chat')[0].scrollHeight);

  $.ajaxSetup({
      headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });
  var url = APP_URL + '/' + 'sendMsgForAdmin';
    $.ajax({
      type: 'POST',
      url: url,
      data:$('#send-container').serialize(),
      success: function (data) {
      },
    })
    $('#message').val('');

  })



  //////////////////////last message update///////////////////////////////////////////////
  function lastmessageupdate(from_id,to_id,email,message,date,name,img,from_image,from_name,from_email){

    var newData={
        email: email,
        message:message,
        from_userid:from_id,
        to_userid:to_id,
        time:date,
        to_name:name,
        img:img,
        from_image:from_image,
        from_name:from_name,
        from_email:from_email
  };

  var dbRef = firebase.database().ref('last_message/'+to_id+'/');

 dbRef.child(from_id).once('value', function(snapshot) {
    if (snapshot.exists()) {
      // Key exists, update the data
      dbRef.child(from_id).update(newData, function(error) {
        if (error) {

        } else {

        }
      });
    } else {
      // Key does not exist, push the data with the custom key
      dbRef.child(from_id).set(newData, function(error) {
        if (error) {

        } else {

        }
      });
    }
  });

  }


  /////////////////////////////////////////keypress//////////////////////////////////


  $(document).ready(function() {
    $('#message').on('keypress', function(e) {
      // Check if the pressed key is Enter (key code 13)
      if (e.which === 13) {
          e.preventDefault();
          var file = $('#file').prop('files')[0];
          var message=$('#message').val();
          var from_id=$('#from_id').val();
          var to_id=$('#to_id').val();
          var email=$('#email').val();
          var authname=$('#authname').val();
          var from_img=$('#from_img').val();
          var from_email=$('#from_email').val();
          var name=$('#to_name').val();
          var img=$('#img').val();

          const d = new Date();
          // var date=" "+d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate()+" "+d.getHours()+":"+d.getMinutes()+":"+d.getSeconds();

          var date = new Date().toISOString()
          var message=document.getElementById('message').value;


          if (file) {

            var fileName = file.name;
            var fileExtension = fileName.split('.').pop().toLowerCase();
            // Upload image to Firebase Storage
            var storageRef = firebase.storage().ref('files/' + file.name);

            var sto=storageRef.put(file);
            sto.on('state_changed',  function(snapshot) {
              // Handle progress
              var progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
              //$('.fileupload').text('Upload is ' + Math.round(progress) + '% done');
              $('#chat').scrollTop($('#chat')[0].scrollHeight);

              var iurl= APP_URL ;
              $('#chat').append('<li class="repaly msg last-appended loadingimg"><img src="'+iurl+'/load.gif" style="width: 120px; height: auto;"></li>');

            },
            function(error) {
              // Handle errors
              console.error('Error uploading file:', error);
            },
            function() {
                $('#chat li').each(function() {

                    $('.loadingimg').remove();
                });

              $('.preview-container').html('');


            },)
            sto.then(function(snapshot) {
              // Get the image URL after upload

              snapshot.ref.getDownloadURL().then(function(imageUrl) {
                // Push the message and image URL to Firebase

                pushfilemsg(message,from_id,to_id,date,imageUrl,from_img,img,fileExtension)

                 $('#file').val('');

                 $('#file').val(null);
              });
            });


          } else {

            if(message){
              var p={
                message:message,
                from_userid:from_id,
                to_userid:to_id,
                time:date
              }

              firebase.database().ref().child('Chats/'+from_id+'_'+to_id+'/').push(p);
              firebase.database().ref().child('Chats/'+to_id+'_'+from_id+'/').push(p);

             // appendchat(from_id,to_id)
            }
           }




          lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email)
          $('#chat').scrollTop($('#chat')[0].scrollHeight);

          $.ajaxSetup({
              headers: {
                  'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
              }
            });
          var url = APP_URL + '/' + 'sendMsgForAdmin';
            $.ajax({
              type: 'POST',
              url: url,
              data:$('#send-container').serialize(),
              success: function (data) {
              },
            })
            $('#message').val('');
      }
    });
  });


  ////////////////////////////////search////////////////////////////////////////
  $('body').on('keypress keyup','#search',function(){
    var name = $('#search').val();
    if (name==null || name=="") {

        $('.chat-list a').each(function() {
            $(this).show();
            $(this).removeClass('displaynone')
        })
    }else{

       filterList(name);
    }

})

function filterList(searchText) {

    // Iterate through each li element in the list
    $('.chat-list a').each(function() {
      var currentItemText = $(this).find('h3').text().toLowerCase(); // Get the text of the current li in lowercase
      var currentItemTexts = $(this).find('h3').text();
      // Check if the current li contains the search query
      if (currentItemText.indexOf(searchText) !== -1 || currentItemTexts.indexOf(searchText) !== -1) {
        $(this).show(); // If it does, show the li
        $(this).removeClass('displaynone')
      }
      else {
        $(this).hide(); // If it doesn't, hide the li
        $(this).addClass('displaynone')
      }

    });

  }


  $('.fileattachment').click(function () {
    $('#file').trigger('click');
    $('#file').change(function () {
        var filename = $('#file').val();
        var fileInput = $(this)[0].files[0]; // Get the selected file
        var reader = new FileReader(); // Create a new FileReader object
        var fileName = fileInput.name;
        var fileExtension = fileName.split('.').pop().toLowerCase();
        reader.onload = function (e) {
            var htmlTag;
            switch (fileExtension) {
              case 'jpg':
              case 'jpeg':
              case 'png':
                htmlTag = '<img src="' + e.target.result + '" alt="Preview Image" width="100px" height="100px">';
                break;
              case 'pdf':
                htmlTag = '<embed src="' + e.target.result + '" type="application/pdf" width="100px" height="100px">';
                break;
              case 'txt':
                htmlTag = '<iframe src="' + e.target.result + '" width="100px" height="100px"></iframe>';
                break;
              default:
                htmlTag = '<p>File type not supported for preview.</p>';
                break;
            }
            // Create an image element to display the preview


            // Append the preview to a container element
            $('.preview-container').html(htmlTag);
        };

        // Read the file as a data URL
        reader.readAsDataURL(fileInput);

    });
});

$('body').on('click','#multisendmsg',function(){
  const val = [];
  const from_id=$('#from_id').val();
  const authname=$('#authname').val();
  const from_img=$('#from_img').val();
  const from_email=$('#from_email').val();
  const message_type=$('#message_type').val();
  const date = new Date().toISOString()
  const message=(document.getElementById('message').value || '').trim();
  if(!message) return

  $(':checkbox:checked').each(function(i) {
    val[i] = $(this).val();
    const to_id=val[i];
    const email=$('#email'+val[i]).val();
    const name=$('#to_name'+val[i]).val();
    const img=$('#img'+val[i]).val();
    const p = {
      message:message,
      from_userid:from_id,
      to_userid:to_id,
      time:date
    }
    firebase.database().ref().child('Chats/'+from_id+'_'+to_id+'/').push(p);
    firebase.database().ref().child('Chats/'+to_id+'_'+from_id+'/').push(p);
    lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email)
  });

  $.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  const url = APP_URL + '/' + 'sendMultiMsgForAdmin';
  $.ajax({
    type: 'POST',
    url: url,
    data:{id:val, message, message_type},
    dataType: "json",
    success: function (data) {
      if(data && data.success){
        $('#message').val('');
        alertify.success(data.message || 'Message Sent Successfully');
      } else {
        alertify.error(data.message || 'Something went wrong');
      }
    },
    error: function() {
      alertify.error('Error occurred while sending message');
    }
  });
})

$('body').on('click','#sendmsgOnboarding',function(){
  const val = [];
  const from_id=$('#from_id').val();
  const authname=$('#authname').val();
  const from_img=$('#from_img').val();
  const from_email=$('#from_email').val();
  const message_type=$('#message_type').val();
  const date = new Date().toISOString()
  const message=(document.getElementById('message').value || '').trim();
  if(!message) return

  $(':checkbox:checked').each(function(i) {
    val[i] = $(this).val();
    const to_id=val[i];
    const email=$('#email'+val[i]).val();
    const name=$('#to_name'+val[i]).val();
    const img=$('#img'+val[i]).val();
    const p = {
      message:message,
      from_userid:from_id,
      to_userid:to_id,
      time:date
    }
    firebase.database().ref().child('Chats/'+from_id+'_'+to_id+'/').push(p);
    firebase.database().ref().child('Chats/'+to_id+'_'+from_id+'/').push(p);
    lastmessageupdate(from_id,to_id,email,message,date,name,img,from_img,authname,from_email)
  });

  $.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  const url = APP_URL + '/' + 'admin/sendOnboardingMsgForAdmin';
  $.ajax({
    type: 'POST',
    url: url,
    data:{id:val, message, message_type},
    dataType: "json",
    success: function (data) {
      if(data && data.success){
        $('#message').val('');
        alertify.success(data.message || 'Message Sent Successfully');
      } else {
        alertify.error(data.message || 'Something went wrong');
      }
    },
    error: function() {
      alertify.error('Error occurred while sending message');
    }
  });
})
