// firebase.database().ref('/').on('value', (snapshot) => {
//     if (snapshot.exists()) {
//       const allData = snapshot.val(); // Fetch all data
//       console.log('Real-time data:', allData);
//     } else {
//       console.log('No data available');
//     }
//   });




function startTour(screenName) {

    const steps = getTourSteps(screenName);
    // if (window.location.pathname.split('/').pop().split('.')[0]?.replace(/-/g, "_") == 'post_requirements') {
    //     TourManager.startTour(screenName, steps, true);
    // } else {
        TourManager.startTour(screenName, steps, true);
    // }

    if (screenName == 'post_requirements_dashboard') {
        localStorage.setItem(`${screenName}_tour_started`, 'true');
    }
}

let activeTab = 1;
var segments = window.location.pathname.split('/');

if (segments[1] == 'post-requirements') {
    let url = "/school/tab-data?tab=requirement-overview";
    if (segments[2]) {
        url += `&id=${segments[2]}`; // Append id if present
    }

    handleTabSwitch(document.getElementById("tab_button_1"),url);
}

function initializeBudgetInitialize() {
    var subject_id = $('#sub_subject').val();

    var no_instrtructional_days = $('#no_instrtructional_days').val();
    var class_duration = $('#class_duration').val(); // in minutes
    var total_minutes = no_instrtructional_days * class_duration;
    var total_hours = total_minutes / 60;

    var provide_curriculum = $('.upload_certification_checkbox').is(':checked');
    var qualification = $('#qualification').val();
    var experience = $('#experience').val();

    if (subject_id) {
        $.ajax({
            url: '/school/budget/' + subject_id,
            type: 'GET',
            dataType: 'json',
            data: {
                total_hours: total_hours,
                provide_curriculum: provide_curriculum,
                qualification: qualification,
                experience: experience
            },
            success: function(response) {
                if (response.status) {
                    var budgets = response.budgets;

                    // Update labels and attach actual budget as data attribute
                    $('#experience option[value="3"]').text(`0-3 years of experience: $${budgets[3].toFixed(2)}`).data('budget', budgets[3]);
                    $('#experience option[value="6"]').text(`3-6 years of experience: $${budgets[6].toFixed(2)}`).data('budget', budgets[6]);
                    $('#experience option[value="10"]').text(`6-10 years of experience: $${budgets[10].toFixed(2)}`).data('budget', budgets[10]);
                    $('#experience option[value="11"]').text(`10+ years of experience: $${budgets[11].toFixed(2)}`).data('budget', budgets[11]);

                    $('.credential_div').show();
                    // Enable the select dropdown now that it's populated
                    $('#experience').prop('disabled', false);
                }
            },
            error: function(xhr) {
                console.log('Error fetching subject budget');
            }
        });
    }
}

function updateEndDate() {
    const days = parseInt($('#no_instrtructional_days').val());
    const startDateStr = $('#class_start_date').val();
    console.log(days, startDateStr);
    if (!isNaN(days) && startDateStr) {
        const parts = startDateStr.split('/');
        const startDate = new Date(parts[2], parts[0] - 1, parts[1]);

        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + days - 1); // Add full number of days

        const formattedEndDate = (endDate.getMonth() + 1).toString().padStart(2, '0') + '/' + endDate.getDate().toString().padStart(2, '0') + '/' + endDate.getFullYear();
        $('#class_end_date').val(formattedEndDate);
    }
}

function handleTabSwitch(button, url)
{
    // const replaceHtmlId = e.dataset.bsTarget;
    const replaceHtmlId = button.dataset.bsTarget || `#details_tab_${activeTab}`;
    let selectedTab = $(replaceHtmlId);
    if(!selectedTab.length) {
        $.ajax({
            url: url,
            method: "GET",
            dataType: "json",
            success: function (res) {
                if (res.status == 1) {
                    const selectedTab = document.createElement('div');
                    selectedTab.id = replaceHtmlId.replace('#', '');
                    selectedTab.innerHTML = res.view;
                    selectedTab.role="tabpanel"
                    selectedTab.classList.add('tab-pane')
                    selectedTab.classList.add('fade')
                    document.getElementById('myTabContent').appendChild(selectedTab)
                    updateView(selectedTab);

                }
            }
        })
    } else
    {
        updateView(selectedTab[0]);
    }
}

function initializeNewSchoolSelect2()
{
    $(".deliveryMode").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select delivery mode',
                minimumInputLength: 0,
                allowClear: true,
                minimumResultsForSearch: Infinity,
                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });

    $(".classType").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select class type',
                minimumInputLength: 0,
                allowClear: true,
                minimumResultsForSearch: Infinity,
                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });

    $(".subjectArea").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select subject area',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });

    $(".sub_subject").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select subject',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });

    $(".gradeLevels").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select grade level',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });


    $(".profilType").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select profile type',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });

    $(".language").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select language',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });

    $(".requirementState").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select certified/licensed in',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });
}

function toggleSpecialEducationSection() {
    const isNonCredentialed = $('.non_credentialed').is(':checked');
    const section = $('#special-education-section');

    if (isNonCredentialed) {
        section.addClass('disabled-section');
        section.find('input').prop('disabled', true);
    } else {
        section.removeClass('disabled-section');
        section.find('input').prop('disabled', false);
    }
}
function toggleBudgetFields() {
    if ($(".credentialed:checked").length > 0) {
        console.log('credentialed');
        $(".credential_div").removeClass("d-none");
        $(".non_credentialed_div").addClass("d-none");
    } else if ($(".non_credentialed:checked").length > 0) {
        console.log('non credentialed');
        $(".non_credentialed_div").removeClass("d-none");
        $(".credential_div").addClass("d-none");
    }
}

function updateView(selectedTab)
{
    const parent = document.getElementById('myTabContent');
    Array.from(parent.children).forEach(element => {
      element.classList.remove("active", "show");
      $(`#tab_button_${activeTab - 1}`).removeClass('active');
    });

    selectedTab.classList.add("active", "show");
    $(`#tab_button_${activeTab}`).addClass("active");
    initializeSelect2();
    initializeNewSchoolSelect2();
    initializeTimepicker();
    initializeDaterangepicker();
    initializeBootstrapDatepicker();
    drawTables()

    if (activeTab == 1) {
        initializeOverviewValidation();
        initAutocompletemapPost();
        $('html, body').animate({ scrollTop: 0 }, 200);
    }

    if (activeTab == 2) {
        if(!$(selectedTab.children[1].children[0]).find('.regularSchedule').hasClass('d-none')){
            updateSelectedDays('regular-schedule');
        } else if(!$(selectedTab.children[1].children[0]).find('alternatingSchedule').hasClass('d-none')) {
            updateSelectedDays('alternating-schedule-1');
            updateSelectedDays('alternating-schedule-2');
        }

        initializeScheduleValidation();
        $('.schedule-container .row').each(function (index) {
            document.querySelectorAll('input.stop-page-scroll').forEach(function (input) {
                input.addEventListener('wheel', function (e) {
                    e.preventDefault(); // Block input value from changing
                }, { passive: false }); // Required to make preventDefault work
            });
            applyScheduleValidation(index);
        });

        $('.no-class-dates-container .no_class_dates_field').each(function (index) {
            applyNoClassDateValidation(index);
        });

        $('#no_instrtructional_days, #class_start_date').on('input change', function () {
            updateEndDate();
        });
        $('.calendar-icon').on('click', function() {
            $(this).siblings('input.schedule-datepicker').focus();
        });
        $('html, body').animate({ scrollTop: 0 }, 200);
    }

    if (activeTab == 3) {
        initializeEducatorsValidation();
        $('html, body').animate({ scrollTop: 0 }, 200);

        toggleSpecialEducationSection();
        $('input[name="credential_type"]').on('change', function () {
            toggleSpecialEducationSection();
        });
    }

    if (activeTab == 4) {
        initializeBudgetValidation();
        initializeBudgetInitialize();

        const qualificationVal = $('#qualification').val();
        const experienceVal = $('#experience').val();

        $('#experience').prop('disabled', true);
        if (qualificationVal == 'undefined' || qualificationVal == 'undefined') {
            $('#experience').prop('disabled', true);
        } else{
            $('#experience').prop('disabled', false);
        }
        $('#qualification').on('change', function () {
            const updatedQualificationVal = $(this).val();
            if (updatedQualificationVal) {
                $('#experience').prop('disabled', false);
            } else {
                $('#experience').prop('disabled', true).val(''); // Optionally clear it when disabling
            }
            initializeBudgetInitialize();
        });
        $('#no_instrtructional_days, #class_duration, #sub_subject, #experience, .upload_certification_checkbox').on('change', function () {
            initializeBudgetInitialize();
        });
        $(document).on('change', '#experience', function () {
            var budget = $(this).find('option:selected').data('budget');
            $('#total_budget').val(budget || '');
        });
        $('html, body').animate({ scrollTop: 0 }, 200);

        toggleBudgetFields();
        $('input[name="credential_type"]').on('change', function () {
            toggleBudgetFields();
        });
    }

    $("#prevTabButton").toggleClass("d-none", activeTab === 1);
    // $("#nextTabButton").toggleClass("d-none", selectedTab.id === "details_tab_4");

    // activeTab == 1 ? $('#saveButton').addClass('d-none') : $('#saveButton').removeClass('d-none');
    activeTab == 4 ? $('#nextTabButton').text('Post Class') : $('#nextTabButton').text('Save and Continue')
    activeTab == 4 ? $('#saveButton').text('Save') : $('#saveButton').text('Save as draft')
    if ($('#deliveryMode').val() == 'in-person' || $('#deliveryMode').val() == 'hybrid') {
        $('.medical-container').removeClass('d-none');
    } else {
        $('.medical-container').addClass('d-none');
    }
}

function initializeSelect2() {
    $(".select22").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            })
            .on("select2:open", function () {
                console.log('hello');
                document.querySelector('.select2-container--open .select2-search__field')?.setAttribute('placeholder', 'Search');
            });
    });
    $(".selectTwo").select2();
    // $('.multiselect').multiselect({
    //     enableResetButton: true,
    //     resetButtonText: 'Clear',
    // });
}

function initializeTimepicker() {
    $('.timepicker').timepicker(
        {
            'timeFormat': 'h:i A',
            'step': 1,
            'listWidth': 1,
        }
    ).attr('placeholder', 'HH:MM AM/PM');


    $('.timepicker').bind('timeFormatError', function () {
        $(this).val('');
    });


    $('.av-timepicker').timepicker(
        {
            'timeFormat': 'h:i A',
            'step': 1,
            'listWidth': 1,
        }
    ).attr('placeholder', 'HH:MM AM/PM');


    $('.av-timepicker').bind('timeFormatError', function () {
        $(this).val('');
    });
}

function initializeDaterangepicker() {
    $('.daterangeElement').daterangepicker({
        locale: {
            format: 'MM-DD-YYYY',
            separator: " TO "
        },

        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'This Week': [moment().startOf('week'), moment().endOf('week')],
            'Last Week': [moment().subtract(1, 'week').startOf('week'), moment().subtract(1, 'week').endOf('week')],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'This Year': [moment().startOf('year'), moment().endOf('year')],
            'Last Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
        },
    }).on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
    });

    $(".daterangeElement").each(function () {
        const attributeValue = $(this).attr('value');
        if (attributeValue === '') {
            $(this).trigger('cancel.daterangepicker');
        }
    });

}

function initializeBootstrapDatepicker() {

    $(".av-datepickerfilter").each(function () {
        var $datepicker = $(this);

        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $datepicker.datepicker({
            format: "mm/dd/yyyy",
            autoclose: true,
        })
            .on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });

    });
}

function drawTables() {
    if (typeof dataTable !== 'undefined') {
        dataTable.draw();
    }
    if (typeof dataTable2 !== 'undefined') {
        dataTable2.draw();
    }
}

function initializeOverviewValidation() {
    let validator = $("#overviewpost").validate({
        rules: {
            requirementType: {
                required: true,
            },
            schoolName: {
                required: true,
            },
            classType: {
                required: true,
            },
            "deliveryMode[]": {
                required: true,
            },
            sub_subject: {
                required: true,
            },
            "gradeLevels[]": {
                required: true,
            },
            numberOfStudents: {
                required: true,
                digits: true,
                min: 1,
            },
            requirementDescription: {
                // required: true,
                minlength: 50
            },
            address: {
                required: function() {
                    return $("#deliveryMode").val() === "in-person"; // Only required if deliveryMode is "in-person"
                },
            },
            city: {
                required: function() {
                    return $("#deliveryMode").val() === "in-person"; // Only required if deliveryMode is "in-person"
                },
            },
            state: {
                required: function() {
                    return $("#deliveryMode").val() === "in-person"; // Only required if deliveryMode is "in-person"
                },
            }
        },
        messages: {
            requirementType: "Please select a requirement type.",
            schoolName: "Please select a school.",
            classType: "Please select a class type.",
            deliveryMode: "Please select a delivery mode.",
            subjectArea: "Please select a subject area.",
            sub_subject: "Please select a subject.",
            "gradeLevels[]": "Please select a grade level.",
            numberOfStudents: {
                required: "Please enter the number of students.",
                digits: "Only numeric values are allowed.",
                min: "Number of students must be at least 1.",
            },
            requirementDescription: {
                required: "Please provide a description.",
                minlength: "Description must be at least 50 characters long.",
            },
            address: "Please provide the address for in-person delivery mode.",
        },
        errorClass: "is-invalid", // Adds a class to highlight the field
        validClass: "is-valid",  // Adds a class to mark the field as valid
        errorPlacement: function (error, element) {
            // if (element.attr("name") === "requirementType") {
            //     error.insertAfter(element.closest('.form-check-inline').parent());
            // } else if (element.hasClass('select22')) {
            //     // Place the error message under the select2 container
            //     error.insertAfter(element.next('.select2-container'));
            // } else {
            //     error.insertAfter(element);
            // }
        },
        highlight: function (element) {
            $(element).addClass("is-invalid").removeClass("is-valid");
            if ($(element).attr("name") === "requirementType") {
                $('input[name="requirementType"]').addClass("is-invalid").removeClass("is-valid");
            }
            if ($(element).attr("city") === "city") {
                $('input[name="address"]').addClass("is-invalid").removeClass("is-valid");
            }
            if ($(element).hasClass("select2-hidden-accessible")) {
                $(element).next(".select2-container--bootstrap4")
                        .find(".select2-selection")
                        .addClass("is-invalid").removeClass("is-valid");
            }
        },
        unhighlight: function (element) {
            $(element).removeClass("is-invalid").addClass("is-valid");
            if ($(element).attr("name") === "requirementType") {
                $('input[name="requirementType"]').removeClass("is-invalid").addClass("is-valid");
            }
            if ($(element).attr("city") === "city") {
                $('input[name="address"]').removeClass("is-invalid").addClass("is-valid");
            }
            if ($(element).hasClass("select2-hidden-accessible")) {
                $(element).next(".select2-container--bootstrap4")
                        .find(".select2-selection")
                        .removeClass("is-invalid").addClass("is-valid");
            }
        },
        invalidHandler: function (event, validator) {
            let errors = validator.numberOfInvalids();
            if (errors) {
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
                // Create a common error message
                let message = `<div class="alert alert-danger">
                    Please fix the errors in the form and try again.
                </div>`;
                // Append it to the top of the form if not already present
                if (!$('#overviewpost .alert').length) {
                    $("#overviewpost").prepend(message);
                }
            }
        },
        submitHandler: function (form) {
            // Perform form submission
            alert("Form is valid and ready for submission!");
            return false; // Prevent default form submission for testing
        },
    });
    $('#overviewpost').on('input change', 'input, select, textarea', function() {
        validator.element(this);
    });
}

function initializeScheduleValidation()
{
    $("#schedulepost").validate({
        rules: {
            class_start_date: {
                required: true,
                date: true
            },
            class_end_date: {
                required: true,
                date: true,
                greaterThanStartDate: "#class_start_date" // Custom validation rule to ensure class end date is after start date
            },
            no_instrtructional_days: {
                required: true,
                number: true
            },
            class_duration: {
                required: true,
                number: true
            },
            no_non_instructional_hr: {
                required: true,
                number: true
            },
            "regular_days": {
                required: function() {
                    return $("#regular").is(":checked");
                },
            },
            "schedule_1_days": {
                required: function() {
                    return $("#alternating").is(":checked");
                },
            },
            "schedule_2_days": {
                required: function() {
                    return $("#alternating").is(":checked");
                },
            },
            "schedule_start_time": {
                required: function() {
                    return $("#regular").is(":checked");
                },
            },
            "schedule_end_time": {
                required: function() {
                    return $("#regular").is(":checked");
                },
                greaterThanStartTime: "#schedule_start_time"
            },
            "schedule_1_start_time": {
                required: function() {
                    return $("#alternating").is(":checked");
                },
            },
            "schedule_1_end_time": {
                required: function() {
                    return $("#alternating").is(":checked");
                },
                greaterThanStartTime: "#schedule_1_start_time"
            },
            "schedule_2_start_time": {
                required: function() {
                    return $("#alternating").is(":checked");
                },
            },
            "schedule_2_end_time": {
                required: function() {
                    return $("#alternating").is(":checked");
                },
                greaterThanStartTime: "#schedule_2_start_time"
            },
            other_sch_class_details: {
                required: function() {
                    return $("#other").is(":checked");
                },
            },
            teacher_schedule_screenshot: {
                required: function() {
                    return $("#other").is(":checked");
                },
            },

        },
        messages: {
            class_start_date: {
                required: "Class start date is required",
                date: "Please enter a valid date"
            },
            class_end_date: {
                required: "Class end date is required",
                date: "Please enter a valid date",
                greaterThanStartDate: "End date must be after the start date"
            },
            no_instrtructional_days: {
                required: "Please enter the number of non-instructional days",
                number: "Please enter a valid number" // Custom error message for number validation
            },
            class_duration: {
                required: "Please enter the class duration",
                number: "Please enter a valid number" // Custom error message for number validation
            },
            no_non_instructional_hr: {
                required: "Please enter the number of non-instructional hours",
                number: "Please enter a valid number" // Custom error message for number validation
            },
        },
        errorClass: "is-invalid", // Adds a class to highlight the field
        validClass: "is-valid",  // Adds a class to mark the field as valid
        errorPlacement: function(error, element) {
            // if (element.hasClass('select22')) {
            //     error.insertAfter(element.next('.select2-container'));
            // } else if (element.closest('.no-class-dates-container').length) {
            //     error.appendTo(element.closest('.no-class-dates-container'));
            // } else {
            //     error.insertAfter(element);
            // }
        },
        highlight: function (element) {
            $(element).addClass("is-invalid").removeClass("is-valid"); // Highlight invalid fields
            if ($(element).hasClass("select2-hidden-accessible")) {
                $(element).next(".select2-container--bootstrap4").find(".select2-selection").addClass("is-invalid").removeClass("is-valid");
                $(element).next(".select2-container").find(".select2-selection").addClass("is-invalid").removeClass("is-valid");
            }
        },
        unhighlight: function (element) {
            $(element).removeClass("is-invalid").addClass("is-valid"); // Remove highlight for valid fields
            if ($(element).hasClass("select2-hidden-accessible")) {
                $(element).next(".select2-container--bootstrap4").find(".select2-selection").removeClass("is-invalid").addClass("is-valid");
                $(element).next(".select2-container").find(".select2-selection").removeClass("is-invalid").addClass("is-valid");
            }
        },
        invalidHandler: function (event, validator) {
            let errors = validator.numberOfInvalids();
            console.log(errors);
            let errorFields = validator.errorList;
            console.log(errorFields);
            if (errors) {
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
                // Create a common error message
                let message = `<div class="alert alert-danger">
                    Please fix the errors in the form and try again.
                </div>`;
                // Append it to the top of the form if not already present
                if (!$('#schedulepost .alert').length) {
                    $("#schedulepost").prepend(message);
                }
            }
        },
    });

    // Custom validation rule to check that the end date is after the start date
    $.validator.addMethod("greaterThanStartDate", function(value, element, param) {
        var startDateStr = $(param).val();
        var endDateStr = value;
        var instructionalDays = parseInt($('#no_instrtructional_days').val());

        if (!startDateStr || !endDateStr || isNaN(instructionalDays)) {
            return true; // Skip validation if fields are not properly filled yet
        }

        function parseDate(dateStr) {
            let parts = dateStr.split('/');
            return new Date(parts[2], parts[0] - 1, parts[1]);
        }

        let startDate = parseDate(startDateStr);
        let endDate = parseDate(endDateStr);

        // Calculate the minimum allowed end date
        let expectedEndDate = new Date(startDate);
        expectedEndDate.setDate(startDate.getDate() + instructionalDays - 1);

        return endDate >= expectedEndDate;
    }, "End date must be at least the number of instructional days after the start date");

    // Custom validation rule to check that end time is after start time
    $.validator.addMethod("greaterThanStartTime", function(value, element, param) {
        var startTime = $(param).val();
        function convertTo24Hour(time) {
            var timeParts = time.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
            if (timeParts) {
                var hours = parseInt(timeParts[1]);
                var minutes = timeParts[2];
                var period = timeParts[3];

                if (period.toUpperCase() === 'PM' && hours < 12) {
                    hours += 12;
                } else if (period.toUpperCase() === 'AM' && hours === 12) {
                    hours = 0;
                }

                return ('0' + hours).slice(-2) + ':' + minutes;
            }
            return time; // If the format doesn't match, return as is
        }

        var start24HourTime = convertTo24Hour(startTime);
        var end24HourTime = convertTo24Hour(value);

        var start = new Date("1970-01-01T" + start24HourTime + ":00");
        var end = new Date("1970-01-01T" + end24HourTime + ":00");

        return end > start;
    }, "End time must be after start time");

    $("#schedulepost").find("input, select, textarea").on("input change", function () {
        var validator = $("#schedulepost").validate();
        validator.element(this);  // re-validate current element to update classes/errors immediately
    });
    $('.timepicker-icon').on('click', function () {
        $(this).closest('.position-relative').find('input').focus(); // Trigger input focus
    });
}

function initializeEducatorsValidation()
{
    $('#educatorspost').validate({
        rules: {
            "will_choose_requiremnt": {
                required: true,
            },
            "credential_type": {
                required: true,
            },
            "special_education": {
                required: true,
            },
            "language_requirements[]": {
                required: true,
            },
            other_requirements: {
                maxlength: 250
            }
        },
        messages: {
            profileType_requirements: {
                required: "Please select profile type.",
            },
            "language_requirements[]": {
                required: "Please select language.",
            },
            other_requirements: {
                required: "Please specify any other requirements.",
                maxlength: "The requirements should not exceed 255 characters."
            },
            'states[]': {
                required: "Please select at least one state.",
                minlength: "Please select at least one state."
            }
        },
        errorClass: "is-invalid", // Adds a class to highlight the field
        validClass: "is-valid",  // Adds a class to mark the field as valid
        errorPlacement: function(error, element) {
            // if (element.attr("name") == "states[]") {
            //     error.insertAfter("#states");
            // } else {
            //     error.insertAfter(element);
            // }
        },
        highlight: function (element) {
            $(element).addClass("is-invalid").removeClass("is-valid"); // Highlight invalid fields
            if (element.type === "radio") {
                $("input[name='" + element.name + "']").each(function () {
                    $(this).addClass("is-invalid").removeClass("is-valid");
                });
            }
            if ($(element).hasClass("select2-hidden-accessible")) {
                $(element).next(".select2-container--bootstrap4").find(".select2-selection").addClass("is-invalid").removeClass("is-valid");
            }
        },
        unhighlight: function (element) {
            $(element).removeClass("is-invalid").addClass("is-valid"); // Remove highlight for valid fields
            if (element.type === "radio") {
                $("input[name='" + element.name + "']").each(function () {
                    $(this).removeClass("is-invalid").addClass("is-valid");
                });
            }
        },
        invalidHandler: function (event, validator) {
            let errors = validator.numberOfInvalids();
            if (errors) {
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
                // Create a common error message
                let message = `<div class="alert alert-danger">
                    Please fix the errors in the form and try again.
                </div>`;
                // Append it to the top of the form if not already present
                if (!$('#educatorspost .alert').length) {
                    $("#educatorspost").prepend(message);
                }
            }
        },
        submitHandler: function (form) {
            // Perform form submission
            alert("Form is valid and ready for submission!");
            return false; // Prevent default form submission for testing
        },
    });

    $('#educatorspost').find('input, select, textarea').on('input change', function() {
        var validator = $('#educatorspost').validate();
        validator.element(this);  // validate just this element immediately
    });
    $('select[name="language_requirements[]"]').on('change', function() {
        var validator = $('#educatorspost').validate();
        // Validate entire field (array)
        validator.element($(this));
    });
}

function initializeBudgetValidation()
{
    console.log($("#max").val());
    console.log($("#min").val());
    $('#budgetpost').validate({
        rules: {
            qualification: {
                required: true
            },
            experience: {
                required: true
            },
            benefits: {
                required: false,
                maxlength: 500
            }
        },
        messages: {
            qualification: {
                required: "Please select a qualification."
            },
            experience: {
                required: "Please select experience."
            },
            benefits: {
                maxlength: "Benefits description cannot exceed 500 characters."
            }
        },
        errorClass: "is-invalid", // Adds a class to highlight the field
        validClass: "is-valid",  // Adds a class to mark the field as valid
        errorPlacement: function(error, element) {
            // if (element.hasClass('select22')) {
            //     // Place the error message under the select2 container
            //     error.insertAfter(element.next('.select2-container'));
            // } else {
            //     error.insertAfter(element);
            // }
        },
        highlight: function (element) {
            $(element).addClass("is-invalid").removeClass("is-valid");
            if ($(element).is('select')) {
                $(element).closest('.budget-select').addClass('has-error');
            }
        },
        unhighlight: function (element) {
            $(element).removeClass("is-invalid").addClass("is-valid");
            if ($(element).is('select')) {
                $(element).closest('.budget-select').removeClass('has-error');
            }
        },
        invalidHandler: function (event, validator) {
            let errors = validator.numberOfInvalids();
            if (errors) {
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
                // Create a common error message
                let message = `<div class="alert alert-danger">Please fix the errors in the form and try again.</div>`;
                // Append it to the top of the form if not already present
                if (!$('#budgetpost .alert').length) {
                    $("#budgetpost").prepend(message);
                }
            } else {
                $('#budgetpost .alert').remove();
            }
        },
        submitHandler: function(form) {
            $('#budgetpost .alert').remove();
            form.submit();
        }
    });

    $('#budgetpost').find('select, textarea').on('change input', function() {
        var validator = $('#budgetpost').validate();
        validator.element(this);
    });

    // Custom validation method to check if max value is greater than min value
    $.validator.addMethod("greaterThanMin", function(value, element) {
        var minValue = $("#min").val();
        value = value.replace(/[^0-9.]/g, '');
        minValue = minValue.replace(/[^0-9.]/g, '');

        return parseFloat(value) > parseFloat(minValue);
    }, "Maximum value must be greater than the minimum value.");
}

function continueToTab(_this, isNext = true, skipCheck = false)
{
    if (isNext) {
        if (activeTab <= 6) {
            const switchTabs = () => {
                activeTab = Math.min(activeTab + 1, 4);
                const tabNames = ["requirement-overview", "schedule", "educators-requirements", "budget"];
                const routeUrlBase = APP_URL + "/school/tab-data?tab=" + tabNames[activeTab - 1];
                const segments = window.location.pathname.split('/');
                const routeUrl = segments[2]
                    ? routeUrlBase + `&id=${segments[2]}`
                    : routeUrlBase;

                handleTabSwitch(document.getElementById(`tab_button_${activeTab}`), routeUrl);
            }

            if(skipCheck) {
                return switchTabs();
            }

            switch (activeTab) {
                case 1:
                    const overviewpostIsValid = $("#overviewpost").valid();
                    if (overviewpostIsValid) {
                        if (!Overview(_this, switchTabs)) {
                            return;  // Stop the process if validation fails
                        }
                    }
                    break;
                case 2:
                    const schedulepostIsValid = $("#schedulepost").valid();
                    if (schedulepostIsValid) {
                        if (!schedule(_this, switchTabs)) {
                            return;  // Stop the process if validation fails
                        }
                    }
                    break;
                case 3:
                    const educatorsRequirementIsValid = $("#educatorspost").valid();
                    if (educatorsRequirementIsValid) {
                        if (!educatorsRequirement(_this, switchTabs)) {
                            return;  // Stop the process if validation fails
                        }
                    }
                    break;
                case 4:
                    const budgetpostIsValid = $("#budgetpost").valid();
                    if (budgetpostIsValid) {
                        if (!budget(_this, switchTabs)) {
                            return;  // Stop the process if validation fails
                        }
                    }
                    break;

                default:
                    break;
            }
        }
    } else {
        activeTab = Math.min(activeTab - 1, 4);
        $(`#tab_button_${activeTab + 1}`).removeClass('active');
        switch (activeTab) {
            case 1:
                $('#overviewpost .alert.alert-danger').remove();
                break;

            case 2:
                $('#schedulepost .alert.alert-danger').remove();
                break;

            case 3:
                $('#educatorspost .alert.alert-danger').remove();
                break;

            case 4:
                $('#budgetpost .alert.alert-danger').remove();
                break;

            default:
                break;
        }
        const tabNames = ["requirement-overview", "schedule", "educators-requirements", "budget"];
        const routeUrlBase = APP_URL + "/school/tab-data?tab=" + tabNames[activeTab - 1];
        const segments = window.location.pathname.split('/');
        const routeUrl = segments[2]
            ? routeUrlBase + `&id=${segments[2]}`
            : routeUrlBase;
        handleTabSwitch(document.getElementById(`tab_button_${activeTab}`), routeUrl);
    }

    activeTab === 1 ? $('#prevTabButton').addClass('d-none') : $('#prevTabButton').removeClass('d-none');
    activeTab === 1 || activeTab === 2 || activeTab === 3 ? $('#postButtonfinal').text('Save and Continue') : $('#postButtonfinal').text('Post Class')
}

function storeData(url)
{
    let formData = '';
    let isValid = '';
    switch (activeTab) {
        case 1:
            isValid = $("#overviewpost").valid();
            formData = $('#overviewpost').serialize() + '&id=' + $('#id').val()
            break;

        case 2:
            // isValid = $("#schedulepost").valid();
            formData = $('#schedulepost').serialize() + '&id=' + $('#id').val()
            break;


        case 3:
            // isValid = $("#educatorspost").valid();
            formData = $('#educatorspost').serialize() + '&id=' + $('#id').val()
            break;


        case 4:
            // isValid = $("#budgetpost").valid();
            formData = $('#budgetpost').serialize() + '&id=' + $('#id').val()
            break;

        default:
            break;
    }

    // if (isValid) {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            dataType: "json",
            success: function(response) {
                if (response.success == true) {
                    $('#id').val(response.id);
                    alertify.success('Stored Successfully');
                }
            },
            error: function(xhr, status, error) {
                alertify.error("An error occurred. Please try again.");
            }
        });
    // }
}

function Overview(_this, callback)
{
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/store-post-requirements';
    var th = _this;
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#overviewpost').serialize() + '&id=' + $('#id').val(),
        dataType: "json",
        beforeSend: function () {
            th.innerHTML = loading;
        },
        success: function (data) {
            if(typeof callback != 'undefined') {
                callback(data)
            }
            if (data.success == true) {
              $('#id').val(data.id);
              th.innerHTML = 'Save and Continue';
              alertify.success('Successfully Saved');
            } else {
                th.innerHTML = 'Save and Continue';
            }
        },
        error: function(xhr, status, error) {
            alertify.error("An error occurred. Please try again.");
            th.innerHTML = 'Save and Continue';
        }
    })
}

function schedule(_this, callback)
{
    // var startDate = $('#class_start_date').val();
    // var endDate = $('#class_end_date').val();
    // if (!startDate || !endDate) return;

    // var noClassDates = $('.no_class_dates').map(function () {
    //     return $(this).val();
    // }).get() || [];

    // var schedules = $('.schedule').map(function () {
    //     return $(this).val();
    // }).get() || [];

    // var scheduleStartTimes = $('.schedule_start_time').map(function () {
    //     return $(this).val();
    // }).get() || [];

    // var scheduleEndTimes = $('.schedule_end_time').map(function () {
    //     return $(this).val();
    // }).get() || [];

    // // ✅ Get only the valid dates that match the schedule (e.g., Monday)
    // var validDates = getValidDates(startDate, endDate, noClassDates, schedules, scheduleStartTimes, scheduleEndTimes);

    // var totalMinutes = 0;
    // validDates.forEach(dateInfo => {
    //     if (dateInfo.startTime && dateInfo.endTime) {
    //         totalMinutes += getTimeDifferenceInMinutes(dateInfo.startTime, dateInfo.endTime);
    //     }
    // });

    // console.log({
    //     startDate,
    //     endDate,
    //     noClassDates,
    //     schedules,
    //     scheduleStartTimes,
    //     scheduleEndTimes,
    //     validDates,
    //     totalMinutes
    // });
    // var hours = Math.floor(totalMinutes / 60);

    let formData = new FormData($('#schedulepost')[0]);
    formData.append('id', $('#id').val());
    let schoolfileInput = $('#sch_cal_screenshot')[0];
    let districtfileInput = $('#district_cal_screenshot')[0];
    let schedulefileInput = $('#teacher_schedule_screenshot')[0];
    if (schoolfileInput.files.length > 0) {
        formData.append('sch_cal_screenshot', schoolfileInput.files[0]);
    }

    if (districtfileInput.files.length > 0) {
        formData.append('district_cal_screenshot', districtfileInput.files[0]);
    }

    if (schedulefileInput.files.length > 0) {
        formData.append('teacher_schedule_screenshot', schedulefileInput.files[0]);
    }

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var url = APP_URL + '/store-post-requirements';
    var th = _this;
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        beforeSend: function () {
            th.innerHTML = loading;
        },
        success: function (data) {
            if(typeof callback != 'undefined') {
                callback(data)
            }
            if (data.success == true) {
              $('#id').val(data.id);
              th.innerHTML = 'Save and Continue';
              alertify.success('Successfully Saved');
            } else {
                th.innerHTML = 'Save and Continue';
            }
        },
        error: function(xhr, status, error) {
            alertify.error("An error occurred. Please try again.");
            th.innerHTML = 'Save and Continue';
        }
    })
}

$('body').on('change', '#profileType_requirements', function() {
    let profile = $(this).val();
    if (profile === '') {
        $('#states').prop('disabled', true);
        $('.states-container').css('opacity', '0.5');
    } else {
        $('#states').prop('disabled', false);
        $('.states-container').css('opacity', '1');
    }
});

function educatorsRequirement(_this, callback)
{

    // const checkboxes = $(".upload_certification_checkbox");
    // const firstCheckbox = checkboxes[0]; // This is a native DOM element

    // // Check if at least one is checked
    // const oneChecked = checkboxes.toArray().some(cb => cb.checked);

    // if (!oneChecked) {
    //     $(firstCheckbox).addClass("error_checkbox"); // ✅ Add class with jQuery
    //     return;
    // } else {
    //     $(firstCheckbox).removeClass("error_checkbox"); // ✅ Clean up if valid
    // }
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    var url = APP_URL + '/store-post-requirements';
    var th = _this;
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#educatorspost').serialize() + '&id=' + $('#id').val(),
        dataType: "json",
        beforeSend: function () {
            th.innerHTML = loading;
        },
        success: function (data) {
            if(typeof callback != 'undefined') {
                callback(data)
            }
            if (data.success == true) {
              $('#id').val(data.id);
              th.innerHTML = 'Post Class';
              $("#nextTabButton").hide();
              $("#postButtonfinal").removeClass('d-none').show(); // Remove d-none class and show the button
              alertify.success('Successfully Saved');
            } else {
                th.innerHTML = 'Save and Continue';
            }
        },
        error: function(xhr, status, error) {


            alertify.error("An error occurred. Please try again.");
            th.innerHTML = 'Save and Continue';

        }
    })
}

function budget(_this, callback)
{
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    var url = APP_URL + '/store-post-requirements';
    var th = _this;
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#budgetpost').serialize() + '&id=' + $('#id').val() + '&status=open',
        dataType: "json",
        beforeSend: function () {
            th.innerHTML = loading;
        },
        success: function (data) {
            if(typeof callback != 'undefined') {
                callback(data)
            }
            if (data.success == true) {
              $('#id').val(data.id);
              alertify.success('Successfully Saved');
              setTimeout(() => {
              location.href = APP_URL + '/all-requirements';
            }, 1000);
              th.innerHTML = 'Post Class';
            } else {
                th.innerHTML = 'Post Class';
            }
        },
        error: function(xhr, status, error) {
            alertify.error("An error occurred. Please try again.");
            th.innerHTML = 'Post Class';
        }
    })
}

function updatePostrequirement(url, id) {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        method: 'POST',
        data: {
            status: 'open',
            id: id
        },
        dataType: "json",
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                alertify.error(response.message);
            }
        },
        error: function (xhr, status, error) {
            alertify.error("An error occurred. Please try again.");
        }
    });

}

$('body').on('click', '.post_requirements_delete', function (e) {
    e.preventDefault();
    var id = $(this).attr("data-id");
    // currentRow = $(this).closest('tr');
    var url = $(this).attr("data-url");
    swal({
        title: "Delete?",
        text: "Please ensure and then confirm!",
        type: "warning",
        showCancelButton: !0,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
        reverseButtons: !0
    }).then(function (e) {
        if (e.value === true) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });


            $.ajax({
                type: 'Get',
                url: url,
                dataType: "json",
                beforeSend: function () {
                    $(".loader").removeClass('d-none');
                },
                success: function (data) {
                    // currentRow.remove();
                    $(".loader").addClass('d-none');
                    if (data.success == true) {
                        alertify.success(data.message);
                        window.location.reload();
                    } else {
                        alertify.error(data.message);
                    }

                },
            }).done(function () {
                setTimeout(function () {
                    $(".loader").addClass('d-none');
                }, 500);
            });
        }
        else {
            e.dismiss;
        }
    }, function (dismiss) {
        return false;
    })
});

$('body').on("click", ".add-more", function () {
    // Generate new row HTML
    const newRow = `
    <div class="row after-add-more mt-3">
        <div class="col-lg-6 col-md-6 col-sm-6 left_col">
            <select name="schedule[]" class="form-control select22" multiple>
                <option value="Monday">Monday</option>
                <option value="Tuesday">Tuesday</option>
                <option value="Wednesday">Wednesday</option>
                <option value="Thursday">Thursday</option>
                <option value="Friday">Friday</option>
                <option value="Saturday">Saturday</option>
            </select>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 right_col">
            <div class="schedule_time">
                <input type="text" class="timepicker" name="schedule_start_time[]" placeholder="HH:MM AM/PM">
                <span class="text-center text-dark">-</span>
                <input type="text" class="timepicker" name="schedule_end_time[]" placeholder="HH:MM AM/PM">
                <button type="button" class="btn remove-btn">-</button>
            </div>
        </div>
    </div>`;

    $(".after-add-more").last().after(newRow);

    initializeSelect2();

    $(".timepicker").timepicker({
        placeholder: "h:i A",
        timeFormat: "h:i A",
        step: 1,
    });
});


$('body').on("click", ".remove-btn", function () {
    $(this).closest(".row.after-add-more").remove();
});

// see more
document.addEventListener("DOMContentLoaded", () => {
    const toggleButton = document.querySelector(".toggle-button");
    const content = document.querySelector(".content");

    toggleButton?.addEventListener("click", () => {
        content.classList.toggle("expanded");
        toggleButton.textContent = content.classList.contains("expanded")
            ? "less"
            : "more..";
    });
});

// like dislike buttons
//

// filter expand collapse
var dropdown = document.getElementsByClassName("dropdown-btn");
var i;

for (i = 0; i < dropdown.length; i++) {
    dropdown[i].addEventListener("click", function () {
        this.classList.toggle("active");
        var dropdownContent = this.nextElementSibling;
        if (dropdownContent.style.display === "none") {
            dropdownContent.style.display = "grid";
        } else {
            dropdownContent.style.display = "none";
        }
    });
}
//save button
document.getElementById("heartButton")?.addEventListener("click", function () {
    console.log("clicked")
    const saveButton = document.getElementById("heartButton");

    // Toggle the `saved` class
    if (saveButton.classList.contains("saved")) {
        saveButton.classList.remove("saved");
    } else {
        saveButton.classList.add("saved");
    }
});

function parseJson(key) {

    if (data && data[key] !== undefined) {
        try {
            return JSON.parse(data[key]);
        } catch (e) {
            return data[key]; // If not JSON, return raw value
        }
    }
    return null;
}


// range
// var slider = document.getElementById("myRange");
// var output = document.getElementById("demo");
// if (output) {
//     output.innerHTML = slider.value;
// }

// slider.oninput = function () {
//     output.innerHTML = this.value;
// }

document.addEventListener("DOMContentLoaded", function () {
    const tabLinks = document.querySelectorAll(".tab-link");
    const mainTitleButton = document.querySelector("#mainTitle button");

    tabLinks.forEach(link => {
        link.addEventListener("click", function () {
            // Remove active class from all links
            tabLinks.forEach(l => l.classList.remove("active"));
            // Add active class to the clicked link
            this.classList.add("active");
            // Update the main title with the selected tab's text
            mainTitleButton.textContent = this.dataset.tab;
            // Close the accordion (optional)
            const collapseContent = document.querySelector("#collapseContent");
            if (collapseContent.classList.contains("show")) {
                collapseContent.classList.remove("show");
            }
        });
    });
});

function messageModalBtn(reqId, instId, name, image, instEdu, email){
    $('#messageModalName').text(name);
    $('#instId').val(instId);
    $('#to_id').val(instId);
    $('#reqId').val(reqId);
    $('#email').val(email);
    $('#to_name').val(name);
    $('#instEdu').text(instEdu);
    $('#to_img').val(image);
    $('#messageModalImage').attr('src',image);
    $('#MessageModal').modal('show');
}

function hireModalBtn(reqId, instId, schoolId, userName){
    $('#requirment_id').val(reqId);
    $('#instructor_id').val(instId);
    $('#school_id').val(schoolId);
    $('#userName').text(userName);

    let webUrl = window.location.pathname.split('/');
    if (webUrl[1] == 'list-applicants') {
        $('#invited_by_school').val(0);
    }

    if (webUrl[1] == 'invite-applicants') {
        $('#invited_by_school').val(1);
    }
    $('#HireModal').modal('show');
}

function inviteInstructor(reqId, userId, url)
{
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        method: 'POST',
        data: {
            reqId: reqId,
            userId: userId,
        },
        dataType: "json",
        success: function (response) {
            if (response.success == true) {
                alertify.success(response.message);
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                alertify.error(response.message);
            }
        },
        error: function (xhr, status, error) {
            alertify.error("An error occurred. Please try again.");
        }
    });
}

const prevBtn = document.querySelector('.prev');
const nextBtn = document.querySelector('.next');
const items = document.querySelectorAll('.carousel-item');

let currentIndex = 0;

function updateCarousel() {
    items.forEach((item, index) => {
        item.classList.remove('active');
        if (index === currentIndex) {
            item.classList.add('active');
        }
    });
}
if (prevBtn) {
    prevBtn.addEventListener('click', () => {
        currentIndex = (currentIndex > 0) ? currentIndex - 1 : items.length - 1;
        updateCarousel();
    });
}

nextBtn.addEventListener('click', () => {
    currentIndex = (currentIndex < items.length - 1) ? currentIndex + 1 : 0;
    updateCarousel();
});

// Initialize
updateCarousel();

function likeDislikeBtn(btn, reqId, userId, schoolId, url, status)
{
    var status = null; // Default status is null

    if ($(btn).hasClass('like_btn')) {
        if ($(btn).hasClass('liked')) {
            $(btn).removeClass('liked');
            status = null;
        } else {
            $(btn).addClass('liked').removeClass('disliked');
            $(btn).siblings('.dislike_btn').removeClass('disliked');
            status = 1;
        }
    } else if ($(btn).hasClass('dislike_btn')) {
        if ($(btn).hasClass('disliked')) {
            $(btn).removeClass('disliked');
            status = null;
        } else {
            $(btn).addClass('disliked').removeClass('liked');
            $(btn).siblings('.like_btn').removeClass('liked');
            status = 0;
        }
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        method: 'POST',
        data: {
            reqId: reqId,
            userId: userId,
            schoolId: schoolId,
            status: status,
        },
        success: function (response) {

            if (response.success === true) {
            //     alertify.success(response.message);
            // } else {
            //     alertify.error(response.message);
            let targetValue = $('.list-applicants-tab.active').data('target');
            loadTabContent(targetValue);
            }
        },
        error: function (xhr, status, error) {
            // alertify.error("An error occurred. Please try again.");
        }
    });
}

function ChatMessage(chatname)
{
    $('#sidebaruser').html('');
    var name=$('#to_name').val();
    var from_id=$('#from_id').val();
    var from_image=$('#from_image').val();
    var from_name=$('#from_name').val();
    var user_img=$('#user_img').val();
    var user_email=$('#user_email').val();
    var firstDatas;

    db.ref(chatname).orderByChild("time").limitToLast(1).on("value", function(res){

        if (res.exists()) {
        if ($('.availdata')[0].classList.contains('availdata') && $('.availdata')[0].classList.contains('displaynone')) {
            location.reload();
        }else{
            $('.availdata').removeClass('displaynone')
            $('.nodata').addClass('displaynone')
        }

        }else{
            $('.availdata').addClass('displaynone')
            $('.nodata').removeClass('displaynone')
            $('.chat').html('');
        }
        var data=res.val();
        firebaseData = res.val();
        var  html='';

        var keycount=0;
        for (const key in data) {
            keycount++;

            html+='<li class="userclass user'+data[key].to_userid+'"  data-id="'+data[key].to_userid+'" data-email="'+user_email+'" data-name="'+from_name+'" data-img="'+user_img+'" ><div class="dashboard__meessage__contact__wrap"><div class="dashboard__meessage__chat__img"><span class="dashboard__meessage__dot online'+data[key].to_userid+'"></span><img loading="lazy" src="'+user_img+'" alt=""></div><div class="dashboard__meessage__meta"><div><h5>'+from_name+'</h5><p class="preview">'+data[key].message+'</p></div><div><span class="chat__time">'+getTimestamp(data[key].time)+'</span></div></div></div></li>';

            if(keycount=='1'){

            firstData = data[key];

            }
        }
        $('#sidebaruser').html(html);


        // firstDatas = firstData;
        // var userhtml='<div class="dashboard__meessage__profile__img"><img loading="lazy" src="'+firstDatas.from_image+'" alt=""></div><div class="dashboard__meessage__profile__meta d-flex justify-content-between align-items-center"><div class="text"><h5>'+firstDatas.from_name+'</h5><p>'+firstDatas.message+'</p></div><div class="icon deletechat" data-fromid="'+firstDatas.to_userid+'" data-toid="'+firstDatas.from_userid+'"><svg width="6" height="24" viewBox="0 0 6 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6C4.7 6 6 4.7 6 3C6 1.3 4.7 0 3 0C1.3 0 0 1.3 0 3C0 4.7 1.3 6 3 6Z" fill="#004CBD"/><path d="M3 9C1.3 9 0 10.3 0 12C0 13.7 1.3 15 3 15C4.7 15 6 13.7 6 12C6 10.3 4.7 9 3 9Z" fill="#004CBD"/><path d="M3 18C1.3 18 0 19.3 0 21C0 22.7 1.3 24 3 24C4.7 24 6 22.7 6 21C6 19.3 4.7 18 3 18Z" fill="#004CBD"/></svg></div></div>';

        // $('.topuser').html(userhtml);
        // alert(userhtml)

    });


    db.ref(chatname).once("value", function(res){
        $('#sidebaruser li:first').click();
    })

    var im= $('#sidebaruser li:first').attr('data-img');
    var name= $('#sidebaruser li:first').attr('data-name');
    db.ref(chatname).on("value", function(res){
        if (res.exists()) {
            $('.availdata').removeClass('displaynone')
            $('.nodata').addClass('displaynone')
        }else{
            $('.availdata').addClass('displaynone')
            $('.nodata').removeClass('displaynone')

        }
    })
}

function togglePopup(instId) {
    // Get the popup element and toggle visibility
    var popup = document.getElementById('popup_' + instId);

    // Toggle display
    if (popup.style.display === "none" || popup.style.display === "") {
        popup.style.display = "block";
    } else {
        popup.style.display = "none";
    }
}

function showInputField(instId) {
    // Get the input field container and display it
    var inputContainer = document.getElementById('input-container_' + instId);

    // Show input field container
    if (inputContainer.style.display === "none" || inputContainer.style.display === "") {
        inputContainer.style.display = "block";
    }
}

function initAutocompletemapPost() {
    const mapElement = document.getElementById("map");
    const input = document.getElementById("address");


    if (!mapElement && !input) {
        return false;
    }

    const map = new google.maps.Map(document.getElementById("map"), {
        center: {
            lat: -33.8688,
            lng: 151.2195
        },
        zoom: 13,
        mapTypeId: "roadmap",
    });
    // Create the search box and link it to the UI element.
    const searchBox = new google.maps.places.SearchBox(input);

    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

    map.addListener("bounds_changed", () => {
        searchBox.setBounds(map.getBounds());
    });

    let markers = [];
    searchBox.addListener("places_changed", () => {
        const places = searchBox.getPlaces();

        if (places.length == 0) {
            return;
        }

        // Clear out the old markers.
        markers.forEach((marker) => {
            marker.setMap(null);
        });
        markers = [];

        // For each place, get the icon, name and location.
        const bounds = new google.maps.LatLngBounds();

        places.forEach((place) => {

            const addressComponents = place.address_components;
            let country, city, postal_code, state;
            for (let component of addressComponents) {

                if (component.types.includes('country')) {
                    country = component.long_name;
                } else if (component.types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                } else if (component.types.includes('locality') || component.types.includes('administrative_area_level_3')) {
                    city = component.long_name;
                } else if (component.types.includes('postal_code')) {
                    postal_code = component.long_name;
                }
            }
            $('input[name="city"]').val(city);
            $('input[name="country"]').val(country);
            $('input[name="zipcode"]').val(postal_code);
            $('input[name="state"]').val(state);

            $('input[name="lat"]').val(place.geometry.location.lat());
            $('input[name="lng"]').val(place.geometry.location.lng());

            if (!place.geometry || !place.geometry.location) {
                return;
            }

            const icon = {
                url: place.icon,
                size: new google.maps.Size(71, 71),
                origin: new google.maps.Point(0, 0),
                anchor: new google.maps.Point(17, 34),
                scaledSize: new google.maps.Size(25, 25),
            };

            // Create a marker for each place.
            markers.push(
                new google.maps.Marker({
                    map,
                    icon,
                    title: place.name,
                    position: place.geometry.location,
                }),
            );

            if (place.geometry.viewport) {
                // Only geocodes have viewport.
                bounds.union(place.geometry.viewport);
            } else {
                bounds.extend(place.geometry.location);
            }

        });
        map.fitBounds(bounds);

    });
}

$('body').on('change', 'select[name="subjectArea"]', function () {
    var id = $(this).val();
    const reqId = $('#id').val();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/get_subsubjects';
    $.ajax({
        url: url,
        type: 'post',
        data: { 'id': id, 'reqId': reqId },
        dataType: "html",
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (response) {

            $('#sub_subject').html(response);
            //    $('.subsubject_slect').select2({
            //      theme: 'bootstrap4',
            //      width: 'style',
            //      placeholder: 'Select Sub Subject',
            //      allowClear: Boolean($(this).data('allow-clear')),
            //    });
        },
    })
});

$('body').on('change', '.no_class_dates', function() {
    var isValid = $(this).valid();
    if (isValid) {
        $('.add-noClassDate').prop('disabled', false);
    } else {
        $('.add-noClassDate').prop('disabled', true);
    }
});

$('body').on('click', '.add-noClassDate', function() {
    const count = $('.no-class-dates-container').children().length;
    var newField = `
        <div class="mb-2">
            <div class="position-relative">
                <input type="text" class="schedule-datepicker no_class_dates platform_school_input" name="no_class_dates[${count}]" placeholder="MM/DD/YYYY">
                <span class="ms-2 remove-noClassDate" title="Remove"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
  <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
</svg></span>
            </div>
        </div>
    `;
    $('.no-class-dates-container').append(newField);
    // $(newField).insertBefore($(this));
    initializeBootstrapDatepicker();
    applyNoClassDateValidation(count);
});

$('body').on('click', '.remove-noClassDate', function() {
    $(this).closest('div').remove();
    $("#schedulepost").valid();
    $('#class_start_date, #class_end_date, .no_class_dates, .schedule, .schedule_start_time, .schedule_end_time').trigger('change');
});

$('body').on('click', '.add-schedule', function() {
    const count = $('.schedule-container').children().length;
    var newSchedule = `
            <div class="row mb-2 position-relative">
                <div class="col-lg-5" style="padding-right: 15px;">
                    <select name="schedule[${count}]" class="schedule form-control select22">
                        <option value="Monday">Monday</option>
                        <option value="Tuesday">Tuesday</option>
                        <option value="Wednesday">Wednesday</option>
                        <option value="Thursday">Thursday</option>
                        <option value="Friday">Friday</option>
                        <option value="Saturday">Saturday</option>
                        <option value="Sunday">Sunday</option>
                    </select>
                </div>
                <div class="col-lg-5 d-flex gap-1 p-0 custom-gap">
                    <div class="start-time-conatiner position-relative">
                        <input type="text" class="timepicker schedule_start_time platform_school_input" style="width: 132px !important; height:40px !important;" name="schedule_start_time[${count}]">
                        <span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>
                    </div>
                    <div class="end-time-conatiner position-relative">
                        <input type="text" class="timepicker schedule_end_time platform_school_input" style="width: 132px !important; height:40px !important;" name="schedule_end_time[${count}]">
                        <span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>
                    </div>
                </div>
                <div class="col-lg-1 p-0 remove-schedule"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
  <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
</svg></div>
            </div>
            `;

    $('.schedule-container').append(newSchedule);
    initializeTimepicker();
    initializeSelect2();
    applyScheduleValidation(count);
});

$('body').on('click', '.remove-schedule', function() {
    $(this).closest('.row').remove();
    $('#class_start_date, #class_end_date, .no_class_dates, .schedule, .schedule_start_time, .schedule_end_time').trigger('change');
});

$('body').on('click', '.select2-selection__arrow', function () {
    $(this).prev('input.timepicker').trigger('focus');
});

$('body').on('change', '#deliveryMode', function () {
    let selectedValues = $(this).val();

    if(selectedValues == 'in-person' || selectedValues == 'hybrid'){
        $('.addressField').removeClass('d-none');
    } else{
        $('.addressField').addClass('d-none');
    }
});

function applyFilters (route) {
    filters = $('#filtersForm').serialize();
    SortApplicants(null, sortCriteria, route, null);
}
var filters = "";
var sortCriteria = "";
let reviewTabId = "";
let inviteTabId = "";
let textSearch = "";
function SortApplicants(elm, criteria, route, searchText)
{
    // $(elm).css('background-color', '#e9ecef');
    const segmment = window.location.pathname.split('/')
    sortCriteria = criteria;
    textSearch = searchText;

    if (textSearch == null || textSearch == '') {
        $('.resultTxt').html('');
        textSearch = ($('#search').val() != undefined && $('#search').val().trim() != '') ? encodeURIComponent($('#search').val().trim()) : '';
        if (segmment[1] != 'profile') {
            filters = $('#filtersForm').serialize();
        }
    }

    if (segmment[1] == 'profile') {
        textSearch=$("#searchbarschools").val();
        if (textSearch != "") {
            filters = $('#filtersForm').serialize();
        }
    }

    const reviewTab = $('.list-applicants-tab');
    if (reviewTab.length) {
        let activeTab = reviewTab.filter('.active');
        if (activeTab.length) {
            reviewTabId = activeTab.attr('id');
        }
    }

    let inviteTab = $('.invite-talent-tab');
    if (inviteTab.length) {
        let activeTab = inviteTab.filter('.active');
        if (activeTab.length) {
            inviteTabId = activeTab.attr('id');
            if (inviteTabId == 'history') {
                inviteTab = $('.invite-talent-history-tab');
                if (inviteTab.length) {
                    let activeTab = inviteTab.filter('.active');
                    if (activeTab.length) {
                        inviteTabId = activeTab.attr('id');
                    }
                }
            }
        }
    }

    console.log(`${route}?${filters.replace(/\-/g, '_')}${sortCriteria ? '&sort='+ sortCriteria : ''}${textSearch ? '&searchText='+ textSearch : ''}${segmment[1] ? '&tab='+ segmment[1] : ''}${'&reviewTabId='+ reviewTabId }${'&inviteTabId='+ inviteTabId }`);

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: `${route}?${filters.replace(/\-/g, '_')}${sortCriteria ? '&sort='+ sortCriteria : ''}${textSearch ? '&searchText='+ textSearch : ''}${segmment[1] ? '&tab='+ segmment[1] : ''}${'&reviewTabId='+ reviewTabId }${'&inviteTabId='+ inviteTabId }`,
        method: 'GET',
        dataType: 'json',
        success: function (response) {

             $(".count_result").empty();

             $(".count_result").text(response.count);

            if (response.success) {

                $("#totalresults").empty();
                $("#searchparameter").empty();
                $("#totalresults").show();
                $("#totalresults").text(response.count + " results");
                $("#heading_search_query").empty();
                $(".search_is_empty").hide();
                $(".search_without_empty").show();
                $("#heading_search_query").text(response.search);
                if(response.search==""){

                    $(".search_is_empty").show();
                    $(".search_without_empty").hide();

                }
                else{
                    $(".heading_for_resuslts").show();
                    $("#searchparameter").show();
                    $("#searchparameter").html(response.search + ` <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.50371 7.56099L9.15521 10.2125C9.29591 10.3532 9.48673 10.4322 9.68571 10.4322C9.88468 10.4322 10.0755 10.3532 10.2162 10.2125C10.3569 10.0718 10.4359 9.88097 10.4359 9.68199C10.4359 9.48302 10.3569 9.29219 10.2162 9.15149L7.56371 6.49999L10.2157 3.84849C10.2853 3.77883 10.3406 3.69613 10.3782 3.60512C10.4159 3.51411 10.4353 3.41657 10.4353 3.31807C10.4352 3.21957 10.4158 3.12204 10.3781 3.03104C10.3404 2.94005 10.2851 2.85738 10.2155 2.78774C10.1458 2.71811 10.0631 2.66288 9.97208 2.62521C9.88107 2.58754 9.78353 2.56816 9.68503 2.56818C9.58653 2.5682 9.489 2.58763 9.39801 2.62534C9.30701 2.66306 9.22434 2.71833 9.15471 2.78799L6.50371 5.43949L3.85221 2.78799C3.78306 2.71633 3.70033 2.65915 3.60884 2.61981C3.51736 2.58046 3.41895 2.55972 3.31937 2.55881C3.21978 2.5579 3.12102 2.57683 3.02883 2.61449C2.93664 2.65216 2.85287 2.70781 2.78242 2.7782C2.71197 2.84858 2.65624 2.9323 2.61848 3.02445C2.58073 3.1166 2.56171 3.21535 2.56253 3.31494C2.56334 3.41452 2.58399 3.51295 2.62325 3.60447C2.66251 3.69599 2.71961 3.77877 2.79121 3.84799L5.44371 6.49999L2.79171 9.15199C2.72011 9.22121 2.66301 9.304 2.62375 9.39552C2.58449 9.48704 2.56384 9.58546 2.56303 9.68505C2.56221 9.78463 2.58123 9.88338 2.61898 9.97554C2.65674 10.0677 2.71247 10.1514 2.78292 10.2218C2.85337 10.2922 2.93714 10.3478 3.02933 10.3855C3.12152 10.4232 3.22028 10.4421 3.31987 10.4412C3.41945 10.4403 3.51786 10.4195 3.60934 10.3802C3.70083 10.3408 3.78356 10.2837 3.85271 10.212L6.50371 7.56099Z" fill="black"/>
                    </svg>`);

                }

                $('html, body').animate({ scrollTop: 0 }, 'fast'); // or use 'slow'
                if(response.view==""){
                    response.view = `<p class="text-danger text-center mt-5">No result Found</p>`;
                }
                $('#data-container').html(response.view);
                var filterContainer = '';
                var reviewTabId = '';
                var inviteHistory = 0;
                Object.entries(response.request).forEach(([key, value]) => {
                    const tabId = (key === 'reviewTabId' && (value != null && value != '')) ? `#${value}` : '';

                    if (tabId !== '') {
                        $(tabId).text(`${value.toUpperCase()} (${response.totalApplicants})`);
                    }

                    if (key === 'inviteTabId' && value === 'talent') {
                        $('#talent').text(`Invite Talent`);
                    }

                    if (key === 'inviteTabId' && value === 'invited') {
                        inviteHistory += response.totalApplicants;
                        $('#invited').text(`Invited`);
                    }

                    if (key === 'inviteTabId' && value === 'accepted') {
                        inviteHistory += response.totalApplicants;
                        $('#accepted').text(`Accepted`);
                    }

                    if (key === 'inviteTabId' && value === 'declined') {
                        inviteHistory += response.totalApplicants;
                        $('#declined').text(`Declined`);
                    }

                    if (value === 'invited' || value === 'accepted' || value === 'declined') {
                        $('#history').text(`Invitation History`);
                    }

                    if (response.request.tab == 'discover') {
                        if (response.request.searchText != '' && response.request.searchText != undefined) {
                            $('.resultTxt').text(`${response.count} Results for ${response.request.searchText}`);
                        } else {
                            $('.resultTxt').text('');
                        }
                    } else if (key === 'searchText' && value !== '') {
                        let count = response.totalApplicants;
                        if (count === undefined && response.totalApplicants) {
                            count = response.totalApplicants;
                        }
                        count = count || 0;
                        $('.resultTxt').text(`${count} Results for ${value}`);
                    }
                    // let formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                    let formattedKey = '';
                    let displayValue = '';
                    let isSelectall = checkSelectAll(key);
                    if (isSelectall) {
                        formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                        displayValue = 'All';
                    } else {
                        if (Array.isArray(value)) {
                            if (key === 'years_of_relevant_experience') {
                                const input = $("input[name='years-of-relevant-experience[]']")[0]
                                if (value[0] !== input.min || value[1] !== input.max) {
                                    formattedKey = 'Years of Relevant Experience';
                                    displayValue = `${value[0]} yrs - ${value[1]} yrs`;
                                }
                            } else if (key === 'educator_online_rate') {
                                const input = $("input[name='educator-online-rate[]']")[0]
                                if (value[0] !== input.min || value[1] !== input.max) {
                                    formattedKey = 'Online Range';
                                    displayValue = `$${value[0]} - $${value[1]}`;
                                }
                            } else if (key === 'educator_in_person_rate') {
                                const input = $("input[name='educator-in-person-rate[]']")[0]
                                if (value[0] !== input.min || value[1] !== input.max) {
                                    formattedKey = 'In Person Range';
                                    displayValue = `$${value[0]} - $${value[1]}`;
                                }
                            } else if (key === 'subject_area') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.map(subject => subject.replace(/\b\w/g, char => char.toUpperCase())).join(', ');
                            } else if (key === 'subject') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.map(subject => subject.replace(/\b\w/g, char => char.toUpperCase())).join(', ');
                            } else if (key === 'grade_levels') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.map(grades => grades.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase())).join(', ');
                            } else if(key === 'certificationlicense') {
                                formattedKey = 'Certification License';
                                displayValue = value.map(license => license.replace(/_/g, '-').replace(/\b\w/g, char => char.toUpperCase())).join(', ');
                            } else if(key === 'valid_in_states') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.join(', ');
                            } else if(key === 'program_type') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.map(program => program.replace(/_/g, '-').replace(/\b\w/g, char => char.toUpperCase())).join(', ');
                            } else if(key === 'total_cost') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.map(cost => cost.replace(/_/g, '-').replace(/\b\w/g, char => char.toUpperCase())).join(', ');
                            }  else if(key === 'delivery_mode') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.map(cost => cost.replace(/_/g, '-').replace(/\b\w/g, char => char.toUpperCase())).join(', ');
                            } else {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value.join(' - ');
                            }
                        } else {
                            if (key === 'zipcode' && value != null) {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value;
                            } else if(key === 'sort') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                if (value === 'atoz') {
                                    displayValue = 'A to Z';
                                } else if (value === 'ztoa') {
                                    displayValue = 'Z to A';
                                } else {
                                    displayValue = value;
                                }
                            } else if (key != 'zipcode') {
                                formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
                                displayValue = value;
                            }
                        }
                    }
                    if (key == 'reviewTabId' || key == 'inviteTabId') {
                        reviewTabId = value;
                    }
                    if (key !== 'searchText' && key != 'tab' && key != 'reviewTabId' && key != 'inviteTabId' && formattedKey !== '') {
                        let displayText = `${formattedKey} : ${displayValue}`;
                        let shortText = displayText.length > 18 ? displayText.substring(0, 19) + '...' : displayText;
                        filterContainer += `<span class="filterTitle me-2" title="${formattedKey} : ${displayValue}" onclick="removeFilter(null, this, null,'${key}')">
                        ${shortText}
                        <svg width="25px" height="25px" viewBox="0 -0.5 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier">
                                        <path d="M6.96967 16.4697C6.67678 16.7626 6.67678 17.2374 6.96967 17.5303C7.26256 17.8232 7.73744 17.8232 8.03033 17.5303L6.96967 16.4697ZM13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697L13.0303 12.5303ZM11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303L11.9697 11.4697ZM18.0303 7.53033C18.3232 7.23744 18.3232 6.76256 18.0303 6.46967C17.7374 6.17678 17.2626 6.17678 16.9697 6.46967L18.0303 7.53033ZM13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303L13.0303 11.4697ZM16.9697 17.5303C17.2626 17.8232 17.7374 17.8232 18.0303 17.5303C18.3232 17.2374 18.3232 16.7626 18.0303 16.4697L16.9697 17.5303ZM11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697L11.9697 12.5303ZM8.03033 6.46967C7.73744 6.17678 7.26256 6.17678 6.96967 6.46967C6.67678 6.76256 6.67678 7.23744 6.96967 7.53033L8.03033 6.46967ZM8.03033 17.5303L13.0303 12.5303L11.9697 11.4697L6.96967 16.4697L8.03033 17.5303ZM13.0303 12.5303L18.0303 7.53033L16.9697 6.46967L11.9697 11.4697L13.0303 12.5303ZM11.9697 12.5303L16.9697 17.5303L18.0303 16.4697L13.0303 11.4697L11.9697 12.5303ZM13.0303 11.4697L8.03033 6.46967L6.96967 7.53033L11.9697 12.5303L13.0303 11.4697Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </span> `;
                    }

                });
                let spanCount = (filterContainer.match(/<span class="filterTitle me-2".*?>/g) || []).length;
                let firstThreeFilters = '';
                let remainingFilters = '';
                if (spanCount > 0) {
                    let allFilters = filterContainer.match(/<span class="filterTitle me-2"[^>]*>[\s\S]*?<\/span>/g) || [];
                    // Separate the first three filters
                    firstThreeFilters = allFilters.slice(0, 3);

                    // Remaining filters
                    remainingFilters = allFilters.slice(3);
                    // filterContainer += `<span class="removeFilter me-5" onclick="removeFilter('all', null,'${reviewTabId}',null)">Remove Filters
                    //     <svg width="20px" height="20px" viewBox="0 -0.5 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    //         <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                    //         <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                    //         <g id="SVGRepo_iconCarrier"> <path d="M6.96967 16.4697C6.67678 16.7626 6.67678 17.2374 6.96967 17.5303C7.26256 17.8232 7.73744 17.8232 8.03033 17.5303L6.96967 16.4697ZM13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697L13.0303 12.5303ZM11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303L11.9697 11.4697ZM18.0303 7.53033C18.3232 7.23744 18.3232 6.76256 18.0303 6.46967C17.7374 6.17678 17.2626 6.17678 16.9697 6.46967L18.0303 7.53033ZM13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303L13.0303 11.4697ZM16.9697 17.5303C17.2626 17.8232 17.7374 17.8232 18.0303 17.5303C18.3232 17.2374 18.3232 16.7626 18.0303 16.4697L16.9697 17.5303ZM11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697L11.9697 12.5303ZM8.03033 6.46967C7.73744 6.17678 7.26256 6.17678 6.96967 6.46967C6.67678 6.76256 6.67678 7.23744 6.96967 7.53033L8.03033 6.46967ZM8.03033 17.5303L13.0303 12.5303L11.9697 11.4697L6.96967 16.4697L8.03033 17.5303ZM13.0303 12.5303L18.0303 7.53033L16.9697 6.46967L11.9697 11.4697L13.0303 12.5303ZM11.9697 12.5303L16.9697 17.5303L18.0303 16.4697L13.0303 11.4697L11.9697 12.5303ZM13.0303 11.4697L8.03033 6.46967L6.96967 7.53033L11.9697 12.5303L13.0303 11.4697Z" fill="#004cbd"/> </g>
                    //         </svg>
                    //     </span> `;
                    $('.remove-all-filter').removeClass('d-none');
                    $('#clearFilter').attr('onclick', `removeFilter('all', null, '${reviewTabId}', null)`);
                    $('#clearFilter').text(`Clears Filters (${spanCount})`);
                } else {
                    $('.remove-all-filter').addClass('d-none');
                }

                $('.filter-container').html('');
                if (filterContainer != '') {
                    $('.filter-container').addClass('pt-1 pb-3');
                    $('.filter-container').append(firstThreeFilters);

                    if (remainingFilters.length > 0) {
                        $('.next3Filter').html('');
                        $('#seeMoreFilter').removeClass('d-none').addClass('d-block');
                        $('#seeMoreFilter').html(`<i class="fa fa-chevron-down" aria-hidden="true"></i> See ${remainingFilters.length} more`);
                        $('.next3Filter').append(remainingFilters);
                    }
                } else {
                    $('.filter-container').removeClass('pt-1 pb-3');
                }
            }
        },
        error: function (xhr, status, error) {
        }
    });
}

function checkSelectAll(key)
{
    if (key != 'years_of_relevant_experience' && key != 'educator_online_rate' && key != 'educator_in_person_rate' && key != 'tab' && key != 'reviewTabId' && key != 'inviteTabId') {
        const selectElement = $(`input[name='${key.replace(/_/g, '-').replace(/\b\w/g, char => char.toLowerCase())}[]']`);
        const dropdownContainer = selectElement.closest('.dropdown-container');
        const checkbox = dropdownContainer.find(`input[type="checkbox"][id='${key.replace(/_/g, '-').replace(/\b\w/g, char => char.toLowerCase())}']`);
        return checkbox.prop('checked');
    }
    return false;
}

function removeFilter(type, elm, reviewTabId, filterKey)
{
    $('#search').val('');
    const segmment = window.location.pathname.split('/')
    if (type == 'all') {
        $('.remove-all-filter').addClass('d-none');
        $('#seeMoreFilter').addClass('d-none')
        $('.next3Filter').html('')
        $('.filter-container').removeClass('pt-1 pb-3');
        $('.filter-container').html('');
        $('#filtersForm')[0].reset();
        switch (filterKey) {
            case 'educator_online_rate':
                $('input[name="educator-online-rate[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                $('input[name="educator-online-rate[]"]').closest('.range').find('.filter-range-slider-max-input').val('101');
                $('input[name="educator-online-rate[]"]').closest('.filter-range-progress-slider').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                $('input[name="educator-online-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('100+');
                $('input[name="educator-online-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');
                break;

            case 'educator_in_person_rate':
                $('input[name="educator-in-person-rate[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                $('input[name="educator-in-person-rate[]"]').closest('.range').find('.filter-range-slider-max-input').val('101');
                $('input[name="educator-in-person-rate[]"]').closest('.filter-range-progress-slider').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                $('input[name="educator-in-person-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('100+');
                $('input[name="educator-in-person-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');
                break;

            case 'years_of_relevant_experience':
                $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-slider-max-input').val('26');
                $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                $('input[name="years-of-relevant-experience[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('25+');
                $('input[name="years-of-relevant-experience[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');
                break;

            default:
                    $('input[name="educator-online-rate[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                    $('input[name="educator-online-rate[]"]').closest('.range').find('.filter-range-slider-max-input').val('101');
                    $('input[name="educator-online-rate[]"]').closest('.filter-range-progress-slider').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                    $('input[name="educator-online-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('100+');
                    $('input[name="educator-online-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');

                    $('input[name="educator-in-person-rate[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                    $('input[name="educator-in-person-rate[]"]').closest('.range').find('.filter-range-slider-max-input').val('101');
                    $('input[name="educator-in-person-rate[]"]').closest('.filter-range-progress-slider').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                    $('input[name="educator-in-person-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('100+');
                    $('input[name="educator-in-person-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');

                    $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                    $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-slider-max-input').val('26');
                    $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                    $('input[name="years-of-relevant-experience[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('25+');
                    $('input[name="years-of-relevant-experience[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');
                break;
        }

        if(segmment[1]=="profile"){

            var newurl = APP_URL + "/profile";


            SortApplicants(null, null, newurl, null);

        }
        let url = APP_URL + '/'+segmment[1]+'/'+segmment[2];

        if (segmment[1]=="discover") {
            url = APP_URL + '/'+segmment[1];
        }

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: url,
            method: 'GET',
            data: {
                type: reviewTabId,
            },
            dataType: 'json',
            success: function (response) {
                if (response.status) {
                    $('#data-container').html(response.view);
                    switch (reviewTabId) {
                        case 'applicants':
                            $('#applicants').text(`APPLICANTS (${response.totalApplicants})`);
                            break;

                        case 'shortlist':
                            $('#shortlist').text(`SHORTLISTED (${response.totalLikeShortlist})`);
                            break;

                        case 'messaged':
                            // $('#messaged').text(`MESSAGED (${response.totalChatMessage})`);
                            break;

                        case 'archived':
                            $('#archived').text(`ARCHIVED (${response.totalDislikeShortlist})`);
                            break;

                        case 'talent':
                            $('#talent').text(`Invite Talent`);
                            break;

                        case 'invited':
                            $('#invited').text(`Invited`);
                            break;

                        case 'accepted':
                            $('#accepted').text(`Accepted`);
                            break;

                        case 'declined':
                            $('#declined').text(`Declined`);
                            break;

                        default:
                            break;
                    }
                } else if (response.success) {
                    $('#data-container').html(response.view);
                }
            },
            error: function (xhr, status, error) {
            }
        });
    } else {
        $(elm).closest('.filterTitle').remove();
        switch (filterKey) {
            case 'sort':
                sortCriteria = '';
                break;

            case 'subject_area':
                $('input[name="subject-area[]"]').prop('checked', false);
                $('input[id="subject-area"]').prop('checked', false);
                break;

            case 'subject':
                $('input[name="subject[]"]').prop('checked', false);
                $('input[id="subject"]').prop('checked', false);
                break;

            case 'grade_levels':
                $('input[name="grade-levels[]"]').prop('checked', false);
                $('input[id="grade-levels"]').prop('checked', false);
                break;

            // case 'certificationlicense':
            //     $('input[name="certificationlicense[]"]').prop('checked', false);
            //     break;

            // case 'credentialing_agency':
            //     $('input[name="credentialing-agency[]"]').prop('checked', false);
            //     break;

            case 'valid_in_states':
                $('input[name="valid-in-states[]"]').prop('checked', false);
                $('input[id="valid-in-states"]').prop('checked', false);
                break;

            case 'program_type':
                $('input[name="program-type[]"]').prop('checked', false);
                $('input[id="program-type"]').prop('checked', false);
                break;

            case 'delivery_mode':
                $('input[name="delivery-mode[]"]').prop('checked', false);
                $('input[id="delivery-mode"]').prop('checked', false);
                break;

            case 'zipcode':
                $('input[name="zipcode"]').val('');
                break;

            case 'profile_type':
                $('input[name="profile-type[]"]').prop('checked', false);
                $('input[id="profile-type"]').prop('checked', false);
                break;

            case 'total_cost':
                $('input[name="total-cost[]"]').prop('checked', false);
                $('input[id="total-cost"]').prop('checked', false);
                break;

            case 'minimum_education_level':
                $('input[name="minimum-education-level[]"]').prop('checked', false);
                $('input[id="minimum-education-level"]').prop('checked', false);
                break;

            case 'language_spoken':
                $('input[name="language-spoken[]"]').prop('checked', false);
                $('input[id="language-spoken"]').prop('checked', false);
                break;

            case 'educator_online_rate':
                $('input[name="educator-online-rate[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                $('input[name="educator-online-rate[]"]').closest('.range').find('.filter-range-slider-max-input').val('101');
                $('input[name="educator-online-rate[]"]').closest('.range').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                $('input[name="educator-online-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('100+');
                $('input[name="educator-online-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');
                break;

            case 'educator_in_person_rate':
                $('input[name="educator-in-person-rate[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                $('input[name="educator-in-person-rate[]"]').closest('.range').find('.filter-range-slider-max-input').val('101');
                $('input[name="educator-in-person-rate[]"]').closest('.range').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                $('input[name="educator-in-person-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('100+');
                $('input[name="educator-in-person-rate[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');
                break;

            case 'years_of_relevant_experience':
                $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-slider-min-input').val('0');
                $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-slider-max-input').val('26');
                $('input[name="years-of-relevant-experience[]"]').closest('.range').find('.filter-range-progress').css('width', '100%').css('left', '0%');
                $('input[name="years-of-relevant-experience[]"]').closest('.filter-range-slider').find('.filter-range-slider-max').text('25+');
                $('input[name="years-of-relevant-experience[]"]').closest('.filter-range-slider').find('.filter-range-slider-min').text('0');
                break;

            default:
                break;
        }

        $('.apply-filter-btn').trigger('click');
    }
}

function applyScheduleValidation(index)
{
    $('input[name="schedule_start_time[' + index + ']"]').rules('add', {
        required: true,
        messages: {
            required: "Start time is required"
        }
    });

    $('input[name="schedule_end_time[' + index + ']"]').rules('add', {
        required: true,
        greaterThanStartTime: "input[name='schedule_start_time[" + index + "]']",
        messages: {
            required: "End time is required",
            greaterThanStartTime: "End time must be after start time"
        }
    });

    $('select[name="schedule[' + index + ']"]').rules('add', {
        required: true,
        messages: {
            required: "Please select a day"
        }
    });
}

function applyNoClassDateValidation(index)
{
    $('input[name="no_class_dates[' + index + ']"]').rules('add', {
        required: true,
        messages: {
            required: "No class date is required"
        }
    });
}

// function loadUser(id) {
//     const url = APP_URL + '/public_profile/'+id;
//     $.ajaxSetup({
//         headers: {
//             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//         }
//     });
//     $.ajax({
//         url: url,
//         type: 'GET',
//         success: function(response) {
//             window.toggleInstructorProfileSidebar(true, response.html)
//         },
//         error: function(xhr, status, error) {
//             console.error('Error loading user card:', error);
//         }
//     });
// }


function handleCardClick(event, id, reqId, proposalId = null)
{
    // Check if the clicked element is NOT an anchor tag or button
    if (!event.target.closest('a') && !event.target.closest('button') && !event.target.closest('.pop_up_invite') && !event.target.closest('.select2-selection__choice') && !event.target.closest('.invite_talent_modal') && !event.target.closest('#createlist') && !event.target.closest('#exampleModalCenter') &&   !(event.target.tagName === 'INPUT' && event.target.type === 'checkbox') &&!event.target.closest('#exampleModalCenter2') ) {
        loadUser(id,reqId, proposalId); // Call your function
    }


}

// Your existing function remains the same
function loadUser(id, reqId, proposalId = null) {
    const url = APP_URL + '/public_profile/' + id;
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'JSON',
        data: {
            reqId: reqId,
            proposalId: proposalId,
            type:"dashboard_instructor",
        },
        success: function(response) {
            window.toggleInstructorProfileSidebar(true, response.html);
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

function offerBtn(reqId, id) {
    $('')
    const url = APP_URL + '/view-hire-offer/' + id;
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'JSON',
        data: {
            reqId: reqId,
        },
        success: function(response) {
            $('#appendOffer').html(response.html);


            $('#HireModal').modal('show');


            // window.InstructorOfferSidebar(true, response.html);
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

function handleFileSelection() {
    let fileInput = document.getElementById('file');
    let fileNameDisplay = document.getElementById('fileName');

    // Trigger the file input click
    fileInput.click();

    // Listen for file selection
    fileInput.onchange = function () {
        if (fileInput.files.length > 0) {
            fileNameDisplay.textContent = fileInput.files[0].name; // Show selected file name
        } else {
            fileNameDisplay.textContent = "No file selected";
        }
    };
}

$('body').on('click', '#editPayHour', function() {
    // $("#payHour").removeAttr("readonly").focus();
    let $input = $("#payHour");

    // Toggle readonly attribute
    if ($input.prop("readonly")) {
        $input.prop("readonly", false).focus();
    } else {
        $input.prop("readonly", true);
    }
});

$('body').on("input", "#payHour", function() {
    let rate = parseFloat($(this).val().replace(/[^0-9.]/g, '')) || 0;
    let totalHours = parseFloat($("#totalCost").data("total-hours")) || 0;
    $("#totalCost").val((totalHours * rate).toFixed(2));
});

function storeOffer(instructorId, dataId) {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    const url = APP_URL + '/store-hire-offer';
    let isValid = true;
    const paymentOption = $('#payment-option').val();
    const payPerHour = $('#payHour').val()?.split('/')?.[0];
    const totalCost = $('#totalCost').val();
    const contractTerms = $('#additional-terms').val();
    const fileInput = $('#file')[0];
    // const file = $('#file')[0].files[0];

    if (payPerHour == '') {
        $('#payHour').attr('style', 'border-bottom-color: red !important;');
        isValid = false;
    } else {
        $('#payHour').attr('style', 'border-bottom-color: black !important;');
    }

    if (totalCost == '') {
        $('#totalCost').attr('style', 'border-bottom-color: red !important;');
        isValid = false;
    } else {
        $('#totalCost').attr('style', 'border-bottom-color: black !important;');
    }


    if (!$('#service').is(':checked')) {
        $('#service').addClass('checkbox-error').removeClass('checkbox-checked');
        isValid = false;
    } else {
        $('#service').addClass('checkbox-checked').removeClass('checkbox-error');
    }

    let file = fileInput.files[0];
    if (fileInput.files.length != 0) {
        let allowedExtensions = ["pdf", "csv", "xls", "xlsx"];
        let fileExtension = file.name.split('.').pop().toLowerCase();
        let fileSize = file.size;
        let maxSize = 5 * 1024 * 1024; // 5MB

        if (!allowedExtensions.includes(fileExtension)) {
            $("#file").val("");
            $('#fileButton').attr('style', 'border: 1px solid red !important;');
            isValid = false;
        } else if (fileSize > maxSize) {
            $("#file").val("");
            $('#fileButton').attr('style', 'border: 1px solid red !important;');
            isValid = false;
        } else {
            $('#fileButton').attr('style', 'border: none !important;');
        }
    }

    if (!isValid) {
        $('html, body').animate({ scrollTop: 0 }, 'slow');
        return false;
    }

    let formData = new FormData();
    formData.append('instructor_id', instructorId);
    formData.append('data_id', dataId);
    formData.append('payment_option', paymentOption);
    formData.append('pay_per_hour', payPerHour);
    formData.append('total_cost', totalCost);
    formData.append('contract_terms', contractTerms);
    formData.append('file', $('#file')[0].files[0]);

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        beforeSend: function () {
            $('.main_slide_offer').html(loading);
            $('.main_slide_offer').prop('disabled', true);
        },
        success: function(response) {
            if (response.success) {
                $('#offerAlertModal').show();
                $('.main_slide_offer').prop('disabled', true);
                $('.main_slide_offer').text('View Contract');
                // alert('Offer sent successfully!');
            } else {
                $('#HireModal').modal('hide');
                $('#alertOfferStatusModal').modal('show');
            }
        },
        error: function(xhr) {
            console.error(xhr.responseText);
            // alert('An error occurred. Please try again.');
        }
    });
}

function DuplicateRequirement(id)
{
    const url = APP_URL + '/duplicate-requirement-data/'+id;
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        method: 'POST',
        dataType: 'json',
        success: function (response) {
            if (response.success) {
                alertify.success('Data Duplicate');
                window.location.reload();
            }
        },
        error: function (xhr, status, error) {
        }
    });
}

function CloseRequirement(id)
{
    const url = APP_URL + '/close-requirement';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        method: 'POST',
        dataType: 'json',
        data: {
            id: id,
        },
        success: function (response) {
            if (response.success) {
                alertify.success('Requirement Closed');
                window.location.reload();
            }
        },
        error: function (xhr, status, error) {
            console.log(error);

        }
    });
}

$('body').on('change', 'input[name="delivery[]"]', function(){
    let online = $('#delivery-0').is(':checked');
    let inPerson = $('#delivery-1').is(':checked');
    let hybrid = $('#delivery-2').is(':checked');
    if ($(this).attr('id') === 'delivery-2' && hybrid) {
        $('input[name="delivery[]"]').prop('checked', true);
    } else if (!online || !inPerson) {
        $('#delivery-2').prop('checked', false);
    }

    if (online && inPerson) {
        $('#delivery-2').prop('checked', true);
    }
});

function viewProfileDescription(elm)
{
    var descriptionText = $(elm).closest('.description-container').find('.description-text');
    var fullDescription = $(elm).closest('.description-container').find('.full-description');
    var viewMoreLink = $(elm);

    if (fullDescription.is(':visible')) {
        fullDescription.hide();
        descriptionText.show();
        viewMoreLink.text('view more');
    } else {
        fullDescription.show();
        descriptionText.hide();
        viewMoreLink.text('view less');
    }
};

let debounceSearchTimer;
$('#search').on('keyup', function (e) {
    const segment = window.location.pathname.split('/');
    let reviewTabId = '';
    let inviteTabId = '';

    const reviewTab = $('.list-applicants-tab');
    if (reviewTab.length) {
        let activeTab = reviewTab.filter('.active');
        if (activeTab.length) {
            reviewTabId = activeTab.attr('id');
        }
    }

    let inviteTab = $('.invite-talent-tab');
    if (inviteTab.length) {
        let activeTab = inviteTab.filter('.active');
        if (activeTab.length) {
            inviteTabId = activeTab.attr('id');
            if (inviteTabId == 'history') {
                inviteTab = $('.invite-talent-history-tab');
                if (inviteTab.length) {
                    let activeTab = inviteTab.filter('.active');
                    if (activeTab.length) {
                        inviteTabId = activeTab.attr('id');
                    }
                }
            }
        }
    }

    clearTimeout(debounceTimer); // clear previous timer before setting a new one
    // if (e.which === 13) { // Detect "Enter" key press
    //     e.preventDefault(); // Prevent form submission if inside a form
        let route = APP_URL + `/filtered-applicants/${segment[2]}`;
        // const searchText = encodeURIComponent($(this).val().trim()); // Get search input value
        const searchText = $(this).val().replace(/^\s+/, ''); // Get search input value
        if (searchText != '') {
            debounceSearchTimer = setTimeout(function () {
                SortApplicants(null, null, route, searchText);
            }, 1000); // 1 second debounce for typing
        }
        if (searchText == '' && $('.resultTxt') != undefined) {
            debounceSearchTimer = setTimeout(function () {
                $('.resultTxt').html('');
                SortApplicants(null, null, route, searchText);
            }, 1000); // 1 second debounce for typing
        }
        $(this).val($(this).val().replace(/^\s+/, ''));
        // if (searchText !== '') {
            // $.ajax({
            //     url: `/search-filter/${segment[2]}?searchText=${searchText}&tab=${segment[1]}&reviewTabId=${reviewTabId}&inviteTabId=${inviteTabId}`, // Change to your actual endpoint
            //     method: 'GET', // Or 'POST' based on your controller logic
            //     dataType: 'json',
            //     success: function (response) {
            //         console.log('Search results:', response);
            //         if (response.success) {
            //             $('#data-container').html(response.view);
            //             switch (reviewTabId) {
            //                 case 'applicants':
            //                     $('#applicants').text(`APPLICANTS (${response.totalApplicants})`);
            //                     break;

            //                 case 'shortlist':
            //                     $('#shortlist').text(`SHORTLISTED (${response.totalApplicants})`);
            //                     break;

            //                 case 'messaged':
            //                     // $('#messaged').text(`MESSAGED (${response.totalChatMessage})`);
            //                     break;

            //                 case 'archived':
            //                     $('#archived').text(`ARCHIVED (${response.totalApplicants})`);
            //                     break;

            //                 default:
            //                     break;
            //             }
            //         }
            //     },
            //     error: function (xhr, status, error) {
            //         console.error('Search error:', error);
            //     }
            // });
        // }
    // }
});

function inviteUserModal(schoolId, reqId, status, reqName)
{
    const userNames = $('.user_id_checkbox:checked').map(function() {
        return $(this).data('username');
    }).get();

    $('#inviteUserName').text(userNames);
    $('#inviteReqName').text(reqName);
    $('.invite-user-btn').attr('onclick', `inviteBulkUser(${schoolId}, ${reqId}, '${status}')`);
    $('#inviteUserModal').modal('show');
}

$('body').on('change', '.user_id_checkbox', function() {
    let countChecked = 0;
    $('.user_id_checkbox').each(function () {
        if($(this).is(':checked')) {
            countChecked++;
        }
    })
    console.log(countChecked == 0 && !$('.multi-invite-btn').hasClass('d-none') && !$('#history').hasClass('active'));
    console.log($('#history').hasClass('active'));

    if (countChecked == 0 && !$('.multi-invite-btn').hasClass('d-none') && !$('#history').hasClass('active')) {
        $('.multi-invite-btn').addClass('d-none');
    } else if ($('#history').hasClass('active')) {
        $('.multi-invite-btn').addClass('d-none');
    } else {
        $('.multi-invite-btn').removeClass('d-none');
    }
});

function inviteBulkUser(schoolId, reqId, status)
{
    $('#inviteUserModal').modal('hide');
    const userIds = $('.user_id_checkbox:checked').map(function() {
                        return $(this).val();
                    }).get();

    const url = APP_URL + '/invite-bulk-user';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        method: 'POST',
        data: {
            school_id: schoolId,
            requirement_id: reqId,
            user_ids: userIds,
            status: status,
        },
        dataType: 'json',
        success: function (response) {
            if (response.success) {
                alertify.success('Instructors Invited');
                window.location.reload();
            }
        },
        error: function (xhr, status, error) {
            alertify.error(error);
        }
    });
}

function inviteUser(button, schoolId, reqId, userId, status)
{
    $(button).prop('disabled', true);
    $('#inviteUserModal').modal('hide');

    const url = APP_URL + '/invite-user';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        method: 'POST',
        data: {
            school_id: schoolId,
            requirement_id: reqId,
            user_id: userId,
            status: status,
        },
        dataType: 'json',
        success: function (response) {
            if (response.success) {
                alertify.success('Instructor Invited');
                window.location.reload();
            }
        },
        error: function (xhr, status, error) {
            alertify.error(error);
        }
    });
}


//for the opening of the modal
$(document).on("click", ".invite-user", function(event) {

    var instructor_id = $(this).data("instructor_id");
    var screen_name=$(this).data("screen");
    if (screen_name === "public_profile") {
        console.log("sdfsdfsd");

        $("#invite_talent_modal" + instructor_id).modal("show");

        // Wait until modal is shown, then adjust backdrop
        $("#invite_talent_modal" + instructor_id).on('shown.bs.modal', function () {
            $(".modal-backdrop").css("z-index", "0"); // Lower z-index so it's behind everything
        });
    } else {
        $("#invite_talent_modal" + instructor_id).modal("show");
    }

});
// for the closing of the  modal
$(document).on("click",".close_invite_talent",function(){
    var instructor_id = $(this).data("instructor_id");



    $("#invite_talent_modal" +instructor_id).modal("hide");

});


$(document).on("click",".cross_icon_div_indtructorpop_up",function(e){


    $('.pop_up_invite').hide();
    $(".overlay").hide();

})



function toggleRequirementList(button) {
    const list = button.nextElementSibling;
    if (list.style.display === "none" || list.style.display === "") {
        list.style.display = "block";
    } else {
        list.style.display = "none";
    }
}
$(document).on("click",".cross_icon_div",function(event){
    console.log("rohit");
    event.stopPropagation();



})
function withdrawInvitation(button, schoolId, reqId, userId)
{
    $(button).prop('disabled', true);
    const url = APP_URL + '/withdraw-invitation';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: url,
        method: 'POST',
        data: {
            school_id: schoolId,
            requirement_id: reqId,
            user_id: userId,
        },
        dataType: 'json',
        success: function (response) {
            if (response.success) {
                alertify.success('Invitation Withdraw');
                window.location.reload();
            } else {
                alertify.error(response.message);
            }
        },
        error: function (xhr, status, error) {
            alertify.error(error);
        }
    });
}

// ✅ Function to get valid dates based on schedule (Monday, etc.)
function getValidDates(start, end, noClassDates, schedules, scheduleStartTimes, scheduleEndTimes) {
    var startDate = new Date(start);
    var endDate = new Date(end);
    var validDates = [];

    // ✅ Get noClassDates from noClassDates
    var noClassDates = noClassDates.map(date => formatDate(new Date(date)));

    while (startDate <= endDate) {
        let formattedDate = formatDate(startDate);
        let dayName = getDayName(startDate); // Get day of the week

        // ✅ Check if the date matches the schedule and is not in noClassDates
        let scheduleIndex = schedules.indexOf(dayName);

        if (scheduleIndex !== -1 && !noClassDates.includes(formattedDate)) {
            validDates.push({
                date: formattedDate,
                startTime: scheduleStartTimes[scheduleIndex] || null,
                endTime: scheduleEndTimes[scheduleIndex] || null
            });
        }

        startDate.setDate(startDate.getDate() + 1); // Move to next day
    }

    return validDates;
}

// ✅ Function to get the day name (e.g., Monday, Tuesday)
function getDayName(date) {
    return date.toLocaleDateString('en-US', { weekday: 'long' });
}

// ✅ Function to calculate time difference in minutes
function getTimeDifferenceInMinutes(start, end) {
    var startDate = new Date("01/01/2000 " + start);
    var endDate = new Date("01/01/2000 " + end);

    var diff = (endDate - startDate) / 60000; // Convert milliseconds to minutes
    return diff >= 0 ? diff : (1440 + diff); // Handle overnight cases
}

// ✅ Function to format date as MM/DD/YYYY
function formatDate(date) {
    var mm = String(date.getMonth() + 1).padStart(2, '0'); // Get month (01-12)
    var dd = String(date.getDate()).padStart(2, '0'); // Get day (01-31)
    var yyyy = date.getFullYear();
    return mm + '/' + dd + '/' + yyyy;
}

function ChangeHireStatus(elm, status,hireId)
{
    const url = APP_URL + '/update-hire-offer/'+hireId;
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'JSON',
        data: {
            status: status,
        },
        success: function(response) {
           if (response.success) {
                alertify.success('Change Successfully');
                window.location.reload();
           } else {
            alertify.error('Data not found');
           }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

if ($('#overviewTabs a').hasClass('active')) {
    $('#overviewTabs a.active').trigger('click');
}

function updateOverview(selectedTab, encryptedId, userId, viewClass = false) {

    const parent = document.getElementById('overview-container');

    Array.from(parent.children).forEach(element => {
        element.classList.add("d-none");
    });

    $('#overviewTabs a').removeClass('active');
    $(`#`+selectedTab).addClass('active');

    if (selectedTab === "class-details") {
        document.querySelector('.class-details').classList.remove("d-none");
    } else if (selectedTab === "session-summary") {
        document.querySelector('.summary').classList.remove("d-none");
        loadOverviewDetails(encryptedId, userId, viewClass);
    } else if (selectedTab === "class-setup") {
        document.querySelector('.class-setup').classList.remove("d-none");
    }
}

function loadOverviewDetails(encryptedId, userId, viewClass) {
    let APP_URL;
    if (APP_URL == undefined) {
        APP_URL = window.location.origin;
    }
    const url = APP_URL + '/view-datatable/'+encryptedId+'/'+userId;
    if ($.fn.DataTable.isDataTable('#dataTable')) {
        $('#dataTable').DataTable().destroy();
    }
    let table = $('#dataTable').DataTable({
        processing: true,
        // serverSide: true,
        ordering: true,
        searching: true,
        // ajax: "{{ route('view-datatable', ['encryptedId' => $encryptedId, 'userId' => encrypt_str($user->id)]) }}",
        ajax: {
            url:url,
            type:"GET",
            data: function(d) {
                d.class_rating_educator = $('#class_rating').val();
                d.class_rating_proctor = $('#proctor_rating').val();
                d.class_start_date = $('#class_daterange').val();
                d.class_status = $('#class_status').val();
                d.viewClass = viewClass;
            }
        },
        columns: [{
                data: 'class_date',
                title: 'Class Date'
            },
            {
                data: 'start_end_time',
                title: 'Start-End Time'
            },
            {
                data: 'class_rating_educator',
                title: 'Class rating (Educator)'
            },
            {
                data: 'class_summary_educator',
                title: 'Class summary (Educator)'
            },
            {
                data: 'class_rating_proctor',
                title: 'Class rating (Proctor)'
            },
            {
                data: 'class_summary_proctor',
                title: 'Class summary (Proctor)'
            },
            // {
            //     data: 'class_status',
            //     title: 'Class Status'
            // },
            {
                data: 'class_status',
                title: 'Class Status',
                render: function(data, type, row) {
                    let statusClass = data === 'Completed' ? 'compt' : (data ===
                        'Upcoming' ? 'upcmg' : '');
                    return `<span class="${statusClass}">${data}</span>`;
                }
            }
            // {
            //     data: 'payment_status',
            //     title: 'Payment status',
            //     className: 'toggle-column hidden-column'
            // },
            // {
            //     data: 'payment_status_reason',
            //     title: 'Payment status (Reason)',
            //     className: 'toggle-column hidden-column'
            // },
            // {
            //     data: 'date_of_payment',
            //     title: 'Date of payment',
            //     className: 'toggle-column hidden-column'
            // },
            // {
            //     data: 'amount_paid',
            //     title: 'Amount paid',
            //     className: 'toggle-column hidden-column'
            // },
            // {
            //     data: 'summary_updated_educator',
            //     title: 'Summary updated (Educator)',
            //     className: 'toggle-column hidden-column'
            // },
            // {
            //     data: 'summary_updated_proctor',
            //     title: 'Summary updated (Proctor)',
            //     className: 'toggle-column hidden-column'
            // },
            // {
            //     data: 'attendance',
            //     title: 'Attendance (number of students)',
            //     className: 'toggle-column hidden-column'
            // },
            // {
            //     data: 'uploaded_document',
            //     title: 'Uploaded document',
            //     className: 'toggle-column hidden-column'
            // }
        ]
    });
    // Default DataTable search box hide karna
    // $('#dataTable_paginate, .dataTables_info, .dataTables_length, .dataTables_filter').addClass('d-none');
    $('.dataTables_length, .dataTables_filter').addClass('d-none');

    // Custom search box se search functionality add karna
    $('.summerry-search input[type="search"]').off('keyup').on('keyup', function () {
        table.search(this.value).draw();
    });

}

$(document).ready(function () {
    // Initialize Date Range Picker
    $('.daterangeSession').daterangepicker({
        autoUpdateInput: false,
        locale: {
            format: 'MM/DD/YYYY',
            cancelLabel: 'Clear'
        },
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'This Week': [moment().startOf('week'), moment().endOf('week')],
            'Last Week': [moment().subtract(1, 'week').startOf('week'), moment().subtract(1, 'week').endOf('week')],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'This Year': [moment().startOf('year'), moment().endOf('year')],
            'Last Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')]
        }
    });

    // Set value in input when range is selected
    $('.daterangeSession').on('apply.daterangepicker', function (ev, picker) {
        $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
    });

    // Clear input when cancel is clicked
    $('.daterangeSession').on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
    });
});

function SessionFilterModal() {
    $('#SessionFilterModal').modal('show');
}

function applySessionFilters() {
    console.log("Applying Filters...");  // Debugging
    $('#dataTable').DataTable().ajax.reload();
    $('#SessionFilterModal').modal('hide');
}
function filterFormReset(event) {
    event.preventDefault();

    $('#class_rating').val('');
    $('#proctor_rating').val('');
    $('#class_daterange').val('');
    $('#class_status').val('');

    // DataTable ko reload karna bina table reset kiye
    $('#dataTable').DataTable().ajax.reload(null, false);
}

function addProctor()
{
    // document.querySelectorAll('.proctor_fields').forEach(function (el) {
    //     el.classList.remove('d-none');
    // });
    $('#select_proctor').val('');
    $('#addProctorModal').modal('show');
}

function showRoster(programId)
{
    const url = APP_URL + '/view-roster'
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'JSON',
        data: {
            programId: programId,
        },
        success: function(response) {
            if (response.success) {
                $('#roster-section').html(response.view);
                $('#viewRosterModal').modal('show');
            } else {
                console.error('Data not found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

function withdrawOffer(hireId, reqId)
{
    const url = APP_URL + '/update-hire-offer/'+hireId;
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'JSON',
        data: {
            status: 'withdraw',
        },
        success: function(response) {
            if (response.success) {
                alertify.success('Contract Withdraw Successfully');
                window.location.href = APP_URL + '/view-hires/' + reqId;
            } else {
                console.error('Data not found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

$('body').on('change', '#select_proctor', function() {
    $('.proctor_fields').addClass('d-none');
});

function addStudent()
{
    const url = APP_URL + '/get-classes';
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'JSON',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">Select Grade</option>';
                $.each(response.classes, function(index, classData) {
                    options += `<option value="${classData.id}">${classData.class_name}</option>`;
                });
                let newRow = `
                <tr class="proctor_tbl_row add_stdnt_tbl">
                    <td class="add_stdnt_name">
                        <label>Student Name</label>
                        <input type="text" name="student_name[]" class="form-control student_name" maxlength="50">
                    </td>
                    <td class="add_stdnt_grade">
                        <label>Grade</label>
                        <div class="add_stdnt_grade_flx">
                        <select class="grade" name="grade[]" id="grade">
                            ${options}
                        </select>
                            <a href="javascript:void(0);" class="deleteStudent"><i class="fa fa-trash"></i></a>
                        </div>
                    </td>

                </tr>
            `;
            $("#studentTableBody").append(newRow);
            } else {
                alertify.error('Data not found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

$('body').on("click", ".deleteStudent", function () {
    $(this).closest("tr").remove();
});

$('body').on('change', '.csv-file', function() {
    var file = this.files[0]; // Selected file ko get karna
    if (file) {
        var fileName = file.name; // File ka naam get karna
        var fileExt = fileName.split('.').pop().toLowerCase(); // File ka extension get karna
        var allowedExtensions = ['csv', 'xls', 'xlsx']; // Allowed file extensions

        if (!allowedExtensions.includes(fileExt)) {
            // Invalid file type par error message show karna
            $('.roster-err').text('Invalid file type. Please upload a CSV or Excel file.');
            $(this).val(''); // File input ko reset karna
            return;
        } else {
            $('.roster-err').text(''); // Agar valid file hai toh error hata dena
        }

        $('.download_roster').remove();
        // Check if the "download_roster" element already exists
        var downloadLink = $('.download_roster');
        if (downloadLink.length === 0) {
            // Agar pehle se nahi hai toh create karke append karna
            downloadLink = $(`<a class="add_proctr download_roster document_upload">Roster.csv
                <svg class="remove-proctor cursor-pointer" width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.5 7.88403L1.65589 12.7281C1.47465 12.9094 1.24398 13 0.963878 13C0.683777 13 0.453105 12.9094 0.271863 12.7281C0.0906207 12.5469 0 12.3162 0 12.0361C0 11.756 0.0906207 11.5253 0.271863 11.3441L5.11597 6.5L0.271863 1.65589C0.0906207 1.47465 0 1.24398 0 0.963878C0 0.683777 0.0906207 0.453105 0.271863 0.271863C0.453105 0.0906207 0.683777 0 0.963878 0C1.24398 0 1.47465 0.0906207 1.65589 0.271863L6.5 5.11597L11.3441 0.271863C11.5253 0.0906207 11.756 0 12.0361 0C12.3162 0 12.5469 0.0906207 12.7281 0.271863C12.9094 0.453105 13 0.683777 13 0.963878C13 1.24398 12.9094 1.47465 12.7281 1.65589L7.88403 6.5L12.7281 11.3441C12.9094 11.5253 13 11.756 13 12.0361C13 12.3162 12.9094 12.5469 12.7281 12.7281C12.5469 12.9094 12.3162 13 12.0361 13C11.756 13 11.5253 12.9094 11.3441 12.7281L6.5 7.88403Z" fill="#004CBD"/>
                </svg></a>`);
            $('.import_csv').after(downloadLink);
        } else {
            $('.download_roster').remove();
        }

        // File name ko `download_roster` anchor tag ke andar set karna
        // downloadLink.text(fileName);
    }
});

$('body').on('click', '.remove-proctor', function() {
    $(this).closest('.download_roster').remove();
})

$("body").on("change", ".document-file", function () {
    let file = this.files[0]; // Selected file ko get karna
    let maxDocuments = 5;
    let currentDocuments = $(".download_document").length;
    if (file) {
        let maxSize = 500 * 1024;
        let fileName = file.name; // File ka naam get karna
        var fileSize = file.size;
        let fileExt = fileName.split(".").pop().toLowerCase(); // File ka extension get karna
        let allowedExtensions = ["pdf", "docx", "png", "jpg"]; // Allowed file extensions

        if (!allowedExtensions.includes(fileExt)) {
            $(".onboarding-err").text(
                "Invalid file type. Please upload a valid document file."
            );
            $(this).val(""); // File input reset karna
            return;
        } else if (fileSize > maxSize) {
            $(".onboarding-err").text(
                "File size exceeds 500 KB. Please upload a smaller file."
            );
            $(this).val("");
            return;
        } else if (currentDocuments >= maxDocuments) {
            $(".onboarding-err").text(
                "You can only upload a maximum of " + maxDocuments + " documents."
            );
            $(this).val(""); // Reset the file input
            return;
        } else {
            $(".onboarding-err").text(""); // Valid file hone par error hata dena
        }

        // Create a new download link for the file
        let downloadLink = $(
            '<a class="add_proctr download_document document_upload d-flex align-items-center gap-3" title="'+fileName+'"></a>'
        );
        $(this).siblings(".import_document").after(downloadLink);

        if (fileName.length > 16) {
            fileName = fileName.substring(0, 16) + "..."; // Truncate and add ellipsis
        }

        // Set the file name as the text of the download link
        downloadLink.text(fileName);

        // Add a cross button for removal
        let crossButton = $(
            `<svg class="cursor-pointer" width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.48864 0.272727L4.24858 3.24716H4.31676L6.08523 0.272727H8.16903L5.50568 4.63636L8.22869 9H6.10653L4.31676 6.02131H4.24858L2.45881 9H0.34517L3.0767 4.63636L0.396307 0.272727H2.48864Z" fill="#004CBD"></path>
            </svg>`
        );
        downloadLink.append(crossButton);

        // Event listener for the cross button to remove the file
        crossButton.on("click", function () {
            // Remove the file and clear the input
            $(this).parent().remove(); // Remove the download link and the cross button cause dowmload dlink is the parent elemnt
            // Clear the file input
        });

        // Clear the file input value to allow re-uploading the same file if needed
        $(this).val(""); // Reset the file input after uploading to allow re-selection of the same file if needed
    }
});

function storeClassSetup(encryptedId, userId)
{
    var form = document.getElementById('proctor-form'); // Form ko sahi tarike se select karna
    var formData = new FormData(form);
    var isValid = true;
    let hasNoError = true;
    let focusElm = "";

    if ($('#select_proctor').val() == '') {
        $('.ptoctor-select').css('border-color', 'red');
        isValid = false;
        focusElm = $('#select_proctor');
    } else {
        $('.ptoctor-select').css('border-color', '#787777');
    }

    var csvFileInput = $('#csv')[0];
    var csvFile = csvFileInput.files[0];
    if (csvFile && csvFile !== undefined) {
        var allowedExtensions = ['csv', 'xls', 'xlsx'];
        var fileName = csvFile.name;
        var fileExtension = fileName.split('.').pop().toLowerCase();

        if (!allowedExtensions.includes(fileExtension)) {
            isValid = false;
            focusElm = $('#import_csv');
            $('.roster-err').text('Invalid file type. Please upload a CSV or Excel file.');
        } else {
            formData.append('csv', csvFile);
        }
    } else {
        if ($('#rosterFile').is(':visible') && $('#rosterFile').val() !== '') {
            formData.append('rosterFile', $('#rosterFile').val());
        } else {
            $('.roster-err').text('Please Upload the roster');
            isValid = false;
            focusElm = $('#import_csv');
        }
    }

    if ($('#class_link').val() != '') {
        var linkPattern = /^(https?:\/\/|www\.)[^\s]+$/;
        if (!linkPattern.test($('#class_link').val().trim())) {
            $('#class_link').css('border-color', 'red');
            isValid = false;
            focusElm = $('#class_link');
        } else {
            $('#class_link').css('border-color', '#787777');
        }
    }

    var onboardingFileInput = $('#document')[0];
    var documentFile = onboardingFileInput.files[0];
    let maxSize = 500 * 1024;
    if (!documentFile && documentFile !== undefined) {
        var allowedExtensions = ['pdf', 'docx', 'png', 'jpg'];
        var fileName = documentFile.name;
        var fileSize = file.size;
        var fileExtension = fileName.split('.').pop().toLowerCase();

        if (!allowedExtensions.includes(fileExtension)) {
            isValid = false;
            focusElm = $('#document');
            $('.onboarding-err').text('Invalid file type. Please upload a valid document file.');
        } else if (fileSize > maxSize) {
            isValid = false;
            focusElm = $('#document');
            $('.onboarding-err').text('File size exceeds 500 KB. Please upload a smaller file.');
        }
    }

    if (!isValid || !hasNoError) {
        focusElm.focus();
        return false;
    }

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    const url = APP_URL + '/store-class-setup/' + encryptedId;

    formData.append('userId', userId);
    // console.log(Object.fromEntries(formData.entries()));
    $('#complete-setup').attr('disabled', true).css('pointer-events', 'none');
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'JSON',
        data: formData,
        contentType: false,  // Important: Disable default content type
        processData: false,
        beforeSend: function () {
            $('#complete-setup').html(loading);
            $('#complete-setup').attr('disabled', true).css('pointer-events', 'none');
        },
        success: function(response) {
            if (response.success) {
                    $('#complete-setup').attr('disabled', true);
                    alertify.success('Save Successfully');
                    // window.location.reload();
                    $('#proctor_id').val(response.lastProctorId);
                    $('#roster_id').val(response.lastRosterId);
                    $('#classLink_id').val(response.linkId);
                    $('#onboarding_id').val(response.noteId);
                    $('#complete-setup').text('Save All').removeAttr('disabled').css('pointer-events', '');
                    if (response.lastRosterId) {
                        let downloadLink = `<a class="add_proctr download_roster document_upload" onclick="showRoster(${response.programId})">View Roster</a>`;
                        let existingLink = $(".import_csv").next("a.download_roster");
                        if (existingLink.length) {
                            existingLink.replaceWith(downloadLink); // Replace old link with new one
                        } else {
                            $(".import_csv").after(downloadLink); // Insert if not exists
                        }
                    }

                    if (response.onboarding_file) {
                        let documentLink  = `<a href="${response.onboarding_file}" class="add_proctr download_document document_upload">Onboarding Document</a>`;
                        let existingDocumentLink = $(".import_document").prev("a.download_document");
                        if (existingDocumentLink.length) {
                            existingDocumentLink.replaceWith(documentLink); // Replace old document link
                        } else {
                            $(".import_document").before(documentLink); // Insert new document link
                        }
                    }
                    if (response.lastProctorId !== '' && response.lastRosterId != '' && response.linkId != '' && response.noteId != '') {
                        $('#session-summary').removeClass('pe-none');
                    }
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
            } else {
                // alertify.error('Data not found');
                if (response.errors) {
                    // Remove duplicate errors using Set
                    let uniqueErrors = [...new Set(response.errors)];
                    let errorMessage = uniqueErrors.join('<br>');
                    alertify.error(errorMessage);
                    $('.csv-file').val('');
                } else {
                    alertify.error('Data not found');
                }
                $('#complete-setup').text('Save All').removeAttr('disabled').css('pointer-events', '');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

function loadContractTabContent(type)
{
    const parent = document.getElementById('contract-container');

    Array.from(parent.children).forEach(element => {
        element.classList.add("d-none");
    });
    $('#contractDetailsTab a').removeClass('active');
    $(`#`+type).addClass('active');

    if (type === "details") {
        document.querySelector('.contract-details').classList.remove("d-none");
    } else if (type === "messages") {
        // document.querySelector('.messages').classList.remove("d-none");
    } else if (type === "overview") {
        document.querySelector('.contract-overview').classList.remove("d-none");
    }
}

function generateClassLink(element) {
    document.getElementById('class_link').classList.toggle('d-none');
    document.getElementById('select_zoom').classList.toggle('d-none');
}

$('body').on('click', '.import_csv', function() {
    $('.csv-file').trigger('click');
    $('.add_stdnt_tbl').remove();
});

$('body').on('click', '.import_document', function() {
    $('.document-file').trigger('click');
    $('.add_stdnt_tbl').remove();
});



// function toggleSaveButton(section) {
//     let hasValue = false;

//     // Check if any input, select, textarea has a value
//     section.find("input, select, textarea").each(function () {
//         if ($(this).val().trim() !== "") {
//             hasValue = true;
//             return false; // Exit loop early if any field has a value
//         }
//     });

//     // Show or hide the Save button based on input
//     section.find(".save-btn").toggle(hasValue);
// }
// $(document).ready(function () {

//     // Detect input change in text, email, number, textarea, or select
//     $("body").on("input change", ".class-setup-section input, .class-setup-section textarea, .class-setup-section select", function () {
//         toggleSaveButton($(this).closest(".class-setup-section"));
//     });

//     // Detect file upload changes
//     $("body").on("change", ".class-setup-section input[type='file']", function () {
//         toggleSaveButton($(this).closest(".class-setup-section"));
//     });

// });

function saveClassSetup(elm, type, encryptedId, userId)
{
    var isValid = true;
    let hasNoError = true;

    var formData = new FormData();
    if ($('#first_name').val().trim() == '') {
        $('#first_name').css('border-color', 'red');
        isValid = false;
    }else {
        $('#first_name').css('border-color', '#787777');
    }

    if ($('#last_name').val().trim() == '') {
        $('#last_name').css('border-color', 'red');
        isValid = false;
    }else {
        $('#last_name').css('border-color', '#787777');
    }

    // if ($('#phone').val().trim() == '') {
    //     $('#phone').css('border-color', 'red');
    //     isValid = false;
    // } else {
    //     $('#phone').css('border-color', '#787777');
    // }

    if ($('#email').val().trim() != '') {
        var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i
        var regemailid = emailfilter.test($("#email").val());
        if (regemailid == false) {
            $('#email').css('border-color', 'red');
            isValid = false;
        } else {
            $('#email').css('border-color', '#787777');
        }
    }

    formData.append('type', type);
    formData.append('first_name', $('#first_name').val());
    formData.append('last_name', $('#last_name').val());
    formData.append('phone', $('#phone').val());
    formData.append('email', $('#email').val());

    // switch (type) {
    //     case 'proctor':
    //         break;

    //     case 'roster':
    //         if (!$('.add_stdnt_tbl').is(':visible')) {
    //             var fileInput = $('#csv')[0];
    //             var file = fileInput.files[0];

    //             if (!file) {
    //                 $('.roster-err').text('Roster is missing');
    //                 isValid = false;
    //             } else {
    //                 var allowedExtensions = ['csv', 'xls', 'xlsx'];
    //                 var fileName = file.name;
    //                 var fileExtension = fileName.split('.').pop().toLowerCase();

    //                 if (!allowedExtensions.includes(fileExtension)) {
    //                     isValid = false;
    //                     $('.roster-err').text('Invalid file type. Please upload a CSV or Excel file.');
    //                 } else {
    //                     formData.append('csv', file);
    //                 }
    //             }
    //         } else {
    //             $('.student_name').each(function () {
    //                 if ($(this).val().trim() === '') {
    //                     $(this).css('border-color', 'red');
    //                     hasNoError = false;
    //                 } else {
    //                     $(this).css('border-color', '#787777'); // Valid input
    //                     formData.append('student_names[]', $(this).val());
    //                 }
    //             });

    //             $('.grade').each(function () {
    //                 if ($(this).val().trim() === '') {
    //                     $(this).css('border-color', 'red');
    //                     hasNoError = false;
    //                 } else {
    //                     $(this).css('border-color', '#787777'); // Valid input
    //                     console.log($(this).val());

    //                     formData.append('grades[]', $(this).val());
    //                 }
    //             });
    //         }
    //         formData.append('type', type);
    //         break;

    //     case 'links':
    //         if ($('#class_link').is(':visible') && $('#class_link').val() == '') {
    //             $('#class_link').css('border-color', 'red');
    //             isValid = false;
    //         } else {
    //             if ($('#class_link').is(':visible')) {
    //                 var linkPattern = /^(https?:\/\/|www\.)[^\s]+$/;
    //                 if (!linkPattern.test($('#class_link').val().trim())) {
    //                     $('#class_link').css('border-color', 'red');
    //                     isValid = false;
    //                 } else {
    //                     $('#class_link').css('border-color', '#787777');
    //                     formData.append('class_link', $('#class_link').val());
    //                 }
    //             } else if ($('#select_zoom').val() == '') {
    //                 $('#select_zoom').css('border-color', 'red');
    //             } else {
    //                 $('#select_zoom').css('border-color', '#787777');
    //                 formData.append('select_zoom', $('#select_zoom').val());
    //             }
    //         }
    //         formData.append('type', type);
    //         break;

    //     case 'onboarding':
    //         if ($('#onboarding_instructions').val() == '') {
    //             $('#onboarding_instructions').css('border-color', 'red');
    //             isValid = false;
    //         } else {
    //             $('#onboarding_instructions').css('border-color', '#787777');
    //             formData.append('onboarding_instructions', $('#onboarding_instructions').val());
    //         }
    //         var onboardingFileInput = $('#document')[0];
    //         if (onboardingFileInput && onboardingFileInput.files.length > 0) {
    //             var file = onboardingFileInput.files[0];
    //             var allowedExtensions = ['pdf', 'csv', 'xls', 'xlsx'];
    //             var fileName = file.name;
    //             var fileExtension = fileName.split('.').pop().toLowerCase();

    //             if (!allowedExtensions.includes(fileExtension)) {
    //                 isValid = false;
    //                 $('.onboarding-err').text('Invalid file type. Please upload a PDF, CSV or Excel file.');
    //             } else {
    //                 formData.append('document', file);
    //             }
    //         }
    //         formData.append('type', type);
    //         break;

    //     default:
    //         break;
    // }

    if (!isValid || !hasNoError) {
        return false;
    }
    formData.append('onboarding_id', $('#onboarding_id').val());
    formData.append('classLink_id', $('#classLink_id').val());
    formData.append('roster_id', $('#roster_id').val());
    formData.append('proctor_id', $('#proctor_id').val());
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    const url = APP_URL + '/save-class-setup/' + encryptedId;
    formData.append('userId', userId);
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'JSON',
        data: formData,
        contentType: false,  // Important: Disable default content type
        processData: false,
        beforeSend: function () {
            $(elm).html(loading);
            $(elm).attr('disabled', true).css('pointer-events', 'none');
        },
        success: function(response) {
            if (response.success) {
                    $(elm).attr('disabled', false);
                    alertify.success('Save Successfully');
                    $('#addProctorModal').modal('hide');
                    $('#addProctorForm')[0].reset();
                    // window.location.reload();
                    $('#proctor_id').val(response.lastProctorId);
                    $('#roster_id').val(response.lastRosterId);
                    $('#classLink_id').val(response.linkId);
                    $('#onboarding_id').val(response.noteId);
                    $(elm).text('Add Proctor').removeAttr('disabled').css('pointer-events', '');
                    if (response.lastProctorId !== '' && response.lastRosterId != '' && response.linkId != '' && response.noteId != '') {
                        $('#session-summary').removeClass('pe-none');
                    }
            } else {
                alertify.error('Data not found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

function closeHireModal()
{
    $('#HireModal').modal('hide');
}


function bookmarkUser(elm)
{
    const instructorId = elm.getAttribute('data-instructor-id');
    const screen=elm.getAttribute('data-screen');
    if (screen == "public_profile_screen") {
        $('#exampleModalCenter2').on('shown.bs.modal', function () {
            $('.modal-backdrop').css('z-index', '1');
        });
    }

    console.log(screen);
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
     url:APP_URL + "/getallcateogry",
     method: "post",
     data:{
        instructor_id:instructorId,
     },
     success:function(response)
     {




        if (response.all_categories && response.all_categories.length > 0) {

            var  assigned_categories=response.assigned_to_instructor_category;




             console.log(assigned_categories);
            $("#exampleModalCenter").modal("hide");
            $("#createlist").modal("hide");
            $("#exampleModalCenter2").modal("show");

            // On success of your AJAX call
                console.log(response);
                $(".dynamic_checkboxes").empty();
            $(".dynamic_save_button").empty();

            $(".dynamic_save_button").append(`
                                                    <a   data-instructor-id="${response.instructor_id}" type="button"
                                                     class="btn educator_type my-3"
                                                     style="width:63%!important; border-radius:34px; color:#004CBD; text-decoration: none;text-align:left">
                                                     + Create a new list
                                                    </a>
                <div style="display: flex; flex-direction: column; align-items: center;">
                                                    <button data-instructor-id="${response.instructor_id}" type="button" class="btn save_created_list btn-secondary py-3" data-dismiss="modal" style="width:48%!important;border-radius:34px;background-color:#004CBD ">Save</button>

                </div>
    `);
         response.all_categories.forEach(function(category)
       {

        $(".dynamic_checkboxes").append(`
        <div class="checkbox-item d-flex gap-3 align-items-center py-3">
         <input class="categories_instructor_save" style="height:15px;border:2px solid #C5C6CC" type="checkbox" id="category-${category.id}" value="${category.id}"
                ${assigned_categories.includes(category.id) ? 'checked' : ''}>
                <label class="mb-0" style="font-weight:bold" for="category-${category.id}">${category.name}</label>
        </div>
    `);




    });



        }
        else{

            $(".dynamic_checkboxes").empty(); // Clears old checkboxes

            $("#exampleModalCenter").modal("show");


            $("#exampleModalCenter2").modal("hide");

            $(".dynamic_first_create_categoy_button").empty();
            $(".dynamic_first_create_categoy_button").append(` <button data-instructor-id=${response.instructor_id} style="background-color:#004CBD;border-radius:34px" class="btn btn-primary px-5 py-3 educator_type" style="border-radius:30px">


                                                    Create a new list</button>`);
        }

     }

    })


}

$(document).on("click", ".close_save_modal", function () {
    // Remove previously bound 'hidden' event handlers
    $('#exampleModalCenter').off('hidden.bs.modal');

    // Now bind your specific one-time action
    $('#exampleModalCenter').one('hidden.bs.modal', function () {
        $('#createlist').modal('hide');
    });

    // Hide the modal
    $("#exampleModalCenter").modal("hide");
});


$(document).on("click", ".save_created_list", function()
{
    //this is the button
    const $button = $(this);
    if ($button.data('processing')) {
        return; // Exit if already processing
    }

    $button.data('processing', true); // Mark as processing

    // Optional: show loader on button
    $button.prop("disabled", true).text("Saving...");


    const $parent = $(this).closest('.newapplicant');

    // Now find only the checked checkboxes within that parent
    let categoriesArray = $parent.find(".categories_instructor_save:checked").map(function() {
        return $(this).val();
    }).get();



    let instructorId = $(this).data("instructor-id");


// Converts the jQuery object (which is special and not a plain array) into a regular JavaScript array.
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});
  $.ajax({

    url:APP_URL +"/instructor_category_save",
    method:"post",
    data:{
        categories:categoriesArray,
        instructorId:instructorId,
    },
    success:function(response){

    if(response.status=="success"){

       if(response.message=="category_is_empty"){
        var instructorId = response.instructor_id;

        var button = $('button[data-instructor-id="' + instructorId + '"]');
        var container = button.closest('.like_dislike_container_class');

        // Find ONLY the SVG inside the button itself
        var svg = button.find('svg');

        // Set button background to white
        button.css('background-color', '#ffffff');

        // Set SVG rect fill (background) to white
        svg.find('rect').attr('fill', '#ffffff');

       // Set SVG path stroke (outline) to blue
         svg.find('path').attr('stroke', '#004CBD');

        alertify.success('Saved successfully');
        $("#exampleModalCenter2").modal("hide");


        $("#createlist").modal("hide");


       }
       else
       {

        var instructorId = response.instructor_id;

        var button = $('button[data-instructor-id="' + instructorId + '"]');
        var container = button.closest('.like_dislike_container_class');

        // Find ONLY the SVG inside the button itself
        var svg = button.find('svg');

        // Change background color of the button
        button.css('background-color', '#004CBD');

       // Change SVG rect fill (background)
       svg.find('rect').attr('fill', '#004CBD');

       // Change SVG path stroke (outline)
        svg.find('path').attr('stroke', '#ffffff');

       alertify.success('Saved Successfully');

       $("#exampleModalCenter2").modal("hide");


       $("#createlist").modal("hide");




       }




    }


    }


  })


});

//this is for the create list button when we create new category final button
$(document).on("click", ".educator_type", function () {

    $("#category_input").css("border", "none");
    $("#exampleModalCenter").modal("hide"); // Hide the first modal
    $("#exampleModalCenter2").modal("hide"); // Hide the first modal
    const $parent = $(this).closest('.newapplicant');
    $parent.find('#category_input').val("");
    console.log("this is the educator id");
    let instructorId = $(this).data("instructor-id");

    // When the first modal is fully hidden, then show the second
    $("#createlist").modal("show");
    $('#exampleModalCenter').on('hidden.bs.modal', function ()
    {
        $(".dynamic_create_list_button").empty();
        $(".dynamic_create_list_button").append(`

            <button class="btn px-5 py-3 cancel_create_list" style="border: 2px solid #004CBD; border-radius: 34px;">Cancel</button>
            <button data-instructor-id=${instructorId} id="create_list" type="submit" class="btn btn-primary  px-5 py-3" style="border-radius:34px;background-color:#004CBD">Create list</button>

        `)
        $("#createlist").modal("show");













    });
    $('#exampleModalCenter2').on('hidden.bs.modal', function ()
    {


        $(".dynamic_create_list_button").empty();
        $(".dynamic_create_list_button").append(`

            <button class="btn px-5 py-3 cancel_create_list" style="border: 2px solid #004CBD; border-radius: 34px;">Cancel</button>
            <button data-instructor-id=${instructorId} id="create_list" type="submit" class="btn btn-primary  px-5 py-3" style="border-radius:34px;background-color:#004CBD">Create list</button>

        `)



        // Unbind this event so it doesn't trigger multiple times in the future
        $('#exampleModalCenter2').off('hidden.bs.modal');
    });







});
 $(document).on("click",".close_save_modal_2",function(){

            $("#exampleModalCenter2").on('hidden.bs.modal', function () {
                $("#createlist").modal("hide");
            });

            $("#exampleModalCenter2").modal("hide");


})
// $(document).on("click",".close_create_list_modal",function(){

//   $("#createlist").modal("hide");










// })

$(document).on("click","#create_list",function(e)
{

    if($("#category_input").val()==""){
        $("#category_input").css("border", "1px solid red");
        return;


    } else
    {
        var $button = $(this); // Cache the button element
        var originalText = $button.text(); // Store original button text

        // Show "Saving..." on the button
        $button.text("Saving...").prop("disabled", true);


        var originalText = $button.text(); // Store original button text



    }

   e.preventDefault();
   var categoryname=$("#category_input").val();
   var instrcutor_id=  $(this).data('instructor-id');
   $.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    $.ajax({
        url:APP_URL + "/create_category",
        method:"post",
        data:{
            category:categoryname,
            instructor_id:instrcutor_id,
        },

        success:function(response)
        {

            if(response.message=="already_exist"){

                var $button = $("#create_list");
                $button.text("Save").prop("disabled", false);
                alertify.error("list already exist");

            }
            else{

                var category_id_new=response.category_id_new;
                var  assigned_categories=response.assigned_to_instructor_category;
                if(response.status=="success"){

                    alertify.success('list added Successfully');
                    $("#createlist").modal("hide");


                    $('#createlist').on('hidden.bs.modal', function ()
                    {

                        $(".dynamic_checkboxes").empty();
                        $(".dynamic_save_button").empty();

                        $(".dynamic_save_button").append(`
                         <div style="display: flex; flex-direction: column; align-items: center;">
                                                             <button data-instructor-id="${response.instructor_id}" type="button" class="btn save_created_list btn-secondary py-3" data-dismiss="modal" style="width:48%!important;border-radius:34px;background-color:#004CBD ">Save</button>
                                                             <p class="fw-500 mb-0 py-3" style="color:black">Or</p>
                                                          <a  data-instructor-id="${response.instructor_id}" type="button"
                                                         class="btn educator_type"
                                                         style="width:48%!important; border-radius:34px; color:#004CBD; text-decoration: underline;">
                                                         Create a new list
                                                        </a>                                                     </div>



                     `);
                          response.all_categories.forEach(function(category)
                        {

                         $(".dynamic_checkboxes").append(`
                         <div class="checkbox-item d-flex gap-3 align-items-center py-3">
                         <input class="categories_instructor_save"
                          style="height:15px;border:2px solid #C5C6CC"
                         type="checkbox"
                         id="category-${category.id}"
                         value="${category.id}"
                         ${assigned_categories.includes(category.id) || category.id === category_id_new ? 'checked' : ''}>

                                 <label class="mb-0" style="font-weight:bold" for="category-${category.id}">${category.name}</label>
                         </div>
                     `);
                     });
                     $("#exampleModalCenter2").modal('show');
                    });

                }


            }


        },
        error: function(xhr, status, error) {
            console.error("AJAX Error:", xhr.responseText);
            let errorMsg = "An error occurred while creating the category.";

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }

            alertify.error(errorMsg);
        }



    })






});
$(document).on("click", ".cancel_create_list", function(e) {
    e.preventDefault();
   // Attach a one-time event listener before hiding
   $('#createlist').off('hidden.bs.modal');
   $('#createlist').one('hidden.bs.modal', function () {
       if ($('.dynamic_checkboxes').children().length > 0) {
           $('#exampleModalCenter2').modal('show');
       } else {
           $('#exampleModalCenter').modal('show');
       }
   });

   // Now hide the modal
   $("#createlist").modal("hide");
});


//this is the close if the create list small pop up
$(document).on("click",".close_create_list_modal",function(e){

    e.preventDefault();
    // Attach a one-time event listener before hiding
    $('#createlist').off('hidden.bs.modal');
    $('#createlist').one('hidden.bs.modal', function () {
        if ($('.dynamic_checkboxes').children().length > 0) {
            $('#exampleModalCenter2').modal('show');
        } else {
            $('#exampleModalCenter').modal('show');
        }
    });

    // Now hide the modal
    $("#createlist").modal("hide");


})

$('body').on("click", ".add-list-btn", function (event) {
    event.stopPropagation();
    let parent = $(this).parent();
    $(this).remove();

    parent.append(`
        <input type="text" class="form-control list-input" placeholder="Enter list name">
    `).find('.list-input').focus();;
});

$('body').on("click", ".list-input", function (event) {
    event.stopPropagation();
});

$('body').on("keypress", ".list-input", function (e) {
    if (e.which == 13) {
        let listName = $(this).val().trim();
        let userId = $(this).closest(".applicant-item").data("applicant-id");
        let reqId = $(this).closest(".applicant-item").data("requirement-id");
        let dropdownMenu = $(this).closest(".dropdown-menu");

        if (listName !== "") {
            const url = APP_URL + '/save-applicant-list';
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'JSON',
                data: {
                    userId: userId,
                    reqId: reqId,
                    list_name: listName,
                },
                success: function(response) {
                    if (response.success) {
                        dropdownMenu.empty();
                        response.lists.forEach(list => {
                            dropdownMenu.append(`<li class="list-item text-secondary px-3">${list}</li>`);
                        });

                        dropdownMenu.append(`
                            <li>
                                <a class="dropdown-item text-secondary add-list-btn" href="javascript:void(0);">Add list +</a>
                            </li>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading user card:', error);
                }
            });
        }
    }
});

let container = $(".class-container");
let cardWidth = $(".class-card").outerWidth(true); // Get the width of a card including margins
let scrollAmount = cardWidth * 1; // Move by 2 cards at a time (adjust if needed)

$(".right-btn").click(function () {
    container.animate({ scrollLeft: "+=" + scrollAmount }, 300); // Scroll right
});

$(".left-btn").click(function () {
    container.animate({ scrollLeft: "-=" + scrollAmount }, 300); // Scroll left
});

function changeRequiremnet(status, url)
{
    $('#req-search').val('');
    $('.btn-group .btn').removeClass('btn-link').removeClass('btn-link_new');
    $('.btn-group .btn[data-status="' + status + '"]').addClass('btn-link').addClass('btn-link_new');
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'JSON',
        data: {
            status: status
        },
        success: function(response) {
            if (response.success) {
                $('.requirement-continer').html(response.html);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}

$('#req-search').on('keyup', function() {

    const searchText = $(this).val().trim();
    let status = $('.btn-group').find('.btn-link').attr('data-status');
    let url = APP_URL + '/all-requirements';
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'JSON',
        data: {
            status: status,
            searchText: searchText,
        },
        success: function(response) {
            if (response.success) {
                $('.requirement-continer').html(response.html);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
});

// $('#reportsTab .report-tab-link').on('click', function() {
//     $('#reportsTab .report-tab-link').removeClass('active'); // Remove active from all tabs
//     $(this).addClass('active'); // Add active to clicked tab

//     let selectedTab = $(this).text().trim();
//     let url = APP_URL + "/reports";
//     let reportType = "";
//     let datatable = "";

//     if (selectedTab === "Spend by contract") {
//         reportType = 'spend-by-contracts';
//         datatable = '#contractsDataTable';

//     } else if (selectedTab === "Spend by educator") {
//         reportType = 'spend-by-educators';
//         datatable = '#educatorsDataTable';
//     }

//     if ($.fn.DataTable.isDataTable('#dataTable')) {
//         $('#dataTable').DataTable().destroy();
//         $('#dataTable tbody').empty(); // Clear old data
//     }

//     loadReportsDatatable(url, reportType, datatable);

// });

// function loadReportsDatatable(url , reportType){
//     $('#dataTable').DataTable({
//         'processing': true,
//         'serverSide': true,
//         'serverMethod': 'GET',
//         'autoWidth': true,
//         'searching': true,
//         'ordering': true,
//         'ajax': {
//             'url': url,
//             'type': 'GET',
//             'data': function(data) {
//                 data.reportType = reportType; // Send reportType in request
//             },

//             'dataSrc': function(response) {
//                 loadChart(response.chartData); // Load chart when DataTable loads
//                 return response.aaData;
//             }
//         },
//         'columns': [
//             {
//                 data: "contractId"
//             },
//             {
//                 data: "contractDetails"
//             },
//             {
//                 data: "contractDates",
//                 orderable: false,
//             },
//             {
//                 data: "educator"
//             },
//             {
//                 data: "totalBudget" ,
//                 orderable: false,
//             },
//             {
//                 data: "budgetUsed",
//                 orderable: false,
//             },
//             {
//                 data: "status"
//             },
//         ],
//         'order': [[0, 'desc']],
//         'drawCallback': function(settings) {
//             var table = $(this).DataTable();
//             var showPagination = table.page.info().recordsTotal > 10;
//             $('#dataTable_paginate').toggle(showPagination);
//         }
//     });
// }

// function loadChart(chartData) {
//     var ctx = document.getElementById("contractsChart").getContext("2d");

//     var contractLabels = chartData.map(item => "Contract " + item.contract);
//     var totalBudgets = chartData.map(item => item.totalBudget);
//     var budgetsUsed = chartData.map(item => item.budgetUsed);

//     if (window.contractsChartInstance) {
//         window.contractsChartInstance.destroy();
//     }

//     window.contractsChartInstance = new Chart(ctx, {
//         type: 'line',
//         data: {
//             labels: contractLabels,
//             datasets: [
//                 {
//                     label: "Total Budget ($)",
//                     data: totalBudgets,
//                     backgroundColor: "rgba(54, 162, 235, 0.6)",
//                 },
//                 {
//                     label: "Budget Used ($)",
//                     data: budgetsUsed,
//                     backgroundColor: "rgba(255, 99, 132, 0.6)",
//                 }
//             ]
//         },
//         options: {
//             responsive: true,
//             scales: {
//                 y: {
//                     beginAtZero: true
//                 }
//             }
//         }
//     });
// }
$('#reportsTab .report-tab-link').on('click', function() {

    $("#spend_by_contract").toggleClass("d-none");
    $("#spend_by_educator").toggleClass("d-none");

    $('#reportsTab .report-tab-link').removeClass('active');
    $(this).addClass('active');

    let selectedTab = $(this).text().trim();
    let url = APP_URL + "/reports";
    let reportType, datatable;

    // Hide all content sections
    $('.spend-contracts, .spend-educators').addClass('d-none');

    if (selectedTab === "Spend by contract") {
        $('.spend-contracts').removeClass('d-none');
        reportType = 'spend-by-contracts';
        datatable = '#contractsDataTable';
    } else if (selectedTab === "Spend by educator") {
        $('.spend-educators').removeClass('d-none');
        reportType = 'spend-by-educators';
        datatable = '#educatorsDataTable';
    }

    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable(datatable)) {
        $(datatable).DataTable().destroy();
        $(datatable + ' tbody').empty();
        // $(datatable).empty();
    }

    loadReportsDatatable(url, reportType, datatable);
});



function loadReportsDatatable(url, reportType, datatable) {
    // $(datatable).DataTable().destroy();

    let columns = [];
    if (reportType === 'spend-by-educators') {
        columns = [
            {
                data: "srNo",
                render: function (data, type, row, meta) {
                    return meta.row + 1; // SrNo properly 1,2,3...
                }
            },
            {data: "educatorName"},
            // {data: "activeContracts"},
            {
                data: "activeContracts",
                render: function(data) {
                    return data;
                }
            },
            {data: "totalBudget"},
            {data: "amountUsed"},
            {data: "remainingBudget"},
            {
                data: null,
                orderable: false,
                render: function (data, type, row) {
                    // return `<svg class="expand-btn" data-id="${row.instructor_id}" id="Layer_1_1_" style="width: 20px;" version="1.1" viewBox="0 0 16 16" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8,0C3.582,0,0,3.582,0,8s3.582,8,8,8s8-3.582,8-8S12.418,0,8,0z M8,11.207L2.646,5.854l0.707-0.707L8,9.793l4.646-4.646  l0.707,0.707L8,11.207z"/></svg>`;
                    return `<svg class="expand-btn" data-id="${row.instructor_id}" style="width: 20px;" viewBox="0 0 16 16">
                        <path d="M8 11.207L2.646 5.854l.707-.707L8 9.793l4.646-4.646.707.707L8 11.207z" />
                    </svg>
                    `;
                }
            }
        ];
    } else {
        columns = [
            { data: "contractId" },
            { data: "contractDetails" },
            { data: "contractDates", orderable: false },
            { data: "educator" },
            { data: "totalBudget", orderable: false },
            { data: "budgetUsed", orderable: false },
            { data: "status" }
        ];
    }
    console.log(columns);



    let table = $(datatable).DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: url,
            type: 'GET',
            data: function (data) {
                data.reportType = reportType;
                 // Add date filter parameters
                 const dateRange = $('#dateFilter').val().split(' - ');
                 data.startDate = dateRange[0] || '';
                 data.endDate = dateRange[1] || '';
                 data.search_name_contract = $('#searchInput_contract').val();
                 data.search_name_educator = $('#searchInput_educators').val();
            },
            dataSrc: function(response) {

                if (reportType === 'spend-by-educators') {
                    const chartData = {
                        labels: response.aaData.map(item => item.educatorName),
                        amounts: response.aaData.map(item => item.amountUsed)
                    };
                    loadEducatorsChart(chartData);
                } else {
                    loadChart(response.chartData); // Load chart when DataTable loads
                }
                return response.aaData;
            },
            error: function () {
                console.log('Error fetching data');
            }
        },
        columns: columns,
        order: [[0, 'asc']], // ✅ Ascending SrNo
        drawCallback: function() {




            // ✅ Expand / Collapse
            $('.expand-btn').off('click').on('click', function () {
                const $svg = $(this);
                let educatorId = $(this).data('id');
                // let row = $(this).closest('tr');
                // let tr = row;
                const tr = $svg.closest('tr');
                const path = $svg.find('path');
                console.log(educatorId);
                if (tr.hasClass('shown')) {
                    tr.nextAll('tr.child-row').remove();
                    tr.removeClass('shown');
                    path.attr('d', 'M8 11.207L2.646 5.854l.707-.707L8 9.793l4.646-4.646.707.707L8 11.207z');
                } else {
                    $.ajax({
                        url: APP_URL + '/reports/get-contracts/' + educatorId,
                        type: 'GET',
                        success: function (response) {
                            console.log(url);
                            if (response.data.length > 0) {
                                let html = '';

                                $.each(response.data, function (index, item) {
                                    console.log(item);
                                    html += `<tr class="child-row">
                                        <td>${item.srNo || '-'}</td>
                                        <td>${item.educatorName || '-'}</td>
                                        <td>${item.activeContracts || '-'}</td>
                                        <td>${item.totalBudget || '-'}</td>
                                        <td>${item.amountUsed || '-'}</td>
                                        <td>${item.remainingBudget || '-'}</td>
                                        <td></td>
                                        </tr>`;
                                });



                                tr.after(html);
                                tr.addClass('shown');
                                path.attr('d', 'M8 4.793l4.646 4.646-.707.707L8 6.207l-3.939 3.939-.707-.707L8 4.793z');
                            } else {
                                alert('No Contracts Found');
                            }
                        },
                        error: function() {
                            alert('Failed to load contracts');
                        }
                    });
                }
            });
        },


    });

    $('#searchInput_contract').on('keyup', function() {


        table.draw();

    });
    $('#searchInput_educators').on('keyup', function() {


        table.draw();

    });



    $('.dataTables_length, .dataTables_filter').addClass('d-none');

    // Custom search box se search functionality add karna



    $('#applyDateFilter').click(() => table.ajax.reload());
}


$('#dateFilter').daterangepicker({
    autoUpdateInput: false,
    opens: 'left',
    locale: {
        format: 'MM/DD/YYYY',
        separator: ' - ',
        applyLabel: 'Apply',
        cancelLabel: 'Clear',
        customRangeLabel: 'Custom',
        daysOfWeek: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr','Sa'],
        monthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
    },
    ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment()],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment().endOf('month')],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    }
});

// Event Handlers
$('#dateFilter').on('apply.daterangepicker', function(ev, picker) {
    $(this).val(
        picker.startDate.format('MM/DD/YYYY') + ' - ' +
        picker.endDate.format('MM/DD/YYYY')
    );
    $('#educatorsDataTable').DataTable().ajax.reload();
});

$('#dateFilter').on('cancel.daterangepicker', function(ev, picker) {
    $(this).val('');
    $('#educatorsDataTable').DataTable().ajax.reload();
});

function loadChart(chartData) {
//     var ctx = document.getElementById("contractsChart").getContext("2d");

//     var contractIds = [...new Set(chartData.map(item => item.contract))];
//     var colors = ["#A855F7", "#F97316", "#3B82F6", "#22C55E"];

//     if (window.contractsChartInstance) {
//         window.contractsChartInstance.destroy();
//     }

//     var datasets = contractIds.map((contract, index) => {
//         return {
//             label: "Contract ID: " + contract,
//             data: chartData
//                 .filter(item => item.contract === contract)
//                 .map(item => item.totalBudget),
//             borderColor: colors[index % colors.length],
//             backgroundColor: "transparent",
//             borderWidth: 2,
//             tension: 0.4
//         };
//     });

//     window.contractsChartInstance = new Chart(ctx, {
//         type: 'line',
//         data: {
//             labels: ["Start", "1st Week", "2nd Week", "3rd Week", "4th Week"], // X-axis labels
//             datasets: datasets
//         },
//         options: {
//             responsive: true,
//             plugins: {
//                 legend: {
//                     display: true,
//                     position: 'bottom'
//                 }
//             },
//             scales: {
//                 y: {
//                     beginAtZero: false,
//                     ticks: {
//                         callback: function(value) {
//                             return "$" + value;
//                         }
//                     }
//                 }
//             }
//         }
//     });
// }

const ctx = document.getElementById('contractsChart').getContext('2d');

if (window.myContractsChart instanceof Chart) {
    window.myContractsChart.destroy();
}

const data = {
    labels: ['Start', '1st Week', '2nd Week', '3rd Week', '4th Week'],
    datasets: [
        {
            label: 'Contract ID: 123456',
            data: [100, 150, 200, 230, 270],
            borderColor: 'purple',
            backgroundColor: 'rgba(128, 0, 128, 0.2)',
            tension: 0.4
        },
        {
            label: 'Contract ID: 456899',
            data: [120, 160, 250, 100, 130],
            borderColor: 'orange',
            backgroundColor: 'rgba(255, 165, 0, 0.2)',
            tension: 0.4
        },
        {
            label: 'Contract ID: 577854',
            data: [250, 180, 190, 140, 200],
            borderColor: 'blue',
            backgroundColor: 'rgba(0, 0, 255, 0.2)',
            tension: 0.4
        },
        {
            label: 'Contract ID: 248136',
            data: [150, 140, 180, 220, 210],
            borderColor: 'green',
            backgroundColor: 'rgba(0, 128, 0, 0.2)',
            tension: 0.4
        }
    ]
};

const config = {
    type: 'line',
    data: data,
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: true,
                position: 'bottom'
            }
        },
        scales: {
            y: {
                beginAtZero: false,
                ticks: {
                    callback: function(value) {
                        return '$' + value;
                    }
                }
            }
        }
    }
};

window.myContractsChart = new Chart(ctx, config);
}

function loadEducatorsChart(chartData) {
    const ctx = document.getElementById('educatorsChart').getContext('2d');

    // Check if chart instance already exists and destroy it
    if (window.myEducatorsChart instanceof Chart) {
        console.log(window.myEducatorsChart);
        window.myEducatorsChart.destroy();
    }


    const generateUniqueColors = (count) => {
        const colors = [];
        const hueStep = 360 / count;

        for(let i = 0; i < count; i++) {
            const hue = Math.round((i * hueStep) % 360);
            const saturation = 70 + Math.random() * 15;
            const lightness = 50 + Math.random() * 10;
            colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
        }
        return colors;
    }

    const totalAmount = chartData.amounts.reduce((sum, amount) => sum + parseFloat(amount.replace(/[^0-9.]/g, '') || 0), 0);

    // Calculate percentage values
    const percentageData = chartData.amounts.map(amount => ((parseFloat(amount.replace(/[^0-9.]/g, '') || 0) / totalAmount) * 100).toFixed(2));
    console.log(percentageData);

    const data = {
        labels: chartData.labels.map((label, index) =>
            `${label}\n${percentageData[index]}% | ${chartData.amounts[index]}`
        ),
        datasets: [{
            data: percentageData, // Percentages
            // backgroundColor: [
            //     '#C084FC', // Purple
            //     '#2563EB', // Blue
            //     '#FB923C', // Orange
            //     '#22C55E'  // Green
            // ],
            backgroundColor: generateUniqueColors(chartData.labels.length),
            borderWidth: 2
        }]
    };

    const config = {
        type: 'doughnut', // Doughnut chart
        data: data,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        boxWidth: 20,
                        padding: 15,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {

                        label: function(tooltipItem) {
                            // let labels = chartData.labels;
                            // let values = chartData.amounts;
                            // let percentage = chartData.percentageData;
                            // return `${chartData.labels[tooltipItem.dataIndex]}: ${chartData.amounts[tooltipItem.dataIndex]}`;
                            let label = chartData.labels[tooltipItem.dataIndex];
                            let amount = chartData.amounts[tooltipItem.dataIndex];
                            let percentage = percentageData[tooltipItem.dataIndex]; // Get percentage

                            return `${label}: ${amount} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    };

    window.myEducatorsChart = new Chart(ctx, config);
}



// function loadEducatorsChart() {
//     const ctx = document.getElementById('educatorsChart').getContext('2d');

//     const data = {
//         labels: ['Junir K', 'Martin L.', 'Jeniffer K.', 'Sam J.'],
//         datasets: [{
//             data: [30, 40, 20, 10], // Percentages
//             backgroundColor: [
//                 '#C084FC', // Purple
//                 '#2563EB', // Blue
//                 '#FB923C', // Orange
//                 '#22C55E'  // Green
//             ],
//             borderWidth: 2
//         }]
//     };

//     const config = {
//         type: 'doughnut', // Doughnut chart
//         data: data,
//         options: {
//             responsive: true,
//             plugins: {
//                 legend: {
//                     display: true,
//                     position: 'bottom'
//                 },
//                 tooltip: {
//                     callbacks: {
//                         label: function(tooltipItem) {
//                             let labels = ['Junir K', 'Martin L.', 'Jeniffer K.', 'Sam J.'];
//                             let values = ['$358', '$558', '$228', '$200'];
//                             return `${labels[tooltipItem.dataIndex]}: ${values[tooltipItem.dataIndex]}`;
//                         }
//                     }
//                 }
//             }
//         }
//     };

//     new Chart(ctx, config);
// }


$('#classesTab .class-tab-link').on('click', function() {
    $('#classesTab .class-tab-link').removeClass('active'); // Remove active from all tabs
    $(this).addClass('active'); // Add active to clicked tab

    $('#classesDataTable_filter input[type=search]').val('');
    $('.class-search-input').val('');
    let selectedTab = $(this).text().trim();
    let url = APP_URL + "/all-classes";
    let type = "";

    if (selectedTab === "Today’s Classes") {
        type = 'today';
    } else if (selectedTab === "In Progress") {
        type = 'inprogress';
    } else if (selectedTab === "Upcoming") {
        type = 'upcoming';
    } else if (selectedTab === "Completed") {
        type = 'completed';
    }

    if ($.fn.DataTable.isDataTable('#classesDataTable')) {
        $('#classesDataTable').DataTable().destroy();
        $('#classesDataTable tbody').empty(); // Clear old data
    }

    loadClassesDatatable(url, type);

});

function loadClassesDatatable(url, type){
    let columns = [
        { data: 'id', visible: false },
        { data: 'school_name', searchable: false, orderable: false },
        { data: 'subject' },
        { data: 'grade_level' },
        { data: 'delivery_mode' },
        { data: 'start_time', searchable: false, },
        { data: 'end_time', searchable: false, },
        { data: 'teacher' },
        { data: 'proctor', searchable: false },
        { data: 'action', searchable: false, orderable: false },
    ];

    let columnDefs = [
        { targets: 5, data: 'start_time' }, // Start Time
        { targets: 6, data: 'end_time' }, // End Time
    ];

    if (type !== 'today') {
        columns[5] = { data: 'start_date' };
        columns[6] = { data: 'end_date' };

        columnDefs = [
            { targets: 5, data: 'start_date' }, // Start Date
            { targets: 6, data: 'end_date' }, // End Date
        ];

        // Update column headers in the table
        $('#classesDataTable thead th').eq(5).text('Start Date');
        $('#classesDataTable thead th').eq(6).text('End Date');
    } else {
        // Reset headers if it's today
        $('#classesDataTable thead th').eq(5).text('Start Time');
        $('#classesDataTable thead th').eq(6).text('End Time');
    }

    $('#classesDataTable').DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'GET',
        'autoWidth': true,
        'searching': true,
        'ordering': true,
        'ajax': {
            'headers': {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            'url': url,
            'data': function (data) {
                data.type = type;
            }

        },
        'columns': columns,
        'columnDefs': columnDefs,
        'order': [
            [0, 'desc']
        ],
        'drawCallback': function(settings) {
            var table = $(this).DataTable();
            var showPagination = table.page.info().recordsTotal > 10;
            $('#classesDataTable_paginate').toggle(showPagination);
        }
    });
}

$("#spend_by_contract").on("submit", function(e) {
    e.preventDefault();


    var query_search_contract=$("#searchInput_contract").val();
    console.log(query_search_contract);


    //this input will carry the searched value
    var hiddenInput = $('<input>', {
        type: 'hidden', // Make it hidden so it won't be visible on the page
        name: 'search', // The name must match the field name on the server side
        value:query_search_contract  // Set the value from the outside input
    });
    // Append the hidden input to the form
    $(this).append(hiddenInput);

    // Submit the form now that the hidden input is added
    this.submit();
});
$("#spend_by_educator").on("submit",function(e){

    e.preventDefault();
    console.log("inside the educator");

    var query_search_instructor=$("#searchInput_educators").val();

     var hiddenInput = $('<input>',
    {
        type: 'hidden', // Make it hidden so it won't be visible on the page
        name: 'search', // The name must match the field name on the server side
        value:query_search_instructor,  // Set the value from the outside input
    });

    $(this).append(hiddenInput);


    this.submit();

});

$('body').on('keyup', '.class-search-input', function ()
{
    let searchInput = $('#classesDataTable_filter input[type=search]');
    searchInput.val($(this).val()).trigger('keyup');
})

$(document).on("click",".icofont-navigation-menu",function(){
  $(".mobile_sidebar").toggleClass("show_mobile_sidebar");

});

//this is for the dynamic searach bar
let searchTimeout; // Declare a global variable for the timeout

// $("#searchbarschools").on("keydown", function (e)
// {
//     // Hide dropdown immediately if input is empty
//     setTimeout(() => {
//         if ($("#searchbarschools").val().trim() === "") {
//             $(".searching_results").css("display", "none");
//         }
//     }, 10); // Slight delay to let the keypress update the input field

//     // If Enter is pressed
//     if (e.keyCode === 13)
//         {
//         e.preventDefault(); // Prevent form submission

//         var searchQuery = $("#searchbarschools").val().trim();

//         if (searchQuery !== "") {
//             var url = APP_URL + '/get_dynamic_school_data';
//             var csrfToken = $('meta[name="csrf-token"]').attr('content');

//             $.ajax({
//                 url: url,
//                 method: "POST",
//                 data: {
//                     query: searchQuery
//                 },
//                 headers: {
//                     'X-CSRF-TOKEN': csrfToken
//                 },
//                 success: function (response) {
//                     console.log(response);

//                     $(".list_of_searching").empty();
//                     $(".searching_results").css("display", "").show();

//                     if (response.length === 0) {
//                         $(".list_of_searching").append(`
//                             <span class="text-danger px-3" style="font-weight:500">No results found</span>
//                         `);
//                         return;
//                     }

//                     $.each(response, function(index, instructor) {
//                         $(".list_of_searching").append(`
//                             <a href="${instructor.href}">
//                                 <li class="w-100 text-start my-1 py-1 searching_list_item">
//                                     <div class="d-flex gap-3">
//                                         <img style="border-radius:15px" height="35px" width="35px" src='${instructor.image}' />
//                                         <div>
//                                             <p class="mb-0">${instructor.title}</p>
//                                             <p class="mb-0 w-100" style="font-size:13px">
//                                                 ${instructor.description.split(" ").slice(0, 7).join(" ") + (instructor.description.split(" ").length > 4 ? "..." : "")}
//                                             </p>
//                                         </div>
//                                     </div>
//                                 </li>
//                             </a>
//                         `);
//                     });
//                 },
//                 error: function (xhr, status, error) {
//                     console.error("Error:", status, error);
//                 }
//             });
//         }
//     }
// });

// Toggle the visibility of the dropdown
$(".dropdownicon").on("click",function(){

    if ($(".dropdownlist_schools").is(":visible")) {
        $(".dropdownlist_schools").hide();
    } else {
        $(".dropdownlist_schools").show();
    }



})


//this is for the Hire section of the header

    // Toggle the dropdown menu on button click
    $('#hire_dropdown_button').click(function(event) {
        $(this).tooltip('hide')
        // Prevents the click from propagating to the document
        $(".hire_dropdown").toggleClass("display_none");
    });

    // Hide the dropdown if clicked outside
    $(document).click(function(event) {
        // Check if the click is outside both #hire_dropdown_button, .dropdown-item_hire, and .hire_dropdown
        if (!$(event.target).closest('#hire_dropdown_button').length && !$(event.target).closest('.dropdown-item_hire').length && !$(event.target).closest('.hire_dropdown').length) {
            $('.hire_dropdown').addClass('display_none'); // Add the class to hide the dropdown
        }
    });



//this is for the manage section of the header
$("#manage_dropdown_button").on("click",function(){
    $(".manage_dropdown").toggleClass('display_none');
    $(this).tooltip('hide')
})

$(document).click(function(event) {
    // Check if the click is outside both #hire_dropdown_button, .dropdown-item_hire, and .hire_dropdown
    if (!$(event.target).closest('#manage_dropdown_button').length && !$(event.target).closest('.dropdown-item_manage').length && !$(event.target).closest('.hire_dropdown').length) {
        $('.manage_dropdown').addClass('display_none'); // Add the class to hide the dropdown
    }
});

//this is for the insight section of the hedader
$("#insight_dropdown_button").on("click",function(){

    $(".insight_dropdown").toggleClass('display_none');
    $(this).tooltip('hide')



})
$(document).click(function(event) {
    // Check if the click is outside both #hire_dropdown_button, .dropdown-item_hire, and .hire_dropdown
    if (!$(event.target).closest('#insight_dropdown_button').length && !$(event.target).closest('.insight_dropdown').length && !$(event.target).closest('.hire_dropdown').length) {
        $('.insight_dropdown').addClass('display_none'); // Add the class to hide the dropdown
    }
});

//for the save and continue in the post requirement step
$("#nextTabButton").on("click",function(){
    console.log("clicked");
    $(this).tooltip('hide')



})


//for the hide of the search bar on clicking the screen outside
$(document).click(function(event) {
    // Check if the click is outside both #hire_dropdown_button, .dropdown-item_hire, and .hire_dropdown
    if (!$(event.target).closest('.searching_results').length &&  !$(event.target).closest('#searchbarschools').length ) {
        $('.searching_results').hide(); // Add the class to hide the dropdown
    }
});

//this is for the Ajax when we hit  enter and the click on search_icon
// function performSchoolSearch() {

//     var searchQuery = $("#searchbarschools").val().trim();

//     if (searchQuery === "") {
//         $(".searching_results").css("display", "none");
//         return;
//     }

//     var url = APP_URL + '/get_dynamic_school_data';
//     var csrfToken = $('meta[name="csrf-token"]').attr('content');

//     $.ajax({
//         url: url,
//         method: "POST",
//         data: { query: searchQuery },
//         headers: { 'X-CSRF-TOKEN': csrfToken },
//         success: function (response) {
//             console.log(response);

//             $(".list_of_searching").empty();
//             $(".searching_results").css("display", "").show();

//             if (response.length === 0) {
//                 $(".list_of_searching").append(`
//                     <span class="text-danger px-3" style="font-weight:500">No results found</span>
//                 `);
//                 return;
//             }

//             $.each(response, function(index, instructor) {
//                 $(".list_of_searching").append(`
//                     <a href="${instructor.href}">
//                         <li class="w-100 text-start my-1 py-1 searching_list_item">
//                             <div class="d-flex gap-3">
//                                 <img style="border-radius:15px" height="35px" width="35px" src='${instructor.image}' />
//                                 <div>
//                                     <p class="mb-0">${instructor.title}</p>
//                                     <p class="mb-0 w-100" style="font-size:13px">
//                                         ${instructor.description.split(" ").slice(0, 7).join(" ") + (instructor.description.split(" ").length > 4 ? "..." : "")}
//                                     </p>
//                                 </div>
//                             </div>
//                         </li>
//                     </a>
//                 `);
//             });
//         },
//         error: function (xhr, status, error) {
//             console.error("Error:", status, error);
//         }
//     });
// }

// $("#searchbarschools").on("keydown", function (e) {
//     setTimeout(() => {
//         if ($("#searchbarschools").val().trim() === "") {
//             $(".searching_results").css("display", "none");
//         }
//     }, 10);

//     if (e.keyCode === 13) {
//         e.preventDefault();
//         performSchoolSearch();
//     }
// });

// $(".search-icon").on("click", function () {
//     performSchoolSearch();
// });


//this is the view requirement dropdown icon
$(document).on("click", ".view_dropdown_icon", function () {
    const $this = $(this);
    $(this).toggleClass("rotate-180");

  });


// This is  the outside searach from the dashboard
$(document).ready(function() {
    $('#outsidesearchform').on('submit', function(e) {
        const lastSegment = window.location.pathname.split('/').filter(Boolean).pop();

        if (lastSegment === 'profile') {
            e.preventDefault(); // Block form submission if on 'profile'
        }
    });
});





//this is for the dynamic search for the main searchbar
let debounceTimer;



$("#searchbarschools").on("keyup", function () {
    $(".search-icon").hide();
    $(".remove_icon_main_search").show();

    const searchValue = $("#searchbarschools").val();
    const lastSegment = window.location.pathname.split('/').filter(Boolean).pop();
    const url = APP_URL + "/profile";
    const csrfToken = $('meta[name="csrf-token"]').attr('content');

    clearTimeout(debounceTimer); // clear previous timer before setting a new one

    if (searchValue === "") {
        $(".search-icon").show();
        $(".remove_icon_main_search").hide();
        $(".search_is_empty").show();
        $(".search_without_empty").hide();

        if (lastSegment === 'profile') {
            debounceTimer = setTimeout(function () {
                SortApplicants(null, null, url, searchValue);
            }, 1000); // 1 second debounce for empty search
        }

    } else {
        if (lastSegment === 'profile') {
            debounceTimer = setTimeout(function () {
                SortApplicants(null, null, url, searchValue);
            }, 1000); // 1 second debounce for typing
        }
    }
});


document.addEventListener("DOMContentLoaded", function () {
    const searchIcon = document.querySelector(".search-icon i");
    const form = searchIcon.closest("form");

    searchIcon.addEventListener("click", function () {
        form.submit();
    });
});

//this is for clicking on   the cross button of the searched result above sorting and filter
$(document).on("click","#searchparameter",function()
{
    $(".remove_icon_main_search").hide();
    $(".search-icon").show();

    var url = APP_URL + "/profile";
    var csrfToken = $('meta[name="csrf-token"]').attr('content');
    $("#searchbarschools").val("");

    SortApplicants(null, null, url, $("#searchbarschools").val());


})

  //this is when page reloads cross icon div should not appear
$(document).ready(function () {
    const navEntries = performance.getEntriesByType("navigation");
    if (navEntries.length > 0 && navEntries[0].type === "reload") {
      $("#searchparameter").hide();
      $(".heading_for_resuslts").hide();
      $(".search_is_empty").show();
      $(".search_without_empty").hide();
    }
});

$(document).on("change", "#per_hour", function() {
    const value = $(this).val();
    console.log(value);


   if(value=="fixed"){
    $(".hour_text").hide();

   }else{
    $(".hour_text").show();

   }


    if (value =="Based on candidates qualification") {
        console.log("display none");
        $(".budget_div").addClass("display_none");

    } else {
        $(".budget_div").removeClass("display_none");

    }
});
// this is for the only number input
$(document).ready(function () {
    $(document).on("keydown", ".money-input", function (e) {
        const cursorPos = this.selectionStart;

        // Prevent backspace deleting the $
        if (cursorPos === 1 && e.key === "Backspace") {
            e.preventDefault();
        }

        // Allow navigation, backspace (if not at $), delete, tab, etc.
        const allowedKeys = [
            "Backspace", "Delete", "ArrowLeft", "ArrowRight", "Tab", "Escape"
        ];
        if (
            allowedKeys.includes(e.key) ||
            (e.ctrlKey || e.metaKey && ["a", "c", "v", "x"].includes(e.key.toLowerCase()))
        ) {
            return;
        }

        // Block non-number keys
        if (!e.key.match(/^[0-9]$/)) {
            e.preventDefault();
        }
    });

    $(document).on("input", ".money-input", function () {
        // Ensure value starts with "$" and only digits after
        const val = $(this).val().replace(/[^0-9]/g, '');
        $(this).val('$' + val);
    });

    $(document).on("focus click", ".money-input", function () {
        // Force cursor to stay after the $
        if (this.selectionStart < 1) {
            this.setSelectionRange(1, 1);
        }
    });

    // Initialize inputs if empty
    $(".money-input").each(function () {
        if (!$(this).val().startsWith('$')) {
            $(this).val('$');
        }
    });
    $(document).on("click", ".save_icon_new", function ()
    {

        $(".svg_new").toggleClass("save_icon_background-color");
    })


});

//this is for the cros of the search_icon
$(".remove_icon_main_search").on("click",function(){
    $("#searchbarschools").val('');
   $(".remove_icon_main_search").hide();
   $(".search-icon").show();


   var url = APP_URL + "/profile";
    var csrfToken = $('meta[name="csrf-token"]').attr('content');
    $("#searchbarschools").val("");

    SortApplicants(null, null, url, $("#searchbarschools").val());
})



$(document).on("click",".modalnew",function(){
    $("#exampleModal").modal("show");
});
$(document).on("click",".close_save_profile",function(){

    $("#exampleModal").modal("hide");
})

$('#discover_search').on('keyup', function (e) {
    let route = APP_URL + `/discover`;
    const searchText = $(this).val().trim();
    SortApplicants(null, null, route, searchText);
});


function showUserMessage(userId)
{
    let url = APP_URL + '/messages-screen';
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'JSON',
        data: {
            userId: userId,
        },
        success: function(response) {
            if (response.success) {
                $('.messages-container').html('');
                $('.messages-container').html(response.html);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading user card:', error);
        }
    });
}


// RECORD VIDEO JS

let videoSource = null;
$('body').on('click', '#recordVideo', function() {
    $('#VideoRecordModal').modal('show');
    videoSource = new VideoRecorderService()
    videoSource.setupUI('profile_video_record_container');
})

$('#VideoRecordModal').on('hidden.bs.modal', function () {
    if (typeof videoSource !== undefined) {
        videoSource.stopRecording();
        videoSource.stopAllStreams();
    }
});

//this is for the click on the notificaion  icon
$(document).on("click",".notification",function(){
    console.log("we are inside the notification");
    $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
   $.ajax({

    url:APP_URL +"/notfication_read",
    method:"post",
    success:function(response){{
          if(response.status=="success"){
            console.log(response)
             console.log(response.notifications_data);
             $(".requirement_notification_dropdown_list").empty();
             response.notifications_data.forEach(function(notification,index){

                $(".requirement_notification_dropdown_list").append(`<li class='notification_li' style="font-size:12px;padding:15px 13px">${notification.content}</li><hr style="margin:1px 0px" class="w-100">`)



             });


             $(".count_notification").hide();
             let dropdown = $(".requirement_notification_dropdown");

             if (dropdown.hasClass("display_none")) {
                 dropdown
                     .removeClass("display_none")
                     .hide() // to avoid sudden jump
                     .slideDown(300);
             } else {
                 dropdown.slideUp(300, function () {
                     dropdown.addClass("display_none");
                 });
             }
          }

    }
    }

   })

})

//removeal of tooltip in notification
$('.tooltipnotification').on('click', function () {
    $(this).tooltip('hide'); // hide the tooltip
    $(this).blur();          // remove focus to get rid of border
});
$(document).on('click', function (e) {
    if (!$(e.target).closest('.tooltipnotification, .requirement_notification_dropdown').length) {
        const dropdown = $('.requirement_notification_dropdown');
        if (!dropdown.hasClass('display_none')) {
            dropdown.slideUp(200, function () {
                dropdown.addClass('display_none');
            });
        }
    }
});

//this is for removing the tooltip
$(document).on('click', '[data-bs-toggle="tooltip"]', function () {
    var tooltip = bootstrap.Tooltip.getInstance(this); // Get the tooltip instance
    if (tooltip) {
        tooltip.hide(); // Hide the tooltip
    }
});
$(document).on("click", ".requirement_dropdown", function() {
    $(".requirement_dropdown_div").slideToggle(300); // Smooth toggle
});

//for the prevention of tab button
document.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
        e.preventDefault(); // Prevent default tab behavior

        // Get all focusable elements
        const focusable = Array.from(document.querySelectorAll(
            'a[href], button, textarea, input, select, [tabindex]:not([tabindex="-1"])'
        )).filter(el => !el.disabled && el.offsetParent !== null);

        if (focusable.length === 0) return;

        const currentIndex = focusable.indexOf(document.activeElement);
        const nextIndex = e.shiftKey ? currentIndex - 1 : currentIndex + 1;
        const nextEl = focusable[(nextIndex + focusable.length) % focusable.length];

        if (nextEl) nextEl.focus({ preventScroll: true }); // Focus without scrolling
    }
});


//for the checkbox to be checked

$(document).on('change', '.valid_till_check_box', function() {
    if ($(this).is(":checked")) {
      console.log("Checked");
    } else {
      console.log("Not checked");
    }
  })

  //this is fo the save user dashboard
function SaveUserDashboard(elm)
{
    const instructorId = $(elm).data("instructor-id");
    console.log(instructorId);

// Use backticks for template literals so ${instructorId} gets evaluated
$(`#exampleModalCenter2dashobard${instructorId}`).modal("show");
}

 $(document).on("click",".newmodal",function(){

  $("#exampleModal").modal("show");

 })

// document.querySelectorAll('.status-btn').forEach(btn => {
//     btn.addEventListener('click', function () {
//         document.querySelectorAll('.status-btn').forEach(b => {
//             b.classList.remove('btn-link');
//         });
//         this.classList.add('btn-link');
//         // Remove focus to avoid focus styles
//         this.blur();
//     });
// });

$('body').on('keydown', '.stop-page-scroll', function(e) {
    // List of keys we want to block
    const invalidKeys = ['-', '+', '*', '/', 'e', 'E'];

    if (invalidKeys.includes(e.key)) {
        e.preventDefault(); // Block character
    }
});


$('body').on('change', '.radio_class_sch', function () {
    const selectedValue = $(this).val();
    if (selectedValue == 'regular') {
        $('.regularSchedule').removeClass('d-none');
        $('.alternatingSchedule').addClass('d-none');
        $('.otherSchedule').addClass('d-none');
        $('#alternating-schedule-1').val('');
        $('#alternating-schedule-2').val('');
        $('.other-remove-opt').html(`Upload teacher schedule screenshot <span class="teach_schedule_screenshot_opt">(Optional)</span>`);

    } else if (selectedValue == 'alternating') {
        $('.regularSchedule').addClass('d-none');
        $('.alternatingSchedule').removeClass('d-none');
        $('.otherSchedule').addClass('d-none');
        $('#regular-schedule-days').val('');
        updateSelectedDays('alternating-schedule-1');
        updateSelectedDays('alternating-schedule-2');
        $('.other-remove-opt').html(`Upload teacher schedule screenshot <span class="teach_schedule_screenshot_opt">(Optional)</span>`);

    } else if (selectedValue == 'other') {
        $('.regularSchedule').addClass('d-none');
        $('.alternatingSchedule').addClass('d-none');
        $('.otherSchedule').removeClass('d-none');
        $('#regular-schedule-days').val('');
        $('#alternating-schedule-1').val('');
        $('#alternating-schedule-2').val('');
        $('.other-remove-opt').html(`Upload teacher schedule screenshot<span class="text-danger">*</span>`);

    }
});

const ScheduleDayMap = {
        "MON": "Monday",
        "TUE": "Tuesday",
        "WED": "Wednesday",
        "THU": "Thursday",
        "FRI": "Friday",
        "SAT": "Saturday",
        "SUN": "Sunday"
    };

// Function to update the input field with selected days (full names)
function updateSelectedDays(scheduleId) {
    let selectedDays = [];

    // Get all selected days for the given schedule
    $('#' + scheduleId + ' .days-list-select').each(function () {
        // Get the full day name by mapping the abbreviated day
        const fullDayName = ScheduleDayMap[$(this).text().trim()];
        if (fullDayName) {
            selectedDays.push(fullDayName);
        }
    });

    if (!$('.regularSchedule').hasClass('d-none')) {
        // Update the corresponding input field with the array of full day names
        $('#' + scheduleId + '-days').val(JSON.stringify(selectedDays));
    } else if (!$('.alternatingSchedule').hasClass('d-none')) {
        // Update the corresponding input field with the array of full day names
        $('#' + scheduleId + '-days').val(JSON.stringify(selectedDays));
    }
    // $('#' + scheduleId + '-days').val(JSON.stringify(selectedDays));
}

// Handle the click event on the days in any schedule
$('body').on('click', '.days-list', function () {
    if (!$(this).hasClass('days-list-disabled')) {
        // Toggle the 'days-list-select' class
        $(this).toggleClass('days-list-select');

        // Update the corresponding schedule input field with the selected days
        if ($(this).closest('#regular-schedule').length) {
            updateSelectedDays('regular-schedule');
        } else if ($(this).closest('#alternating-schedule-1').length) {
            updateSelectedDays('alternating-schedule-1');
            const dayText = $(this).text().trim();
            console.log(dayText);
            if ($(this).hasClass('days-list-select')) {
                $('#alternating-schedule-2 .days-list').each(function () {
                    if ($(this).text().trim() === dayText) {
                        $(this).removeClass('days-list-select');
                    }
                });
                updateSelectedDays('alternating-schedule-2');
            } else {
                $('#alternating-schedule-2 .days-list').each(function () {
                    if ($(this).text().trim() === dayText) {
                        $(this).addClass('days-list-select');
                    }
                });
                updateSelectedDays('alternating-schedule-2');
            }
        } else if ($(this).closest('#alternating-schedule-2').length) {
             const dayText = $(this).text().trim();
            if ($(this).hasClass('days-list-select')) {
                $('#alternating-schedule-1 .days-list').each(function () {
                    if ($(this).text().trim() === dayText) {
                        $(this).removeClass('days-list-select');
                    }
                });
            } else {
                $('#alternating-schedule-1 .days-list').each(function () {
                    if ($(this).text().trim() === dayText) {
                        $(this).addClass('days-list-select');
                    }
                });
            }
            updateSelectedDays('alternating-schedule-1');
        }
    }
});

function ScreenshotUpload(_this) {
    console.log($(_this).closest('div').find('input[type="file"]'));
    let fileInput = $(_this).closest('div').find('input[type="file"]');
    let fileErr = $(_this).closest('div').find('.file_err');
    fileInput.click();

    fileInput.on('change', function() {
        // Get the selected file's name
        let file = this.files && this.files[0];

        if (file) {
            let fileName = file.name;
            let fileExtension = fileName.split('.').pop().toLowerCase(); // Get file extension in lowercase
            let fileSize = file.size / 1024 / 1024; // Convert size to MB
            let displayName = fileName;

            if (fileName.length > 12) {
                let dotIndex = fileName.lastIndexOf('.');
                let namePart = fileName.substring(0, dotIndex);
                let extPart = fileName.substring(dotIndex + 1);

                displayName = namePart.substring(0, 12) + '...';
            }
            // Define allowed file extensions
            let allowedExtensions = ['jpg', 'jpeg', 'png', 'bmp', 'webp', 'tiff'];

            // Check if the file extension is valid
            if (allowedExtensions.indexOf(fileExtension) === -1) {
                // Invalid file type (if it's not in allowedExtensions)
                fileErr.text("Invalid file type. Please upload an image (jpg, jpeg, png, bmp, webp, tiff).");
                fileInput.val(''); // Clear the file input
                return; // Stop the process
            }

            // Check if the file size is greater than 1MB
            if (fileSize > 1) {
                fileErr.text("File size exceeds 1MB. Please upload a smaller file.");
                fileInput.val(''); // Clear the file input
                return; // Stop the process
            }

            // Create the SVG markup (the one you provided)
            let svgIcon = `
                <svg width="12" height="13" onclick="removeScreenshot(this)" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.00683 8.24984L10.048 12.6228C10.2624 12.8548 10.5533 12.9852 10.8566 12.9852C11.1598 12.9852 11.4507 12.8548 11.6651 12.6228C11.8795 12.3907 12 12.076 12 11.7479C12 11.4197 11.8795 11.105 11.6651 10.873L7.62239 6.5L11.6643 2.12705C11.7705 2.01215 11.8546 1.87576 11.9121 1.72566C11.9695 1.57557 11.999 1.4147 11.999 1.25225C11.9989 1.0898 11.9693 0.928949 11.9118 0.77888C11.8544 0.628811 11.7701 0.492463 11.6639 0.377621C11.5578 0.262779 11.4317 0.171692 11.293 0.109561C11.1543 0.0474296 11.0056 0.0154706 10.8555 0.0155089C10.7054 0.0155472 10.5567 0.0475818 10.4181 0.109784C10.2794 0.171986 10.1534 0.263137 10.0472 0.378034L6.00683 4.75098L1.96566 0.378034C1.86026 0.259842 1.73417 0.165547 1.59474 0.10065C1.45531 0.0357541 1.30533 0.00155596 1.15355 5.18716e-05C1.00177 -0.00145222 0.85124 0.0297676 0.710733 0.0918897C0.570226 0.154012 0.442559 0.245792 0.335182 0.361875C0.227805 0.477959 0.142868 0.61602 0.0853262 0.768004C0.0277848 0.919988 -0.00120828 1.08285 3.85728e-05 1.24709C0.00128543 1.41133 0.0327471 1.57365 0.0925883 1.72459C0.152429 1.87553 0.239451 2.01207 0.348577 2.12622L4.39128 6.5L0.349339 10.8738C0.240214 10.9879 0.153192 11.1245 0.0933506 11.2754C0.0335095 11.4263 0.00204743 11.5887 0.000800572 11.7529C-0.000446285 11.9171 0.0285468 12.08 0.0860882 12.232C0.14363 12.384 0.228567 12.522 0.335944 12.6381C0.443321 12.7542 0.570988 12.846 0.711495 12.9081C0.852002 12.9702 1.00254 13.0015 1.15431 12.9999C1.30609 12.9984 1.45607 12.9642 1.5955 12.8993C1.73493 12.8345 1.86102 12.7402 1.96642 12.622L6.00683 8.24984Z" fill="#787777"></path>
                </svg>
            `;

            // Replace the content (button or div) with the file name and SVG
            $(_this).text(displayName);  // Replace the button text with the file name
            $(_this).removeAttr('onclick');
            $(_this).append(svgIcon).css('background-color', 'transparent').css('color', '#000').css('border', '1px solid #004cbd').css('gap', '9px');  // Add the SVG next to the file name
        }
    });
}

function removeScreenshot(_this) {
    let container = $(_this).closest('div');

    updateUploadScreenshotModal(
        "Delete File",
        "This action will permanently remove the uploaded file. Are you sure you want to delete it?",
        "Delete",
        function () {
            let fileInput = container.find('input[type="file"]');
            let fileErr = container.find('.file_err');

            // Clear file input and error message
            fileInput.val('');
            fileErr.text('');
            fileInput.attr('data-val', '');

            // Clear other input values inside the same container
            container.find('input:not([type="file"])').val('');
            fileErr.text('');

            // Reset upload button appearance and re-bind click
            let originalText = "Upload Screenshot";
            let buttonHtml = `<span class="arrow-icon">
                <svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.14848 2.83727L1.94762 6.9274C1.77615 7.09435 1.57611 7.17448 1.34749 7.1678C1.11887 7.16112 0.918833 7.07403 0.747369 6.90653C0.590194 6.73959 0.507891 6.54482 0.500461 6.32223C0.493031 6.09964 0.575334 5.90487 0.747369 5.73792L6.40568 0.228765C6.49141 0.145293 6.58429 0.0860275 6.68431 0.0509692C6.78433 0.015911 6.89149 -0.00106159 7.0058 5.13677e-05C7.12011 0.00116433 7.22728 0.0186932 7.3273 0.0526386C7.42732 0.0865839 7.52019 0.145571 7.60593 0.2296L13.2642 5.73876C13.4214 5.89179 13.5 6.08322 13.5 6.31305C13.5 6.54287 13.4214 6.74098 13.2642 6.90737C13.0928 7.07431 12.889 7.15778 12.653 7.15778C12.4169 7.15778 12.2134 7.07431 12.0426 6.90737L7.86312 2.83727L7.86312 12.1653C7.86312 12.4018 7.78082 12.6002 7.61621 12.7604C7.45161 12.9207 7.24814 13.0006 7.0058 13C6.76347 12.9994 6.55971 12.9193 6.39453 12.7596C6.22936 12.5999 6.14734 12.4018 6.14848 12.1653L6.14848 2.83727Z" fill="white"></path>
                </svg>
            </span> ${originalText}`;

            let button = container.find('button');
            button.css({
                'background-color': '#004cbd',
                'color': '#fff',
                'border': 'none',
                'gap': ''
            });
            button.html(buttonHtml);

            // ✅ Reattach the ScreenshotUpload handler to the button itself
            button.attr('onclick', 'ScreenshotUpload(this)');
        }
    );

    // Close modal cleanup
    $('.modal').modal('hide');
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
}

function updateUploadScreenshotModal(
    title,
    text,
    yesButtonText,
    successCallback,
    cancelButtonText = "Cancel",
    cancelButtonVisible = true
) {
    const modalElement = $('#close-model');

    modalElement.find(".modal-title").text(title);
    modalElement.find(".modal-body").html(text);

    const cancelBtn = modalElement.find(".modal-cancel");
    const confirmBtn = modalElement.find(".modal-confirm");

    if (cancelButtonVisible) {
        cancelBtn.text(cancelButtonText).show();
    } else {
        cancelBtn.hide();
    }

    confirmBtn.text(yesButtonText);

    // Remove old click events
    cancelBtn.off("click");
    confirmBtn.off("click");

    // Bind new click events
    cancelBtn.on("click", () => {
        modalElement.modal("hide");
    });

    confirmBtn.on("click", () => {
        if (typeof successCallback === "function") {
            successCallback();
        }
        modalElement.modal("hide");
    });

    modalElement.modal("show");
}

$('body').on('change', '#schedule_start_time, #schedule_end_time, #schedule_1_start_time, #schedule_2_start_time, #class_duration', function() {
    let startTime = $('#schedule_start_time').val();
    let schedule1startTime = $('#schedule_1_start_time').val();
    let schedule2startTime = $('#schedule_2_start_time').val();
    // let endTime = $('#schedule_start_time').val();
    let classDuration = parseInt($('#class_duration').val());

    if (startTime != '' && !isNaN(classDuration)) {
        // Convert startTime (string) to Date object
        let start = parseTime(startTime);
        // Add duration in minutes
        start.setMinutes(start.getMinutes() + classDuration);
        // Format the new time as "h:mm A"
        let endTime = formatTime(start);
        // Set it to the end time input
        $('#schedule_end_time').val(endTime);
    }

    if (schedule1startTime != '' && !isNaN(classDuration)) {
        // Convert startTime (string) to Date object
        let start = parseTime(schedule1startTime);
        // Add duration in minutes
        start.setMinutes(start.getMinutes() + classDuration);
        // Format the new time as "h:mm A"
        let endTime = formatTime(start);
        // Set it to the end time input
        $('#schedule_1_end_time').val(endTime);
    }

    if (schedule2startTime != '' && !isNaN(classDuration)) {
        // Convert startTime (string) to Date object
        let start = parseTime(schedule2startTime);
        // Add duration in minutes
        start.setMinutes(start.getMinutes() + classDuration);
        // Format the new time as "h:mm A"
        let endTime = formatTime(start);
        // Set it to the end time input
        $('#schedule_2_end_time').val(endTime);
    }
});

// Utility function to parse time string (e.g. "10:00 AM") into a Date object
function parseTime(timeStr) {
    let [time, modifier] = timeStr.split(' ');
    let [hours, minutes] = time.split(':').map(Number);

    if (modifier === 'PM' && hours !== 12) {
        hours += 12;
    }
    if (modifier === 'AM' && hours === 12) {
        hours = 0;
    }

    let date = new Date();
    date.setHours(hours);
    date.setMinutes(minutes);
    date.setSeconds(0);
    return date;
}

// Utility function to format Date object into "h:mm A"
function formatTime(date) {
    let hours = date.getHours();
    let minutes = date.getMinutes();
    let ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // hour 0 should be 12
    minutes = minutes < 10 ? '0' + minutes : minutes;

    return `${hours}:${minutes} ${ampm}`;
}

$('body').on('input', '#numberOfStudents', function() {
    if (this.value < 0) {
        this.value = 0;
    }
});
// #region calculate budget
$(document).on("click","#calculate_budget_div",function(){
    
    $("#budgetcalculatormodal").modal("show");
   

});
$(document).on("click",".close_budgetcontent_modal",function(){


$("#budgetcalculatormodal").modal("hide");

});

$(document).on("click", "#post_requirement_submit_budget", function (e) {
    let hasError = false;

    $(".error_calculate_budget").each(function () {
        const $el = $(this);
        const tag = $el.prop("tagName").toLowerCase();
        const type = $el.attr("type");

        let isEmpty = false;

        if (type === "checkbox") {
            isEmpty = !$el.is(":checked");
        } else {
            const value = $el.val();
            isEmpty = value === null || $.trim(value) === "";
        }

        if (isEmpty) {
            $el.css("border", "1px solid red");
            hasError = true;
        } else {
            $el.css("border", ""); // Clear red border if valid
        }
    });

    if (hasError) {
        e.preventDefault(); // Prevent form submission or further actions
        return false;
    }

    // Proceed with further logic if no error
});




$(document).ready(function () {
    // Initialize Select2 with Bootstrap 4 theme
    $('.new_budget').select2({
        theme: 'bootstrap4'
    });

    // Wait until Select2 is rendered, then style it
    setTimeout(function () {
        $('.new_budget').each(function () {
            var $select2Container = $(this).next('.select2-container');

            // Hide the arrow
            $select2Container.find('.select2-selection__arrow').hide();

            // Change padding of the selection box
            $select2Container.find('.select2-selection')
                .css({
                    'padding': '-10px 12px',       // Adjust padding as needed
                    'border-radius': '32px',      // Optional: rounded corners
                    'border': '1px solid black'   // Optional: border styling
                });
        });
    }, 0);
});






// #endregion