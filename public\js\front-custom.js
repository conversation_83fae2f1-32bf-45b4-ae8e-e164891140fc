window.changeMyAvalibilityAlert = 'This change impacts an active program. If you would still like to update your availability, first request a replacement or a sub for the impacted programs. Please contact <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>  for additional support.';
$(function () {
    let getTabData1 = document.getElementById('getTabData1');
    if (getTabData1) {
        let clickEvent = new Event("click");
        getTabData1.dispatchEvent(clickEvent);
    }
    $('.numericInput').on('input', function () {
        // Replace non-numeric characters and allow only one dot for decimal
        $(this).val($(this).val().replace(/[^0-9.]/g));

        // Ensure that if a dot is the first character, it is replaced with '0.'
        if ($(this).val() === '.') {
            $(this).val('0.');
        }
    });

    initializeSelect2();
    initializeTimepicker();
    initializeDaterangepicker();
    initializeBootstrapDatepicker();
    $('.daterangeElement').on('input', function () {
        if (!$(this).val()) {
            $(this).trigger('cancel.daterangepicker');
        }
    });
});

$(document).on('change input focus', '.av-timepicker', function () {


    // Example usage:
    var time24h = convertTo24Hour($(this).val());
    if ($(this).attr('data-check-time')) {
        if (time24h > $(this).attr('data-check-time')) {
            this.value = $(this).attr('data-org-ntime');
            validateTimeInputs($(this).closest('.timing'));


            window.showAlert = function () {
                alertify.alert(changeMyAvalibilityAlert).setHeader('');
            }

            alertify.alert().setting('modal', true);

            window.showAlert();


        }
    }
    if ($(this).attr('data-max-check-time')) {
        if ($(this).attr('data-max-check-time') > time24h) {
            this.value = $(this).attr('data-org-ntime');

            validateTimeInputs($(this).closest('.timing'));


            window.showAlert = function () {
                alertify.alert(changeMyAvalibilityAlert).setHeader('');
            }

            alertify.alert().setting('modal', true);

            window.showAlert();

        }
    }



});
function syncCheckboxes(checkbox) {
    var siblingCheckbox = $(checkbox).next('input[type="checkbox"]');
    siblingCheckbox.prop('checked', checkbox.checked);
}
function validateTimeInputs($timeInputs) {
    if (!$timeInputs.is(":visible")) {
        return true;
    }
    var startTime = $timeInputs.find('.start-time').val();
    var $endInput = $timeInputs.find('.end-time');
    var endTime = $endInput.val();

    if (startTime && endTime) {
        if (new Date('1970-01-01 ' + startTime) >= new Date('1970-01-01 ' + endTime)) {
            $endInput.focus();
            $endInput.css("outline", "1px solid red");
            return false;
        } else {
            $endInput.css("outline", "unset");
        }
    }
    return true;
}

function convertTo24Hour(time12h) {
    // Extracting hours, minutes, and AM/PM indicator from the input string
    var match = time12h.match(/^(\d+):(\d+)\s?([APap][Mm])$/);
    if (!match) {
        return null; // Invalid input format
    }

    var hours = parseInt(match[1]);
    var minutes = parseInt(match[2]);
    var period = match[3].toUpperCase();

    // Adjusting hours for PM times
    if (period === "PM" && hours < 12) {
        hours += 12;
    }
    // Adjusting hours for AM times at 12 AM
    else if (period === "AM" && hours === 12) {
        hours = 0;
    }

    // Formatting hours and minutes as two-digit strings
    var hoursStr = ('0' + hours).slice(-2);
    var minutesStr = ('0' + minutes).slice(-2);

    return hoursStr + ':' + minutesStr;
}




$(document).on('change', 'input.active-program-class', function () {
    const currentDate = this.value;

    const originalDate = $(this).data('original-date');
    const checkDate = $(this).data('check-date');

    if ($(this).hasClass('min-date')) {

        if (originalDate && checkDate && currentDate > checkDate) {

            window.showAlert = function () {
                alertify.alert(changeMyAvalibilityAlert).setHeader('');
            }

            alertify.alert().setting('modal', true);

            window.showAlert();
            this.value = checkDate;

            $(this).datepicker('setDate', checkDate);
        }
    }
    else if ($(this).hasClass('max-date')) {

        if (originalDate && checkDate && currentDate < checkDate) {
            window.showAlert = function () {
                alertify.alert(changeMyAvalibilityAlert).setHeader('');
            }

            alertify.alert().setting('modal', true);

            window.showAlert();
            this.value = checkDate;
            $(this).datepicker('setDate', checkDate);
        }
    }
});

$(document).on('click input change focus', 'input.active-program-time', function (event) {
    event.preventDefault();


    window.showAlert = function () {
        alertify.alert(changeMyAvalibilityAlert).setHeader('');


    }

    alertify.alert().setting('modal', true);

    window.showAlert();


});
/* $(document).on('click', 'input.active-program-time', function (event) {
    event.preventDefault();

    const currentTime = convertTo24HourFormat(this.value);

    const originalTime = $(this).data('org-time');
    const checkTime = $(this).data('check-time');

    const inputElement = this;
    if ($(this).hasClass('min-time')) {

        if (originalTime && checkTime && currentTime > checkTime) {

            window.showAlert = function () {
                alertify.alert('Cannot change the time during an active program.').setHeader('');
                inputElement.value = originalTime;

            }

            alertify.alert().setting('modal', true);

            window.showAlert();


        }
    }
    else if ($(this).hasClass('max-time')) {

        if (originalTime && checkTime && currentTime < checkTime) {
            window.showAlert = function () {
                alertify.alert('Cannot change the time during an active program.').setHeader('');
                inputElement.value = originalTime;

            }

            alertify.alert().setting('modal', true);

            window.showAlert();


        }
    }
});

function convertTo24HourFormat(timeString) {
    const date = new Date('2000-01-01 ' + timeString);
    return date.toTimeString().slice(0, 8);
} */
$(document).on('click input change focus', 'select.active-program-weekday,select.can-not-change', function (event) {

    event.preventDefault();

    window.showAlert = function () {
        alertify.alert(changeMyAvalibilityAlert).setHeader('');
    }

    alertify.alert().setting('modal', true);

    window.showAlert();
});
$(document).on('click focus', 'input.active-program-location', function (event) {
    var originalValue = $(this).data('org-value');

    event.preventDefault();

    window.showAlert = function () {
        alertify.alert(changeMyAvalibilityAlert).setHeader('');
    }

    alertify.alert().setting('modal', true);

    window.showAlert();
});
$('.modal').on('shown.bs.modal', function () {
    initializeTimepicker();
    initializeBootstrapDatepicker();


});
// #W334 - 2. Instructors end should have program timezone displayed for the invite
function openEyeModal(url, modalId = "#commanModal") {

    $.ajax({
        type: "GET",
        url: url,
        data: {},
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeSelect2();
        },
        error: function (data) {
            dispatchErrorMsgs(data);
        }
    });

}

//end
function initializeTimepicker() {
    $('.timepicker').timepicker(
        {
            'timeFormat': 'h:i A',
            'step': 1,
            'listWidth': 1,
        }
    ).attr('placeholder', 'HH:MM AM/PM');


    $('.timepicker').bind('timeFormatError', function () {
        $(this).val('');
    });


    $('.av-timepicker').timepicker(
        {
            'timeFormat': 'h:i A',
            'step': 1,
            'listWidth': 1,
        }
    ).attr('placeholder', 'HH:MM AM/PM');


    $('.av-timepicker').bind('timeFormatError', function () {
        $(this).val('');
    });
}

function initializeDaterangepicker() {
    $('.daterangeElement').daterangepicker({
        locale: {
            format: 'MM-DD-YYYY',
            separator: " TO "
        },

        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'This Week': [moment().startOf('week'), moment().endOf('week')],
            'Last Week': [moment().subtract(1, 'week').startOf('week'), moment().subtract(1, 'week').endOf('week')],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'This Year': [moment().startOf('year'), moment().endOf('year')],
            'Last Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
        },
    }).on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
    });

    $(".daterangeElement").each(function () {
        const attributeValue = $(this).attr('value');
        if (attributeValue === '') {
            $(this).trigger('cancel.daterangepicker');
        }
    });

}

function initializeBootstrapDatepicker() {
    $(".av-datepickerfilter").each(function () {
        var $datepicker = $(this);
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $datepicker.datepicker({
            format: "mm/dd/yyyy",
            autoclose: true,
        })
        .on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });

    $(".av-monthpickerfilter").each(function () {
        var $datepicker = $(this);
        $datepicker.attr("placeholder", "MM/YYYY");
        $datepicker.datepicker({
            format: "mm/yyyy",
            autoclose: true,
            startView: 1,
            minViewMode: 1,
            maxViewMode: 2
        })
        .on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });

    $(".av-datepicker").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('date-start-date');
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $datepicker.datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            autoclose: true,
        })
            .on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        /*             .on('show', function (e) {
                        var startDate = $datepicker.data('date-start-date');
                        if (startDate) {
                            $datepicker.data('datepicker').setStartDate(startDate);
                        }
                    }); */
    });

    $(".av-datepickerdob").each(function () {
        var $datepicker = $(this);

        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $datepicker.datepicker({
            format: "mm/dd/yyyy",
            endDate: new Date(),
            autoclose: true,
        })
            .on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        /*             .on('show', function (e) {
                        var startDate = $datepicker.data('date-start-date');
                        if (startDate) {
                            $datepicker.data('datepicker').setStartDate(startDate);
                        }
                    }); */
    });

    $(".replacement-datepicker").each(function () {
        var $datepicker = $(this);
        var startDate = $datepicker.data('date-start-date');
        var endDate = $datepicker.data('date-end-date');
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $('.replacement-datepicker').datepicker({
            format: "mm/dd/yyyy",
            startDate: startDate || new Date(),
            endDate: endDate || new Date(),
            autoclose: true,
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });

    $(".schedule-datepicker").each(function () {
        $('.schedule-datepicker').datepicker({
            format: "mm/dd/yyyy",
            autoclose: true,
            startDate: "today",
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
    });
}

function applyCheckbox(elm, targetElm) {
    if ($(elm).is(':checked')) {
        $(targetElm).hide();
    } else {
        $(targetElm).show();
    }
}

function initializeSelect2() {
    $(".select22").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });
    $(".selectTwo").select2();
    // $('.multiselect').multiselect({
    //     enableResetButton: true,
    //     resetButtonText: 'Clear',
    // });


    $(".selectsub").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select Class Date',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });

    $(".select23").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                },
                templateSelection: function (data) {
                    // This will display the value along with the text in the selected tags area
                    if (data.id) {
                        var displayText = data.id.split('-')[1];
                        
                        return displayText + " - " + data.text; // Show formatted value
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });
}

function handleSelect2Select(e) {
    var data = e.params.data.text;
    if (data == 'Select All') {
        $(e.target).find("option").prop("selected", "selected");
        $(e.target).trigger("change");
    }
}




let addMoreClicked = false;
function addMore(e, url, count = 4, boxClass) {

    if (boxClass && $('.' + boxClass).length > count) {
        alertify.error('Max limit reached for Adding', 'Error');
        return;

    }


    if (addMoreClicked) {
        return;
    }
    addMoreClicked = true;

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        method: "GET",
        dataType: "JSON",
        data: {},
        success: function (res) {

            if ($(e)) {
                $(e).closest('div.row').after(res.view);
            }
            // document.startViewTransition(() => {


            // });


            addMoreClicked = false;

        }
    });
}

function removeDuplicate(e) {

    if ($(e)) {
        $(e).closest('div.row').remove();
    }
    // document.startViewTransition(() => {

    // });

}

function initializeDataTable(selector, ajaxUrl, columns, form = null) {
    return $(selector).DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'get',
        'autoWidth': true,
        'searching': false,
        'ordering': false,
        "bLengthChange": false,
        "bInfo": false,
        language: {
            paginate: {
                previous: '<i class="icofont-double-left"></i>',
                next: '<i class="icofont-double-right"></i>'
            }
        },
        drawCallback: function (settings) {
            var table = $(this).DataTable();
            var showPagination = table.page.info().recordsTotal > 10;
            $(selector + '_paginate').toggle(showPagination);
        },
        'ajax': {
            'headers': {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            'url': ajaxUrl,
            'data': function (data) {
                if ($('#' + form).length) {
                    var filterData = $('#' + form).serialize();

                    var filters = decodeURIComponent(filterData).split('&');
                    filters.forEach(function (filter) {
                        var parts = filter.split('=');
                        data[parts[0]] = parts[1];
                    });
                }
            }
        },
        'columns': columns,
    });
}

function openCommanModal(url, modalId = "#commanModal") {

    $.ajax({
        type: "GET",
        url: url,
        data: {},
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeSelect2();
            initializeBootstrapDatepicker();
        },
        error: function (data) {
            dispatchErrorMsgs(data);
        }
    });

}


function selectInstructorType(elm) {
    if ($(elm).val() == '1') {
        $('#all_classes').prop('checked', true);
        $('#sub-classes-box').hide();
    } else {
        $('#sub-classes-box').show();
    }
}

function validateApplyForm(form, modalId) {
    $(".invalid-feedback").hide();
    var error = false;
    if ($('#instructor_type').val() == '') {
        $("#instructor_type_error").show();

        error = true;
    }

    if (!$('#all_classes').is(':checked') && ($('#instructor_type').val() == '0')) {

        if ($("#program_note_id").val() == '') {
            $("#program_note_id_error").show();
            error = true;

        }
    }

    if (error) {
        return false;
    }
    Swal.fire({
        title: 'Main Instructor!',
        text: 'Having a consistent instructor is important to ensure uninterrupted learning for students. By accepting this invite you agree to teach the program for its complete duration unless agreed to otherwise in writing. You may request for sub as needed.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, submit!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            modalFormSubmit(form, modalId);
        }
    });

}


function modalFormSubmit(form, modalId) {
    event.preventDefault();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            }
            else {

                $(modalId).modal('hide');
                alertify.success(res.message);
                drawTables();
                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 1200)
                }
            }
        },
        error: function (data) {
            if (data.status == 422) {
                $.each(data.responseJSON.errors, function (key, value) {

                    if (key.includes('.')) {
                        var fieldName = key.split('.')[0]; // Get the field name before '.'
                        var fieldIndex = key.split('.')[1]; // Get the field index after '.'
                        var inputFields = $(form).find('[name^="' + fieldName + '["]').eq(fieldIndex);
                    } else {
                        var inputFields = $(form).find('[name="' + key + '"]');
                    }

                    inputFields.addClass('is-invalid');
                    inputFields.focus();
                    inputFields.after('<div class="invalid-feedback">' + value + '</div>');
                });
            }
            else if (data.status == 400) {
                dispatchErrorMsgs(data)
            }
            else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }




    }).done(function () {
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}


// Flag to track if form is already being submitted
var isFormSubmitting = false;

function commonFormSubmit(form) {
    event.preventDefault();

    // Check if form is already being submitted
    if (isFormSubmitting) {
        return false;
    }

    isFormSubmitting = true; // Set flag to true to indicate form submission in progress

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            } else {
                alertify.success(res.message);
                drawTables();

                if (res.resetForm) {
                    $(form)[0].reset();

                   $('.rebrec').attr('placeholder','Upload Receipt');
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 1200)
                }
                if (res.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 1200)
                }
            }
        },
        error: function (data) {
            if (data.status == 422) {
                // Validation errors
                $.each(data.responseJSON.errors, function (key, value) {
                    $(form).find('[name="' + key + '"]').addClass('is-invalid');
                    $(form).find('[name="' + key + '"]').after('<div class="invalid-feedback">' + value + '</div>');
                });
            } else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }
    }).always(function () {
        // Reset flag after AJAX request is completed
        isFormSubmitting = false;
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}


function postUrlDataWithConfirmation(url, formid = null) {

    $('#confirm-modal').modal('show');

    $('#confirm-modal-confirm').one('click', function () {
        postUrlData(url);
        $('#confirm-modal').modal('hide');
    });
}

function postUrlData(url) {

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        type: "POST",
        data: {},
        dataType: "json",
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            } else if (res.status == true) {

                alertify.success(res.message);
                drawTables();
                if (res.reload) {

                    location.reload();

                }
                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 1200)
                }
            }

        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });
}

function getReplaceHtmlData(e, url, replaceHtmlId) {
    var selectedId = $(e).val();
    if (selectedId == "") {
        return false;
    }
    var modifiedUrl = url.replace(/\/id$/, '/' + selectedId);
    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: modifiedUrl,
        type: "GET",
        data: {},
        dataType: "json",
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            } else if (res.status == true) {
                $(replaceHtmlId).html(res.view);
                // document.startViewTransition(() => {
                //     $(replaceHtmlId).html(res.view);
                // });
            }

        },
        error: function (data) {
            dispatchErrorMsgs(data);
        }
    });
}

function changeStatus(status, url) {
    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        method: "POST",
        dataType: "JSON",
        data: {
            status: status
        },
        success: function (res) {
            alertify.success(res.message);
            drawTables();
        },
        error: function (data) {
            dispatchErrorMsgs(data);
        }
    });
}

function showStandbyModal(status, actionRoute) {
    Swal.fire({
        title: 'Set to stand-by!',
        text: 'You are going to be assigned as a Standby Instructor!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            changeStatus(status, actionRoute);
        }
    });
}

// Show a pop-up on accepting a main instructor invite
function showMainAcceptPopUp(status, actionRoute) {
    Swal.fire({
        title: 'Main Instructor!',
        text: 'Having a consistent instructor is important to ensure uninterrupted learning for students. By accepting this invite you agree to teach the program for its complete duration unless agreed to otherwise in writing. You may request for sub as needed.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            changeStatus(status, actionRoute);
        }
    });
}

// Show a pop-up on accepting a sub instructor invite for standby
function showSubStandbyPopUp(status, subStandBy, actionRoute) {
    Swal.fire({
        title: 'Sub Standby Instructor!',
        text: 'An instructor has already been assigned, You are now going to become a sub standby instructor!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            changeStatusForSubStandby(status, subStandBy, actionRoute);
        }
    });
}

function changeStatusForSubStandby(status, subStandBy, url) {
    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        method: "POST",
        dataType: "JSON",
        data: {
            status: status,
            is_sub_standby: subStandBy
        },
        success: function (res) {
            alertify.success(res.message);
            drawTables();
        },
        error: function (data) {
            dispatchErrorMsgs(data);
        }
    });
}

function drawTable1() {
    if (typeof dataTable !== 'undefined') {
        dataTable.draw();
    }
}

function drawTable2() {
    if (typeof dataTable2 !== 'undefined') {
        dataTable2.draw();
    }
}

function drawTables() {
    if (typeof dataTable !== 'undefined') {
        dataTable.draw();
    }
    if (typeof dataTable2 !== 'undefined') {
        dataTable2.draw();
    }
}

function getTabData(e, url) {
    $("#back__preloader").show();
    let replaceHtmlId = e.dataset.bsTarget;
    $.ajax({
        url: url,
        method: "GET",
        dataType: "json",
        data: {},
        success: function (res) {
            $("#back__preloader").hide();
            if (res.status == 1) {
                $(replaceHtmlId).html(res.view);
                initializeSelect2();
                initializeDaterangepicker();
                drawTables();
                // document.startViewTransition(() => {
                //     $(replaceHtmlId).html(res.view);
                //     initializeSelect2();
                //     initializeDaterangepicker();
                //     drawTables();
                // });
            }

        },
        error: function (data) {
            dispatchErrorMsgs(data);
        }
    })
}

function dispatchErrorMsgs(data) {
    // alertify.set('notifier', 'position', 'top-right');
    if (typeof data.responseJSON.status !== 'undefined') {
        alertify.error(data.responseJSON.message, 'Error', 10)
    } else {
        $str = '';
        $.each(data.responseJSON.errors, function (key, value) {
            $str = $str + value + ",";
        });
        $err = $str.split(',');
        alertify.error($.unique($err).join("<br>"), 'Error', 10);
    }


}



function postFormDataWithConfirmation(formid) {
    event.preventDefault();


    if (formid) {
        var anyCheckboxChecked = $('input[form="' + formid + '"][type="checkbox"]:checked').length > 0;

        if (!anyCheckboxChecked) {
            alertify.warning('Please select at least one class');
            return false;
        }
        else {
            $('#confirm-modal').modal('show');

            $('#confirm-modal-confirm').one('click', function () {
                commonFormSubmit($('#' + formid));
                $('#confirm-modal').modal('hide');
            });
            $('#confirm-modal').on('hidden.bs.modal', function () {
                setTimeout(function() {
                    location.reload();
                }, 2000);
            });
        }

    }
}






function MarkmodalFormSubmit(form, modalId) {
    event.preventDefault();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            }
            else {

                // $(modalId).modal('hide');
                // alertify.success(res.message);
                drawTables();
                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 1200)
                }
            }
        },
        error: function (data) {
            if (data.status == 422) {
                $.each(data.responseJSON.errors, function (key, value) {

                    if (key.includes('.')) {
                        var fieldName = key.split('.')[0]; // Get the field name before '.'
                        var fieldIndex = key.split('.')[1]; // Get the field index after '.'
                        var inputFields = $(form).find('[name^="' + fieldName + '["]').eq(fieldIndex);
                    } else {
                        var inputFields = $(form).find('[name="' + key + '"]');
                    }

                    inputFields.addClass('is-invalid');
                    inputFields.focus();
                    inputFields.after('<div class="invalid-feedback">' + value + '</div>');
                });
            }
            else if (data.status == 400) {
                dispatchErrorMsgs(data)
            }
            else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }




    }).done(function () {
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}

function filterForm1Submission(event, modal = '#dataTableModal') {
    event.preventDefault();
    drawTable1();
    $(modal).modal('hide');
}

function filterForm1Reset(event, modal = '#dataTableModal') {
    event.preventDefault();
    $(event.target).closest('form')[0].reset();
    // Reset Select2
    $(event.target).closest('form').find('select.selectTwo').val(null).trigger('change');

    // Reset DateRangePicker
    $(event.target).closest('form').find('input.daterangepicker').val('');
    drawTable1();

    // $(modal).modal('hide');
}

function filterForm2Submission(event, modal = '#dataTable2Modal') {
    event.preventDefault();
    drawTable2();
    $(modal).modal('hide');
}

function filterForm2Reset(event, modal = '#dataTable2Modal') {
    event.preventDefault();
    $(event.target).closest('form')[0].reset();
    // Reset Select2
    $(event.target).closest('form').find('select.selectTwo').val(null).trigger('change');

    // Reset DateRangePicker
    $(event.target).closest('form').find('input.daterangepicker').val('');
    drawTable2();

    $(modal).modal('hide');
}

