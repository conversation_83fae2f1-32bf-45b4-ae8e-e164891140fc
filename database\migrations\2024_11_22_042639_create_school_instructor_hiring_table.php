<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolInstructorHiringTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_instructor_hiring', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('requirment_id')->nullable();
            $table->bigInteger('school_id')->nullable();
            $table->bigInteger('instructor_id')->nullable();
            $table->integer('invited_by_school')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_instructor_hiring');
    }
}
