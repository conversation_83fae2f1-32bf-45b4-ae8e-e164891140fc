$(function () {
    let getTabData1 = document.getElementById('getTabData1');
    if (getTabData1) {
        let clickEvent = new Event("click");
        getTabData1.dispatchEvent(clickEvent);
    }
    $('.numericInput').on('input', function () {
        // Replace non-numeric characters and allow only one dot for decimal
        $(this).val($(this).val().replace(/[^0-9.]/g));

        // Ensure that if a dot is the first character, it is replaced with '0.'
        if ($(this).val() === '.') {
            $(this).val('0.');
        }
    });

});



let addMoreClicked = false;
function addMore(e, url ,count=4,boxClass) {

    if(boxClass && $('.'+boxClass).length>count){
        alertify.error('Max limit reached for Adding', 'Error');
        return;
        
    }


    if (addMoreClicked) {
        return;
    }
    addMoreClicked = true;

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        method: "GET",
        dataType: "JSON",
        data: {},
        success: function (res) {

            if ($(e)) {
                $(e).closest('div.row').after(res.view);
            }
            // document.startViewTransition(() => {
                

            // });


            addMoreClicked = false;
            
        }
    });
}

function removeDuplicate(e) {
    if ($(e)) {
        $(e).closest('div.row').remove();
    }

    // document.startViewTransition(() => {
       
    // });

}

function initializeDataTable(selector, ajaxUrl, columns, form = null) {
    return $(selector).DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'get',
        'autoWidth': true,
        'searching': false,
        'ordering': false,
        "bLengthChange": false,
        "bInfo": false,
        language: {
            paginate: {
                previous: '<i class="icofont-double-left"></i>',
                next: '<i class="icofont-double-right"></i>'
            }
        },
        drawCallback: function (settings) {
            var table = $(this).DataTable();
            var showPagination = table.page.info().recordsTotal > 10;
            $(selector + '_paginate').toggle(showPagination);
        },
        'ajax': {
            'headers': {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            'url': ajaxUrl,
            'data': function (data) {
                if ($('#' + form).length) {
                    var filterData = $('#' + form).serialize();

                    var filters = decodeURIComponent(filterData).split('&');
                    filters.forEach(function (filter) {
                        var parts = filter.split('=');
                        data[parts[0]] = parts[1];
                    });
                }
            }
        },
        'columns': columns,
    });
}
function openCommanModal(url, modalId = "#commanModal") {

    $.ajax({
        type: "GET",
        url: url,
        data: {},
        dataType: "json",
        success: function (res) {

            $(modalId + ' .modal-dialog').html(res.view);
            $(modalId).modal('show');
            initializeBootstrapDatepicker();
        }
    });

}

function modalFormSubmit(form, modalId) {
    event.preventDefault();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            }
            else {

                $(modalId).modal('hide');
                alertify.success(res.message);
                drawTables();
                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 1200)
                }
            }
        },
        error: function (data) {
            if (data.status == 422) {
                $.each(data.responseJSON.errors, function (key, value) {
         
                    if (key.includes('.')) {
                        var fieldName = key.split('.')[0]; // Get the field name before '.'
                        var fieldIndex = key.split('.')[1]; // Get the field index after '.'
                        var inputFields = $(form).find('[name^="' + fieldName + '["]').eq(fieldIndex);
                    } else {
                        var inputFields = $(form).find('[name="' + key + '"]');
                    }

                    inputFields.addClass('is-invalid');
                    inputFields.focus();
                    inputFields.after('<div class="invalid-feedback">' + value + '</div>');
                });
            } else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }
        
        
        
        
    }).done(function () {
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}


function commonFormSubmit(form) {

    event.preventDefault();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let url = $(form).attr('action');
    let method = $(form).attr('method');
    let formData = new FormData($(form)[0]);

    $.ajax({
        url: url,
        type: method,
        processData: false,
        contentType: false,
        data: formData,
        dataType: "json",
        beforeSend: function () {
            $(form).find('button .save-loader').show();
            $(form).find('button').prop("disabled", true);
            $(form).find('.is-invalid').removeClass('is-invalid');
            $(form).find('.invalid-feedback').remove();
        },
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            } else {

                alertify.success(res.message);
                drawTables();

                if (res.resetForm) {
                    $(form)[0].reset();
                }

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 1200)
                }
                if (res.reload) {
                    window.setTimeout(function () {
                        location.reload();
                    }, 1200)
                }
            }

        },
        error: function (data) {
            if (data.status == 422) {
                // Validation errors
                $.each(data.responseJSON.errors, function (key, value) {

                    $(form).find('[name="' + key + '"]').addClass('is-invalid');

                    $(form).find('[name="' + key + '"]').after('<div class="invalid-feedback">' + value + '</div>');
                });
            } else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            $(form).find('button .save-loader').hide();
            $(form).find('button').prop("disabled", false);
        }
    }).done(function () {
        $(form).find('button .save-loader').hide();
        $(form).find('button').prop("disabled", false);
    });
}

function postUrlDataWithConfirmation(url) {
    var userConfirmed = confirm('Are you sure?');

    if (!userConfirmed) {
        return true;
    }
    postUrlData(url);
}

function postUrlData(url) {

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        type: "POST",
        data: {},
        dataType: "json",
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            } else if (res.status == true) {

                alertify.success(res.message);
                drawTables();

                if (res.rurl) {
                    window.setTimeout(function () {
                        window.location = res.rurl
                    }, 1200)
                }
            }

        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });
}

function getReplaceHtmlData(e, url, replaceHtmlId) {
    var selectedId = $(e).val();
    if (selectedId == "") {
        return false;
    }
    var modifiedUrl = url.replace(/\/id$/, '/' + selectedId);
    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: modifiedUrl,
        type: "GET",
        data: {},
        dataType: "json",
        success: function (res) {
            if (res.status == false) {
                alertify.error(res.message);
            } else if (res.status == true) {

                $(replaceHtmlId).html(res.view);
                
                // document.startViewTransition(() => {
                   
                // });
            }

        },
        error: function (data) {
            alertify.error(data.responseJSON.message, 'Error');
        }
    });
}

function changeStatus(status, url) {

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: url,
        method: "POST",
        dataType: "JSON",
        data: {
            status: status
        },
        success: function (res) {
            alertify.success(res.message);
            drawTables();
        }
    });
}

function drawTables() {
    if (typeof dataTable !== 'undefined') {
        dataTable.draw();
    }
    if (typeof dataTable2 !== 'undefined') {
        dataTable2.draw();
    }
}

function getTabData(e, url) {
    let replaceHtmlId = e.dataset.bsTarget;
    $.ajax({
        url: url,
        method: "GET",
        dataType: "json",
        data: {},
        success: function (res) {

            if (res.status == 1) {
             
                    $(replaceHtmlId).html(res.view);
                    initializeSelect2();
                    initializeDaterangepicker();
                    initializeBootstrapDatepicker();
                    drawTables();
        
            }

        },
    })
}

function initializeDaterangepicker() {
    $('.daterangeElement').daterangepicker({
        locale: {
            format: 'MM-DD-YYYY',
            separator: " TO "
        },

        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'This Week': [moment().startOf('week'), moment().endOf('week')],
            'Last Week': [moment().subtract(1, 'week').startOf('week'), moment().subtract(1, 'week').endOf('week')],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'This Year': [moment().startOf('year'), moment().endOf('year')],
            'Last Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
        },
    }).on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
    });

    $(".daterangeElement").each(function () {
        const attributeValue = $(this).attr('value');
        if (attributeValue === '') {
            $(this).trigger('cancel.daterangepicker');
        }
    });

}
function applyCheckbox(elm, targetElm) {
    if ($(elm).is(':checked')) {
        $(targetElm).hide();
    } else {
        $(targetElm).show();
    }
}

function initializeBootstrapDatepicker() {

    $(".av-datepickerfilter").each(function () {
        var $datepicker = $(this);
      
        $datepicker.attr("placeholder", "MM/DD/YYYY");
        $datepicker.datepicker({
            format: "mm/dd/yyyy",
            autoclose: true,
        })
            .on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        
    });
    
  

    
}

function initializeSelect2() {
    $(".select22").each(function () {
        $(this)
            .wrap('<div class="position-relative"></div>')
            .select2({
                theme: 'bootstrap4',
                width: 'style',
                placeholder: 'Select',
                minimumInputLength: 0,
                allowClear: true,

                dropdownParent: $(this).parent(),
                templateResult: function (data, container) {

                    if (data.element && data.element.selected) {
                        $(container).css('background-color', '#3875d7');
                    }
                    return data.text;
                }
            })
            .on("select2:select", function (e) {
                handleSelect2Select(e);
            });
    });
    $(".selectTwo").select2();
    // $('.multiselect').multiselect({
    //     enableResetButton: true,
    //     resetButtonText: 'Clear',
    // });
}

function handleSelect2Select(e) {
    var data = e.params.data.text;
    if (data == 'Select All') {
        $(e.target).find("option").prop("selected", "selected");
        $(e.target).trigger("change");
    }
}


$(".updatecontact").click(function () {

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var url = APP_URL + '/updatecontactdeatils';
    var formData = new FormData($("#update_contact")[0]);
    $.ajax({
        type: 'POST',
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        beforeSend: function () {
            $("#overlay").fadeIn(300)
        },
        success: function (data) {
            if (data.success == true) {
                alertify.success(data.message);
               
            } else {
                alertify.error(data.message);
            }
        }, error: function (data) {
            if (data.status == 422) {
              
                $.each(data.responseJSON.errors, function (key, value) {

                    if (key.includes('.')) {
                        var fieldName = key.split('.')[0]; // Get the field name before '.'
                        var fieldIndex = key.split('.')[1]; // Get the field index after '.'
                        var inputFields = $('#update_contact').find('[name^="' + fieldName + '["]').eq(fieldIndex);
                    } else {
                        var inputFields = $('#update_contact').find('[name="' + key + '"]');
                    }

                    inputFields.focus();

                });
            } else {
                alertify.error(data.responseJSON.message, 'Error');
            }
            

        }
    }).done(function () {
        setTimeout(function () {
            $("#overlay").fadeOut(300);
        }, 500);
    });
});

function filterForm1Submission(event, modal = '#dataTableModal') {
    event.preventDefault();
    drawTable1();
    $(modal).modal('hide');
}

function filterForm1Reset(event, modal = '#dataTableModal') {
    event.preventDefault();
    $(event.target).closest('form')[0].reset();
    // Reset Select2
    $(event.target).closest('form').find('select.selectTwo').val(null).trigger('change');

    // Reset DateRangePicker
    $(event.target).closest('form').find('input.daterangepicker').val('');
    drawTable1();

    // $(modal).modal('hide');
}

function filterForm2Submission(event, modal = '#dataTable2Modal') {
    event.preventDefault();
    drawTable2();
    $(modal).modal('hide');
}

function filterForm2Reset(event, modal = '#dataTable2Modal') {
    event.preventDefault();
    $(event.target).closest('form')[0].reset();
    // Reset Select2
    $(event.target).closest('form').find('select.selectTwo').val(null).trigger('change');

    // Reset DateRangePicker
    $(event.target).closest('form').find('input.daterangepicker').val('');
    drawTable2();

    $(modal).modal('hide');
}

function drawTable1() {
    if (typeof dataTable !== 'undefined') {
        dataTable.draw();
    }
}



