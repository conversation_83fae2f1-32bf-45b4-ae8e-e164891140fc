<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPaymentFrequencyToSchoolManagementSetting extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('school_management_setting', function (Blueprint $table) {
            //
        });

        DB::table('school_management_setting')->insert([
            'type' => 'payment_frequency',
            'value' => null,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('school_management_setting', function (Blueprint $table) {
            DB::table('school_management_setting')->where('type', 'payment_frequency')->delete();
        });
    }
}
