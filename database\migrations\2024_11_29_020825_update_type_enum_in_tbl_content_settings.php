<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTypeEnumInTblContentSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tbl_content_settings', function (Blueprint $table) {
            //
        });

        DB::statement("ALTER TABLE tbl_content_settings MODIFY COLUMN type ENUM('aboutus', 'terms-conditions', 'privacy-policy', 'activity-terms-condition', 'Online contract', 'In person contract', 'hybrid contract', 'Online Logistics/Prerequisites', 'In person Logistics/Prerequisites', 'New Instructor Contract')");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tbl_content_settings', function (Blueprint $table) {
            //
        });
        DB::statement("ALTER TABLE tbl_content_settings MODIFY COLUMN type ENUM('aboutus', 'terms-conditions', 'privacy-policy', 'activity-terms-condition', 'Online contract', 'In person contract', 'hybrid contract', 'Online Logistics/Prerequisites', 'In person Logistics/Prerequisites', 'New Instructor Contract')");
    }
}
