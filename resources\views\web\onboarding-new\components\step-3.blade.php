<style>
    .hide {
        display: none;
    }

    .custom-file-input {
        position: relative;
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #ccc;
        border-radius: 34px;
        background-color: #fff;
        display: flex !important;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        font-family: sans-serif;
    }

    .custom-file-input input[type="file"] {
        position: absolute !important;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
        opacity: 0 !important;
        cursor: pointer !important;
    }

    .additional_certificate_upload_parent_div {
        position: absolute !important;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
        opacity: 0 !important;
        cursor: pointer !important;
    }

    .tools-field {
        border: 1px solid;
        border-radius: 7px;
        font-size: 1rem;
        background-color: #7accd23d;
    }
</style>
<div class="inner-form">
    <form action="#" method="post" id="secondstep" enctype="multipart/form-data">
        {!! csrf_field() !!}

        <input type="hidden" name="id" id="id" value="{{ $data['id'] }}">
        <div class="row">
            <div class="login__heading">
                <h5 class="login__title mt-5">Education & Experience</h5>
            </div>

            <div class="col-xl-12 col-md-12 mt-3">
                <div>
                    <h3 class="onboarding-step-form onboarding-step3-form px-2 py-3">Education </h3>
                    <div class="login__form form-title mt-5">
                        <h4 class="form-heading onboarding-step-form certificationyes">Certificates and Licenses</h4>
                        <p class="">I am certified/licensed </p>
                        {{-- <p class="certificationno" style="display:none;">I am</p> --}}
                        <div class="check-box d-flex ">
                            <div class="form-check ">
                                <input class="form-check-input" type="radio" name="certification"
                                    id="certification_yes" value="yes" onclick="checkcertificateRadio('1')"
                                    @if (!empty($data['second'])) @if ($data['second']->certification == 'yes') {{ 'checked' }} @endif
                                @else {{ 'checked' }} @endif>
                                <label class="form-check-label" for="certification">
                                    Yes
                                </label>
                            </div>
                            <div class="form-check" style="margin-left:3%">
                                <input class="form-check-input" type="radio" name="certification"
                                    id="certification_no" value="no" onclick="checkcertificateRadio('0')"
                                    @if (!empty($data['second'])) @if ($data['second']->certification == 'no') {{ 'checked' }} @endif
                                @endif>
                                <label class="form-check-label" for="certification">
                                    No
                                </label>
                            </div>
                        </div>
                        <span id="certification_error" class="error rederror"></span>
                    </div>

                    {{-- <div>
                        <div>
                            <select
                                class="form-control common__login__input w-25 px-3 py-3"
                                id="certification_dropdown"
                                name="certification"
                                style="
        border-radius: 34px !important;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url({{ asset('new_images_school/dropdown.png') }});
                    background-repeat: no-repeat;
                    background-position: right 1rem center;
                    background-size: 13px;
                    padding-right: 3rem;
                    background-color: white;
                    ">
                    <option value="License/Certification" selected>License/Certification</option>
                    <option value="Other">Other</option>
                    </select>






                </div>
            </div> --}}
        </div>

</div>




<div class="col-xl-12 col-md-12 secondno " <?php if (!empty($data['second'])) {
                                                if (
                                                    $data['second']->certification == 'no'
                                                ) { ?> style='display:block;'
    <?php } else { ?>style="display:none;" <?php }
                                            } else { ?>style="display:none;" <?php } ?>>
    <div class="login__form select sel" style="position: relative;">
        <div class="d-flex align-items-center">
            <label for="profile_type" class="me-2 mb-0" style="width: 50px;">I am</label>
            <select style="width:40%" class="form-select common__login__input col-6 ptype"
                aria-label="Teaching certification valid till (Year)" name="profile_type" id="profile_type">
                <option value="" selected hidden>Select</option>
                @if($data['profile_type'] && $data['profile_type']->value)
                @foreach (json_decode($data['profile_type']->value, true) as $type)
                <option value="{{ $type }}" @if (!empty($data['second'])) @if ($data['second']->profile_type == $type) {{ 'selected' }} @endif @endif>{{ $type }}</option>
                @endforeach
                @endif

                <option value="Other"
                    @if (!empty($data['second'])) @if ($data['second']->profile_type == 'Other') {{ 'selected' }} @endif
                    @endif>Other</option>
            </select>
        </div>
    </div>
    <span id="profile_type_error" class="error rederror"></span>
</div>


<div class="col-xl-12 col-md-12 other_specify"
    @if (!empty($data['second'])) @if ($data['second']->profile_type == 'Other') style="display:block;" @else style="display:none" @endif
    @else style="display:none" @endif>
    <div class="login__form">

        <input class="common__login__input" type="text" placeholder="Please specify" name="other_specify"
            id="other_specify"
            value="@if (!empty($data['second'])) {{ $data['second']->specify }} @endif">
    </div>
    <span id="profile_type_error" class="error rederror"></span>
</div>


<!-- //this div will not show up until and unless update is not there -->
<div class="appendCertificate secondyes">
    @if (!empty($data['second']) && !empty($data['second']->education) && $data['second']->certification == 'yes')
    @foreach ($data['second']->education as $key => $secondData)
    <div class="row">
        <div class="col-lg-11">
            <div class="row">
                <div class="col-xl-6 col-md-6 secondyes certification-list">
                    <div class="login__form">
                        <select class="common__login__input form-select credentialing_agency"
                            placeholder="Credentialing Agency*" data-allow-clear="1" type="text"
                            name="credentialing_agency[]" id="credentialing_agency_{{$key}}">
                            <option value="" hidden>Credentialing Agency*</option>

                            @foreach ($data['credentialing_agency'] as $agency)
                            <option value="{{ $agency->agency }}"
                                @if (!empty($data['second'])) @php $prvaluec=explode(",",$secondData->credentialing_agency); @endphp @if (in_array($agency->agency, $prvaluec)) {{ 'selected' }} @endif
                                @endif> {{ $agency->agency }}</option>
                            @endforeach
                            <option value="Other"
                                @if (!empty($data['second'])) @php $prvaluec=explode(",",$secondData->credentialing_agency); @endphp @if (in_array('Other', $prvaluec)) {{ 'selected' }} @endif
                                @endif>Other</option>

                        </select>
                    </div>

                    <div class="login__form">
                        <div class="col-xl-12 col-md-12">
                            <div
                                class="login__form  sel credentialingAgencyOther
                                        @if (!empty($data['second'])) @php $prvaluec = explode(" ,", $secondData->credentialing_agency); @endphp
                                @if (in_array('Other', $prvaluec))
                                d-block
                                @else
                                @if (!empty($data['second']) && in_array('Other', explode(',',$secondData->education)))
                                invisible
                                @else
                                d-none
                                @endif
                                @endif
                                @else
                                d-none
                                @endif">
                                <input type="text" name="credentialing_agency_other[]"
                                    id="credentialing_agency_other" value="<?php if (!empty($data['second'])) {
                                                                                $prvaluec = explode(',', $secondData->credentialing_agency);
                                                                                if (in_array('Other', $prvaluec)) {
                                                                                    echo trim($secondData->credentialing_agency_other);
                                                                                }
                                                                            } ?>"
                                    class="common__login__input credentialing_agency_other"
                                    placeholder="Enter Other Credentialing Agency Name*">

                            </div>
                        </div>
                    </div>

                    <div class="login__form select sel" style="position: relative;">
                        <input
                            class="common__login__input month_and_year_graduation teaching_certification_year"
                            type="text" placeholder="Valid Till*"
                            name="teaching_certification_year[]" id="teaching_certification_year"
                            value="@if (!empty($secondData->certification_year)) {{ $secondData->certification_year }} @endif">
                        <svg onclick="teachingYearSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px"
                            height="20px" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg" stroke="#000000"
                            stroke-width="0.00024000000000000003">
                            <g id="SVGRepo_bgCarrier" stroke-width="0" />
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <g id="SVGRepo_iconCarrier">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z"
                                    fill="#aeaeae" />
                            </g>
                        </svg>

                    </div>
                    <span id="teaching_certification_year_error" class="error rederror"></span>

                    <div
                        class="login__form upload d-flex position-relative align-items-center gap-5">
                        @if (!empty($secondData->certificate))
                        @php
                        $certi = explode('certificate/', $secondData->certificate);
                        @endphp
                        @endif
                        <input type="hidden" class="certificate_value"
                            name="certificate_value[]" id="certificate_value_{{ $key }}"
                            value="{{ !empty($secondData->certificate) ? $secondData->certificate : '' }}">
                        <input type="hidden" class="certification_name"
                            name="certification_name[]" id="certification_name_{{ $key }}"
                            value="{{ !empty($secondData->certification_name) ? $secondData->certification_name : '' }}">
                        <input type="file" id="certificate_{{ $key }}"
                            class="form-control second certificatefile certificate"
                            style="padding: 18px; border-radius: 25px; opacity: 0; position: absolute; z-index:-1 width: 100%;"
                            name="certificate[]" class="certificate"
                            accept=".doc,.docx,.pdf,.png,.jpg,.jpeg">
                        <span class="certificate_text" style="padding: 12px 20px;border: 1px solid #d8dadc;width: 100%;display: flex;border-radius: 32px;justify-content: space-between;color: #757575;align-items: center;">
                            Upload Certificate/License*
                            <svg width="17" height="17" viewBox="0 0 17 17"
                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
                                    stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round">
                                </path>
                                <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7"
                                    stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round"></path>
                                <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5"
                                    stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </span>
                    </div>
                    <span class="text-danger certificate_err" style="font-size: 13px;"></span>
                    <small style="font-size: 0.8rem; column-gap: 5px" class="flex-wrap d-flex py-3 text-muted justify-content-center d-block">
                        <i>Accepted file formats include *.pdf, *.doc, *.docx, *.png, *.jpg, and *.jpeg up to 1 MB</i>
                    </small>
                    @if (!empty($secondData->certificate) && $certi && !empty($secondData->certification_name))
                    <span class="uploaded-documents">
                        <span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">
                            {{ $secondData->certification_name }}
                        </span>
                        <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 remove-certificate remove_certificate_new" title="Remove">
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11 1L1 11M1 1L11 11" stroke="#004CBD" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </span>
                    </span>
                    @endif
                </div>

                <div class="col-xl-6 col-md-6 secondyes certification-list">
                    <div class="login__form">
                        <select
                            class="common__login__input form-select certified_special_education"
                            placeholder="Please specify*" data-allow-clear="1" type="text"
                            name="certified_special_education[]" id="certified_special_education_{{$key}}">
                            <option value="" hidden>Certification/License*</option>

                            @foreach ($data['credentialing_agency'] as $agency)
                            @if (!empty($data['second']))
                            @php
                            $prvaluec = explode(',', $secondData->credentialing_agency);
                            @endphp
                            @if (in_array($agency->agency, $prvaluec))
                            @foreach ($agency->certificates()->get() as $cer)
                            <option value="{{ $cer->certificate }}"
                                @if (!empty($data['second'])) @php $prvaluec=explode(",",$secondData->education); @endphp @if (in_array($cer->certificate, $prvaluec)) {{ 'selected' }} @endif
                                @endif>
                                {{ $cer->certificate }}
                            </option>
                            @endforeach
                            @endif
                            @endif
                            @endforeach
                            <option value="Other"
                                @if (!empty($data['second'])) @php $prvaluec=explode(",",$secondData->education); @endphp @if (in_array('Other', $prvaluec)) {{ 'selected' }} @endif
                                @endif>Other</option>

                        </select>
                    </div>
                    <div class="login__form">
                        <div class="col-xl-12 col-md-12">
                            <div class="login__form  sel Certificationsother 
                                                    @if (!empty($data['second']) && !in_array('Other', explode(',',$secondData->education)))
                                                        d-none
                                                    @endif">
                                <input type="text" name="certifications_other[]"
                                    id="certifications_other" value="<?php if (!empty($data['second'])) {
                                                                            $prvaluec = explode(',', $secondData->education);
                                                                            if (in_array('Other', $prvaluec)) {
                                                                                echo trim($secondData->certifications_other);
                                                                            }
                                                                        } ?>"
                                    class="common__login__input certifications_other"
                                    placeholder="Enter Other Certificate Name*">

                            </div>
                        </div>
                    </div>

                    <div class="login__form  sel">

                        <select class="teaching_certification_states" multiple
                            placeholder="Valid in (states)*" data-allow-clear="1"
                            name="teaching_certification_states[{{ $key }}][]"
                            id="teaching_certification_states_{{ $key }}">

                            @foreach ($data['state'] as $rowss)
                            <option value="{{ str_replace(' ', '_', $rowss->name) }}"
                                @if (!empty($data['second'])) @php
                                $prvaluec=json_decode($secondData->states);
                                @endphp
                                @if (in_array(str_replace(' ', '_', $rowss->name), $prvaluec))
                                selected @endif
                                @endif>
                                {{ $rowss->name }}
                            </option>
                            @endforeach
                        </select>

                    </div>
                    <span id="teaching_certification_states_error" class="error rederror"></span>

                </div>
                <div class="col-lg-12 secondyes">
                    <div class="checkbox certification-checkbox d-flex gap-2 mt-3">
                        <input type="checkbox" class="certification_visible_to_school"
                            @if (!empty($secondData->certification_visible_to_school) && $secondData->certification_visible_to_school == 1) checked @endif
                        class="certification_visible_to_school"
                        name="certification_visible_to_school[]"
                        id="certification_visible_to_school" onclick="CheckCertification(this)">
                        <input type="hidden" class="check_certification"
                            name="check_certification[]" id="check_certification"
                            value="{{(!empty($secondData->certification_visible_to_school) && $secondData->certification_visible_to_school == 1) ? 1 : 0}}">
                        <label for="certification_visible_to_school">Make this
                            Document visible to the schools</label>
                    </div>
                </div>
            </div>
        </div>
        @if (!$loop->first)
        <div class="col-lg-1 pt-3 secondyes">
            <div class="d-flex justify-content-center align-items-center h-100"
                style="border-left:1px solid #d8dadc;">
                <svg onclick="DeleteOnboardingStep(this, 'certificate')" width="30px"
                    height="30px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                    <path fill="#a7a9b7"
                        d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z" />
                </svg>
            </div>
        </div>
        @endif
        @if (!$loop->last)
        <hr style="margin: 25px 0; width: 95.5%">
        @endif
    </div>

    @endforeach
    @endif
</div>

<div class="row mt-5 mb-3 secondyes " style="padding-left: 27px; @if(!empty($data['second']->certification) && $data['second']->certification == 'no') display: none; @endif">
    <div class="col-xl-3 com-md-3 btn btn-info onboarding-step-btn @if (!empty($data['second'])) @if ($data['second']->certification == 'no') '' @endif @if (!empty($data['second']->education)) addMoreCertification @endif @endif"
        id="add-certification">
        <span>+</span>
        @if (!empty($data['second']) && !empty($data['second']->education) && count($data['second']->education) > 0) Add More
        @else
        Add Certificate/License @endif
    </div>
</div>

{{-- New Certification --}}
<h4 class="mt-5" style="color:#004CBD;font-weight:500;padding-top:1rem;margin-bottom:0rem;margin-bottom:0.7rem">Additional Certificates/Licenses</h4>
<div id="main_certificate_div">
    <div id="additional_certificates" style="display:none">

    </div>

    <!-- //for the update of the additional certificate -->
    @if(!empty($data["second_addtional_certificate"]))
    @foreach($data["second_addtional_certificate"] as $index=>$certificate)
    @php
    $paddingClass = $index == 0 ? 'py-1' : 'py-5';
    @endphp
    <div class="d-flex additional_certificate {{ $paddingClass }}" style="align-items: stretch;gap:2.6rem;">
        <div style="width:91%!important">
            <div class="row mt-3 category_and_subcategory_div">
                <div class="col-md-6 ">
                    <div class="login__form select_category_div">
                        <select class="common__login__input form-select clas add_select_category additional_certificate_error" data-allow-clear="1" type="text" name="additional_category[]" id="credentialing_agency_1745826268966">
                            <!-- <option value="" hidden=""> Select certificate/license type*</option> -->
                            @foreach ($data["category"] as $cat)
                            <option @if($certificate->category_id == $cat->id) selected @endif value="{{$cat->id}}">{{ $cat->name }}</option>
                            @endforeach
                            <option value="Other" {{ !is_numeric($certificate->category_id) ? 'selected' : '' }}>Other</option>
                        </select>

                        @if (is_numeric($certificate->category_id) && !is_numeric($certificate->sub_category_name))

                        @elseif (!is_numeric($certificate->category_id) && !is_numeric($certificate->sub_category_name))
                        <input value="{{$certificate->category_id}}" class="common__login__input other_category_input_field other_input my-3" placeholder="Enter Other Category Name*">
                        @endif
                    </div>
                </div>

                <!-- //sub_category_div -->
                <div class="col-md-6  ">
                    <div class="login__form other_select_subcategory_select_div">
                        <select class="common__login__input form-select add_sub_category additional_certificate_error" placeholder="Credentialing Agency*" data-allow-clear="1" type="text" name="additional_sub_category[]" id="credentialing_agency_1745826268966">
                            <!-- <option value="" hidden=""> Select certificate/license*</option> -->

                            @foreach ($cat->subCategory as $sub_cat)
                            <option @if($certificate->sub_category_name == $sub_cat->id) selected @endif value="{{$sub_cat->id}}">{{$sub_cat->name}}</option>
                            @endforeach
                            <!-- normal other case -->
                            @if (is_numeric($certificate->category_id) && !is_numeric($certificate->sub_category_name))
                            <option selected value="Other">Other</option>
                            @elseif (!is_numeric($certificate->category_id) && !is_numeric($certificate->sub_category_name))
                            <option value="Other" selected>Other</option>
                            @else
                            <option value="Other" >Other</option>

                            @endif

                        </select>

                        <!-- //this is for when we see the data while selecting in update other is not alreday coming -->
                        <div class="update_input_subcategory">

                        </div>

                        @if (!is_numeric($certificate->category_id) && !is_numeric($certificate->sub_category_name))
                        <input value="{{$certificate->sub_category_name}}" class="other_subcategory_input_field common__login__input my-3 other_input" placeholder="Enter Other Certification Name*">
                        @elseif (!is_numeric($certificate->sub_category_name))
                        <input value="{{$certificate->sub_category_name}}" class="other_subcategory_input_field common__login__input my-3 other_input" placeholder="Enter Other Certification Name*">
                        @endif
                    </div>
                </div>
            </div>

            <div class="row my-1">
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="login__form select sel" style="position: relative;">
                                <input class="common__login__input month_and_year_graduation teaching_certification_year additional_certificate_error employee-dates issue_date" type="text" placeholder="Issue date*" name="additional_issue_date[]" value="{{$certificate->issued_date}}" id="additional_issue_date" value="">
                                <svg onclick="teachingYearSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#000000" stroke-width="0.00024000000000000003">
                                    <g id="SVGRepo_bgCarrier_1745827748577" stroke-width="0"></g>
                                    <g id="SVGRepo_tracerCarrier_1745827748577" stroke-linecap="round" stroke-linejoin="round"></g>
                                    <g id="SVGRepo_iconCarrier_1745827748577">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z" fill="#aeaeae"></path>
                                    </g>
                                </svg>

                            </div>
                        </div>
                        <div class="col-md-6 add_cert_valid_till_div">
                            <div class="login__form select sel " style="position: relative;">
                                <input class="common__login__input month_and_year_graduation add_cert_valid_till_input additional_certificate_error employee-dates validTill_date" type="text" value="{{ $certificate->valid_till_date }}" placeholder="Valid till" name="additional_till_date[]" id="additional_till_date" {{ $certificate->valid_till_date === 'none' ? 'disabled' : '' }}> <svg onclick="teachingYearSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#000000" stroke-width="0.00024000000000000003">
                                    <g id="SVGRepo_bgCarrier_1745827748577" stroke-width="0"></g>
                                    <g id="SVGRepo_tracerCarrier_1745827748577" stroke-linecap="round" stroke-linejoin="round"></g>
                                    <g id="SVGRepo_iconCarrier_1745827748577">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z" fill="#aeaeae"></path>
                                    </g>
                                </svg>

                            </div>
                            <div class="mt-3 ms-1" style="display: flex; align-items: center; gap: 6px;">
                                <input type="checkbox" class="valid_till_check_box"
                                    value="{{$certificate->do_not_expire}}"
                                    name="additional_valid_till_check_box[]"
                                    style="width: 18px; height: 14px;"
                                    {{ $certificate->do_not_expire == 1 ? 'checked' : '' }}>

                                <label>Does not expire</label>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="col-md-6 upload_add_cert_div ">
                    <div class="login__form position-relative">
                        <label class="custom-file-input additional_certificate_error">
                               Certificates/Licenses
                            <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                </path>
                                <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                            <!-- //this one for the update -->
                            <div class="additional_certificate_upload_parent_div">
                                <input type="hidden" data-bs-file="{{$certificate->certificate}}" value="{{$certificate->certificate}}" name="exisiting_additional_certificates[]" class="exisiting_additional_certificates" value="{{$certificate->certificate}}" />
                                <input style="" data-bs-file="{{$certificate->certificate}}" class="add_cert_upload_input additional_certificate_error" type="file" id="additional_certificate_upload" name="additional_certificate_upload[]">
                            </div>
                        </label>
                        <span class="text-danger add_error_file_and_size" style="display:none;font-size:13px"></span>

                        <small style="font-size: 0.8rem; column-gap: 5px" class="flex-wrap d-flex py-3 text-muted justify-content-center d-block">
                            <i>Accepted file formats include *.pdf, *.doc, *.docx, *.png, *.jpg, and *.jpeg up to 1 MB</i>
                        </small>
                        <span class="uploaded-documents mt-3" style="margin-top:2%;width:43%;display:flex;justify-content:space-between; width:fit-content">
                            <span class="add_cert_upload_text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">
                                {{ basename($certificate->certificate) }}

                            </span>


                            <span style="z-index:5;cursor:pointer;" class="ms-2 remove-certificate remove_certificate_new" title="Remove">
                                <svg class="cros_icon_add_cert" style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                                    <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"></path>
                                </svg>
                            </span>
                        </span>
                        <div class="mt-3 ms-1" style="display: flex; align-items: center; gap: 6px;">
                            <input value="{{$certificate->visible_to_school}}"
                                type="checkbox"
                                class="upload_certification_checkbox"
                                style="width: 18px; height: 14px;"
                                {{ $certificate->visible_to_school == 1 ? 'checked' : '' }}>
                            <label>Make this document visible to the schools</label>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="d-flex  ps-3" style="border-left:1px solid #d8dadc; align-items: center;">
            <svg style="cursor:pointer" class="delete_additional_certificate" width="30px" height="30px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path fill="#a7a9b7" d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z"></path>
            </svg>
        </div>


    </div>


    @endforeach

    @endif

    <!-- <------------------******------------>



    <div class="col-xl-4 com-md-3 btn btn-info my-4 onboarding-step-btn add_additional_certificate_button mt-3 mb-5" style="pointer-events: all;"><span>+</span> Add additional certificate/license</div>

</div>


{{-- end of additional certificate --}}




{{-- Academics Section --}}
<div class="col-xl-12 col-md-12 mt-3 ">
    <div class="login__form form-title mt-5">
        <h4 class="form-heading onboarding-step-form">Education</h4>
        <span class="error_education text-danger error rederror"></span>
    </div>
</div>

<div class="appendAcademics">
    @if (
    !empty($data['second']) &&
    !empty($data['second']->highest_level_of_education) &&
    strlen($data['second']->highest_level_of_education) > 0)
    @php
    $ed = 0;
    $highest_level_edu = explode(',', $data['second']->highest_level_of_education);
    $other_education = !empty($data['second']->other_education) ? explode(',', $data['second']->other_education) : '';
    $month_and_year_graduation = explode(',', $data['second']->month_and_year_graduation);
    $GPA = explode(',', $data['second']->GPA);
    $transcript = explode(',', $data['second']->transcript);
    $transcript_name = explode(',', $data['second']->transcript_name);
    $transcript_visible_to_school = explode(',', $data['second']->transcript_visible_to_school);
    $major = explode(',', $data['second']->major);
    $minor = !empty($data['second']->minor) ? explode(',', $data['second']->minor) : '';
    $school_college_name = explode(',', $data['second']->school_college_name);
    $school_location = explode(';', $data['second']->school_location);
    @endphp
    @foreach ($highest_level_edu as $key => $rowusereducation)
    <div class="row">
        @php $ed++; @endphp
        <div class="col-lg-11">
            <div class="row">
                <div class="col-xl-6 col-md-6 removeeducation{{ $ed }}">
                    <div class="login__form">
                        <input class="common__login__input school_college_name" type="text"
                            placeholder="School/College Name*" name="school_college_name[]" id="school_college_name{{ $ed }}"
                            value="@if (!empty($rowusereducation) && !empty($school_college_name[$key])){{ $school_college_name[$key] }}@endif">
                    </div>
                    <span class="error_education text-danger error rederror"></span>
                </div>
                <div class="col-xl-6 col-md-6 removeeducation{{ $ed }}">
                    <div class="login__form">
                        <input class="common__login__input school_location location_city_search" type="text"
                            placeholder="Location (City, State, Country)*" name="school_location[]" id="school_location{{ $ed }}"
                            value="@if (!empty($rowusereducation) && !empty($school_location[$key])){{ $school_location[$key] }}@endif">
                    </div>
                    <span class="error_education text-danger error rederror"></span>
                </div>
                <div class="col-xl-6 col-md-6 removeeducation{{ $ed }}">
                    <div class="login__form">
                        <select class="common__login__input form-select highest_level_of_education"
                            type="text" placeholder="Enter experience teaching ages"
                            name="highest_level_of_education[]"
                            id="highest_level_of_education{{ $ed }}">
                            <option value="">Level of Education*</option>
                            @if (!empty($data['education_list']))
                            @foreach ($data['education_list'] as $rowseducation)
                            <option value="{{ $rowseducation->education }}"
                                <?php
                                if ($rowusereducation == $rowseducation->education) {
                                    echo 'selected';
                                }
                                ?>>
                                {{ $rowseducation->education }}
                            </option>
                            @endforeach
                            @endif
                        </select>
                    </div>
                    <span class="error_education text-danger error rederror"></span>
                </div>

                <div class="col-xl-6 col-md-6 removeeducation{{ $ed }}">
                    <div class="login__form position-relative">
                        <input class="common__login__input month_and_year_graduation"
                            type="text" placeholder="Month and Year of Graduation*"
                            name="month_and_year_graduation[]"
                            id="month_and_year_graduation{{ $ed }}"
                            value="@if (!empty($rowusereducation) && !empty($month_and_year_graduation[$key])){{ $month_and_year_graduation[$key] }}@endif">
                        <svg onclick="graduationSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px"
                            height="20px" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg" stroke="#000000"
                            stroke-width="0.00024000000000000003">
                            <g id="SVGRepo_bgCarrier" stroke-width="0" />
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <g id="SVGRepo_iconCarrier">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z"
                                    fill="#aeaeae" />
                            </g>
                        </svg>
                    </div>
                    <span id="month_and_year_graduation_error" class="error rederror"></span>
                </div>
                <div class="col-xl-6 col-md-6 otherEducation-section removeeducation{{ $ed }} @if(!empty($rowusereducation) && !empty($other_education)) @if(!empty($other_education[$key])) @else d-none @endif @else d-none @endif">
                    <div class="login__form">
                        <input class="common__login__input other_degree" type="text" placeholder="Other Education*" name="other_degree[]" id="other_degree" value="{{ !empty($other_education[$key]) ? $other_education[$key] : '' }}">
                    </div>
                </div>
                <div class="offset-6 otherEducation-section @if(!empty($rowusereducation) && !empty($other_education)) @if(!empty($other_education[$key])) @else d-none @endif @else d-none @endif"></div>

                <div class="col-xl-6 col-md-6 removeeducation{{ $ed }}">
                    <div class="login__form">
                        <input class="common__login__input major" type="text"
                            placeholder="Major*" name="major[]" id="major{{ $ed }}"
                            value="@if (!empty($rowusereducation) && !empty($major[$key])){{ $major[$key] }}@endif">
                    </div>
                    <span class="error_education text-danger error rederror"></span>
                </div>

                <div class="col-xl-6 col-md-6 removeeducation{{ $ed }}">
                    <div class="login__form">
                        <input class="common__login__input minor" type="text"
                            placeholder="Minor (Optional)" name="minor[]"
                            id="minor{{ $ed }}"
                            value="@if (!empty($rowusereducation) && !empty($minor[$key])){{ $minor[$key] }}@endif">
                    </div>
                </div>

                <div class="col-xl-6 col-md-6 removeeducation{{ $ed }}">
                    <div class="login__form">
                        <input class="common__login__input GPA1" type="text"
                            placeholder="Enter GPA*" name="GPA[]" id="GPA{{ $ed }}"
                            onkeypress="validate(event)"
                            value="@if (!empty($rowusereducation) && !empty($GPA[$key])) {{ $GPA[$key] }} @endif">
                    </div>
                    <span id="GPA_error" class="error rederror"></span>
                </div>

                <div class="col-xl-6 col-md-6 removeeducation1">
                    <div class="login__form d-flex position-relative align-items-center gap-5">
                        <input type="hidden" class="transcript_value" name="transcript_value[]"
                            id="transcript_value"
                            value="@if (!empty($rowusereducation) && !empty($transcript[$key])){{ $transcript[$key] }}@else @endif">
                        <input type="hidden" class="transcript_name" name="transcript_name[]"
                            id="transcript_name"
                            value="@if (!empty($rowusereducation) && !empty($transcript_name[$key])){{ $transcript_name[$key] }}@else @endif">
                        <input type="file" id="transcript"
                            class="transcriptfile form-control second transcript" name="transcript[]" accept=".doc,.docx,.pdf,.png,.jpg,.jpeg"
                            style="padding: 18px; border-radius: 25px; opacity: 0; position: absolute; width: 100%;">
                        <span class="transcript_text" style="padding: 12px 20px;border: 1px solid #d8dadc;width: 100%;display: flex;border-radius: 32px;justify-content: space-between;color: #757575;align-items: center;">
                            Upload Transcript*
                            <svg width="17" height="17" viewBox="0 0 17 17"
                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
                                    stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round">
                                </path>
                                <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7"
                                    stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round">
                                </path>
                                <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5"
                                    stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </span>
                    </div>
                    <span class="text-danger transcript_err" style="font-size: 13px;"></span>
                    <small style="font-size: 0.8rem; column-gap: 5px" class="flex-wrap d-flex py-3 text-muted justify-content-center d-block">
                        <i>Accepted file formats include *.pdf, *.doc, *.docx, *.png, *.jpg, and *.jpeg up to 1 MB</i>
                    </small>
                    @if (!empty($rowusereducation) && !empty($transcript_name[$key]))
                    <span class="uploaded-documents">
                        <span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">
                            {{ $transcript_name[$key] }}
                        </span>
                        <span style="position:relative;z-index:5;cursor:pointer" id="remove_transscript" class="ms-2 remove-transcript" title="Remove">
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11 1L1 11M1 1L11 11" stroke="#004CBD" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </span>
                    </span>
                    @endif

                    <div class="checkbox d-flex gap-2 mt-3 transcript-group">
                        <input type="checkbox"
                            {{ !empty($rowusereducation) && !empty($transcript_visible_to_school[$key]) ? 'checked' : '' }}
                            class="transcript_visible_to_school"
                            name="transcript_visible_to_school[]"
                            id="transcript_visible_to_school">
                        <input type="hidden" class="check_transcript" name="check_transcript[]"
                            id="check_transcript"
                            value="{{ !empty($rowusereducation) && !empty($transcript_visible_to_school[$key]) ? 1 : 0 }}">
                        <label for="transcript_visible_to_school">Make this Document visible to
                            the
                            schools</label>
                    </div>
                </div>
            </div>


            <div class="clearfix"></div>
            <input type="hidden" id="educationid" value="{{ $ed }}">
        </div>
        <div class="col-lg-1 pt-3">
            <div class="d-flex justify-content-center align-items-center h-100"
                style="border-left:1px solid #d8dadc;">
                <svg onclick="DeleteOnboardingStep(this,'academics')" width="30px"
                    height="30px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                    <path fill="#a7a9b7"
                        d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z" />
                </svg>

            </div>
        </div>
        @if (!$loop->last)
        <hr style="margin: 25px 0; width: 95.5%">
        @endif
    </div>
    @endforeach
    @endif
</div>

<div class="row mt-4" style="padding-left: 27px;">
    <div class="col-xl-3 com-md-3 btn btn-info onboarding-step-btn @if (!empty($data['second']) && !empty($highest_level_edu)) addMoreAcademics @endif"
        id="add-academics">
        <span>+</span>
        @if (!empty($data['second']) && !empty($highest_level_edu)) Add More
        @else
        Add Education @endif
    </div>
</div>
{{-- Experience Section --}}
<div class="row mt-5 mx-0 px-0">
    <div class="col-lg-11">
        <div class="row">

            <h3 class="onboarding-step-form onboarding-step3-form mt-5">Experience</h3>

            <h4 class="form-heading login__form mt-5">Relevant Experience (K-12)</h4>
            <div class="col-xl-6 col-md-6">
                <div class="login__form">
                    <input type="number" name="total_experience" id="total_experience"
                        class="common__login__input" placeholder="Number of years of relevant experience*"
                        min="0" step="1"
                        value="@if (!empty($data['second'])){{ $data['second']->total_experience }}@endif">
                </div>
            </div>
            <div class="col-xl-6 col-md-6">
                <div class="login__form">
                    {{-- <select class="main_experience_teaching_ages" multiple
                                    placeholder="Experience teaching grade levels" data-allow-clear="1"
                                    type="text" placeholder="Enter experience teaching ages"
                                    name="main_experience_teaching_ages[]" id="main_experience_teaching_ages">

                                    @if (!empty($data['class']))
                                        @foreach ($data['class'] as $rowsclassses)
                                            <option value="{{ $rowsclassses->class_name }}" @php if (!empty($data['second'])) {
                    $val = explode(',', $data['second']->main_experience_teaching_ages);
                    if (in_array($rowsclassses->class_name, $val)) {
                    echo 'selected';
                    }
                    } @endphp>
                    {{ $rowsclassses->class_name }}
                    </option>
                    @endforeach
                    @endif
                    </select> --}}
                </div>
            </div>
        </div>
    </div>
</div>
<div class="apendExperience">
    @if (!empty($data['second']) && !empty($data['second']->teching) && count($data['second']->teching) > 0)
    @foreach ($data['second']->teching as $key => $rowusereducation)
    <div class="row">
        <div class="col-lg-11">
            <div class="row">
                <div class="col-xl-6 col-md-6">
                    <div class="login__form select sel" style="position: relative;">
                        <input class="common__login__input employer_name" type="text"
                            placeholder="Employer Name*" name="employer_name[]"
                            id="employer_name" value="{{ $rowusereducation->employer_name }}">
                    </div>
                    <span id="employer_name_error" class="error rederror"></span>
                </div>

                <div class="col-xl-6 col-md-6">
                    <div class="login__form select sel" style="position: relative;">
                        <input class="common__login__input location location_city_search"
                            type="text" placeholder="Location (City, State, Country)*" name="location[]"
                            id="location" value="{{ $rowusereducation->location }}">
                    </div>
                    <span id="location_error" class="error rederror"></span>
                </div>

                <div class="col-xl-6 col-md-6">
                    <div class="login__form select sel" style="position: relative;">
                        <input class="common__login__input position" type="text"
                            placeholder="Position*" name="position[]" id="position"
                            value="{{ $rowusereducation->position }}">
                    </div>
                    <span id="position_error" class="error rederror"></span>
                </div>

                <div class="col-xl-6 col-md-6">
                    <div class="row position-date">
                        <div class="col-xl-6 col-md-6 login__form start-date position-relative"
                            id="start_date_div6">
                            <input class="common__login__input employee-dates start_date"
                                type="text" placeholder="Start Date*" name="start_date[]"
                                id="start_date" value="{{ $rowusereducation->start_date }}">
                            <svg onclick="startDateSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px"
                                height="20px" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg" stroke="#000000"
                                stroke-width="0.00024000000000000003">
                                <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <g id="SVGRepo_iconCarrier">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z"
                                        fill="#aeaeae" />
                                </g>
                            </svg>
                            <span id="start_date_error" class="error rederror"></span>
                        </div>
                        <div class="col-xl-6 col-md-6 login__form end-date position-relative">
                            <input
                                class="common__login__input employee-dates end_date disable_end_date"
                                type="text" placeholder="End Date*" id="end_date_div6"
                                name="end_date[]" id="end_date"
                                value="{{ $rowusereducation->end_date }}" @if(!empty($rowusereducation->currently_working_here) && $rowusereducation->currently_working_here == 1) readonly style="background-color: #dadada" @endif>
                            <svg onclick="endDateSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px"
                                height="20px" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg" stroke="#000000"
                                stroke-width="0.00024000000000000003">
                                <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <g id="SVGRepo_iconCarrier">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z"
                                        fill="#aeaeae" />
                                </g>
                            </svg>

                            <span id="end_date_error" class="error rederror"></span>
                        </div>
                    </div>
                    <span id="teaching_since_error" class="error rederror"></span>
                </div>
                {{-- <div class="col-xl-6 col-md-6">
                                        <div class="login__form  sel">
                                            <select class="experience_teaching_ages" multiple
                                                placeholder="Experience teaching grade levels" data-allow-clear="1"
                                                type="text" placeholder="Enter experience teaching ages"
                                                name="experience_teaching_ages[{{ $key }}][]"
                id="experience_teaching_ages_{{ $key }}">

                @if (!empty($data['class']))
                @foreach ($data['class'] as $rowsclassses)
                <option value="{{ $rowsclassses->class_name }}"
                    @php if (!empty($data['second'])) {
                    $agesvalue=json_decode($rowusereducation->experience_teaching_ages);
                    if (in_array($rowsclassses->class_name, $agesvalue)) {
                    echo 'selected';
                    }
                    } @endphp>
                    {{ $rowsclassses->class_name }}
                </option>
                @endforeach
                @endif
                </select>
            </div>
            <span id="experience_teaching_ages_error" class="error rederror"></span>
        </div> --}}

        <div class="col-lg-12 col-md-12 d-flex align-items-center mt-3 gap-3 ps-4 justify-content-end">
            <input type="hidden" class="working_here_value" name="working_here_value[]" value="{{(!empty($rowusereducation->currently_working_here) && $rowusereducation->currently_working_here == 1) ? 1 : 0}}">
            <input type="checkbox" @if(!empty($rowusereducation->currently_working_here) && $rowusereducation->currently_working_here == 1) checked @endif class="currently_working_here"
            onclick="updateEndDateState(this)" name="currently_working_here[]"
            id="currently_working_here" value="working_here">
            <label for="currently_working_here">Currently working here</label>
        </div>

    </div>
</div>

@if (!$loop->first)
<div class="col-lg-1 pt-3">
    <div class="d-flex justify-content-center align-items-center h-100"
        style="border-left:1px solid #d8dadc;">
        <svg onclick="DeleteOnboardingStep(this,'experience')" width="30px"
            height="30px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path fill="#a7a9b7"
                d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z" />
        </svg>
    </div>
</div>
@endif
@if (!$loop->last)
<hr style="margin: 25px 0; width: 95.5%">
@endif
</div>
@endforeach
@endif
</div>
<div class="row mt-4" style="padding-left: 27px;">
    <div class="col-xl-3 com-md-3 btn btn-info onboarding-step-btn @if (!empty($data['second']) && !empty($data['second']->teching) && count($data['second']->teching) > 0) addMoreExperience @endif"
        id="add-experience">
        <span>+</span> Add Experience
    </div>
</div>
</div>
<div class="row mt-5">
    <h4 class="form-heading login__form mb-4">Other Work Experience</h4>
    <div class="apendOtherExperience">
        @if (!empty($data['second']) && !empty($data['second']->otherExper) && count($data['second']->otherExper) > 0)
        @foreach ($data['second']->otherExper as $key => $rowusereducation)
        <div class="row">
            <div class="col-lg-11">
                <div class="row">
                    <div class="col-xl-6 col-md-6 other-experience-form-input">
                        <div class="login__form select sel" style="position: relative;">
                            <input class="common__login__input other_employer_name" type="text"
                                placeholder="Employer Name*" name="other_employer_name[]"
                                id="other_employer_name"
                                value="{{ $rowusereducation->employer_name }}">
                        </div>
                        <span id="teaching_since_error" class="error rederror"></span>
                    </div>

                    <div
                        class="col-xl-6 col-md-6 other-experience-form-input d-flex align-items-center">
                        <div class="login__form select sel position-relative w-100">
                            <input class="common__login__input other_location location_city_search"
                                type="text" placeholder="Location (City, State, Country)*" name="other_location[]"
                                id="other_location" value="{{ $rowusereducation->location }}">
                        </div>

                    </div>

                    <div class="col-xl-6 col-md-6 other-experience-form-input">
                        <div class="login__form select sel" style="position: relative;">
                            <input class="common__login__input other_position" type="text"
                                placeholder="Position*" name="other_position[]" id="other_position"
                                value="{{ $rowusereducation->position }}">
                        </div>
                        <span id="teaching_since_error" class="error rederror"></span>
                    </div>

                    <div class="col-xl-6 col-md-6 other-experience-form-input">
                        <div class="row position-date">
                            <div class="col-xl-6 col-md-6 login__form start-date position-relative">
                                <input class="common__login__input employee-dates other_start_date"
                                    type="text" placeholder="Start Date*"
                                    name="other_start_date[]" id="other_start_date"
                                    value="{{ $rowusereducation->start_date }}">
                                <svg onclick="otherStartDateSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px"
                                    height="20px" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" stroke="#000000"
                                    stroke-width="0.00024000000000000003">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <g id="SVGRepo_iconCarrier">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z"
                                            fill="#aeaeae" />
                                    </g>
                                </svg>
                            </div>
                            <div class="col-xl-6 col-md-6 login__form end-date position-relative">
                                <input
                                    class="common__login__input employee-dates other_end_date position-relative"
                                    type="text" placeholder="End Date*" name="other_end_date[]"
                                    id="other_end_date" value="{{ $rowusereducation->end_date }}" @if(!empty($rowusereducation->currently_working_here) && $rowusereducation->currently_working_here == 1) readonly style="background-color: #dadada" @endif>
                                <svg onclick="otherEndDateSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px"
                                    height="20px" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" stroke="#000000"
                                    stroke-width="0.00024000000000000003">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <g id="SVGRepo_iconCarrier">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z"
                                            fill="#aeaeae" />
                                    </g>
                                </svg>
                            </div>
                        </div>
                        <span id="teaching_since_error" class="error rederror"></span>
                    </div>

                    <div class="col-lg-12 col-md-12 d-flex align-items-center mt-3 gap-3 justify-content-end">
                        <input type="hidden" class="other_working_here_value" name="other_working_here_value[]" value="{{(!empty($rowusereducation->other_currently_working_here) && $rowusereducation->other_currently_working_here == 1) ? 1 : 0}}">
                        <input type="checkbox" @if(!empty($rowusereducation->currently_working_here)) checked @endif class="other_currently_working_here"
                        name="other_currently_working_here[]" id="other_currently_working_here"
                        value="other_working_here" onclick="updateOtherEndDateState(this)">
                        {{-- <input type="hidden" class="check_working_here" name="check_working_here[]"
                                            id="check_working_here" value="working_here"> --}}
                        <label for="other_currently_working_here">Currently working here</label>
                    </div>
                </div>
            </div>
            <div class="col-lg-1 pt-3">
                <div class="d-flex justify-content-center align-items-center h-100"
                    style="border-left:1px solid #d8dadc;">
                    <svg onclick="DeleteOnboardingStep(this,'other')" width="30px" height="30px"
                        viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path fill="#a7a9b7"
                            d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z" />
                    </svg>
                </div>
            </div>
            @if (!$loop->last)
            <hr style="margin: 25px 0; width: 95.5%">
            @endif
        </div>

        @endforeach
        @endif
    </div>
    <div class="row mt-3" style="padding-left: 27px;">
        <div class="col-xl-3 com-md-3 btn btn-info onboarding-step-btn @if (!empty($data['second']) && !empty($data['second']->otherExper) && count($data['second']->otherExper) > 0) addMoreOtherExperience @endif"
            id="add-other-experience">
            <span>+</span> Add Experience
        </div>
    </div>
</div>

<div class="row mt-5">
    <div class="col-lg-6 col-xl-6 col-md-12 classfile">
        <div class="mb-3 upload">
            <h4 class="form-heading">Resume
                <span class="filesizecss"><i></i></span>
            </h4>
            <input type="file" id="myFile" class="fileinput form-control myFile second resumefile"
                name="resume" class="myFile" accept=".doc,.docx,.ppt,.pptx,.txt,.pdf">
            <small style="font-size: 0.9rem; column-gap: 5px" class="flex-wrap d-flex py-3 text-muted d-block">
                <i>Accepted file formats include *.pdf, *.doc, *.docx up to 2 MB</i>
            </small>
            <span class="uploaded-documents @if (!empty($data['second']) && !empty($data['second']->resume_name)) @else d-none @endif">
                <span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">
                    {{ (!empty($data['second']) && !empty($data['second']->resume_name)) ? $data['second']->resume_name : '' }}
                </span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 " title="Remove" id="resume_remove">
                    <svg style="cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z" />
                    </svg>
                </span>
            </span>
            <span id="resume_error" class="error rederror fierror"></span>

            <div class="text-icon selectfile" style="z-index: 0;">
                <span class="text resumefiles gray">
                    Upload your resume*
                </span>
            </div>
            <span class="text-icon-2">
                <span class="icon selectfile ">
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
                            stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        </path>
                        <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5"
                            stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
                            stroke-linejoin="round"></path>
                    </svg>
                </span>
            </span>
        </div>
        <div class="login__form" style="position: relative;">
        </div>
        <input type="hidden" value="@if (!empty($data['second'])){{ $data['second']->resume }}@endif"
            id="resume_value" name="resume_value">
        <input type="hidden" value="@if (!empty($data['second'])){{ $data['second']->resume_name }}@endif"
            id="resume_name" name="resume_name">
    </div>
</div>

<div class="row mt-5">
    <!-- <h3 class="onboarding-step-form onboarding-step3-form mt-5">References</h3> -->
    <h4 class="form-heading login__form mb-4">References</h4>
    <small style="font-size: 0.9rem; column-gap: 5px" class="flex-wrap d-flex py-3 text-muted justify-content-start d-block">
        <i>Note : Whizara will reach out to references as a part of the application process</i>
    </small>
    <div class="appendReferences">
        @if (!empty($data['second']) && !empty($data['second']->references) && count($data['second']->references) > 0)
        @foreach ($data['second']->references as $key => $rowuserreference)
        <div class="row">
            <div class="col-lg-11">
                <div class="row">
                    <div class="col-xl-6 col-md-6 references-form-input">
                        <div class="login__form select sel" style="position: relative;">
                            <input class="common__login__input full_name" type="text"
                                placeholder="Full Name*" name="full_name[]"
                                id="full_name"
                                value="{{ $rowuserreference->full_name }}">
                        </div>
                    </div>

                    <div class="col-xl-6 col-md-6 references-form-input">
                        <div class="login__form select sel position-relative w-100">
                            <input class="common__login__input email"
                                type="text" placeholder="Email*" name="email[]"
                                id="email" value="{{ $rowuserreference->email }}">
                        </div>
                        <small class="text-danger email_error"></small>
                    </div>

                    <div class="col-xl-6 col-md-6 references-form-input">
                        <div class="login__form select sel position-relative w-100">
                            <input class="common__login__input phone"
                                type="text" placeholder="Phone*" name="phone[]"
                                id="phone" value="{{ $rowuserreference->phone }}">
                        </div>
                        <small class="text-danger phone_error"></small>
                    </div>

                    <div class="col-lg-12 col-md-12 d-flex align-items-center mt-3 gap-3 justify-content-start reference_visible_checkbox">
                        <input type="hidden" class="reference_visible_value" name="reference_visible_value[]" value="{{(!empty($rowuserreference->reference_visible) && $rowuserreference->reference_visible == 1) ? 1 : 0}}">
                        <input type="checkbox" @if(!empty($rowuserreference->reference_visible)) checked @endif class="reference_visible"
                        name="reference_visible[]" id="reference_visible"
                        onclick="updateReferenceVisible(this)">
                        <label for="reference_visible">Make this visible to schools</label>
                    </div>
                </div>

                @if (!$loop->last)
                <hr style="margin: 25px 0; width: 95.5%">
                @endif
            </div>
            @if (!$loop->first)
            <div class="col-lg-1 pt-3">
                <div class="d-flex justify-content-center align-items-center h-100"
                    style="border-left:1px solid #d8dadc;">
                    <svg onclick="DeleteOnboardingStep(this,'reference')" width="30px" height="30px"
                        viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path fill="#a7a9b7"
                            d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z" />
                    </svg>
                </div>
            </div>
            @endif
        </div>
        @endforeach
        @endif
    </div>
    <div class="row mt-3" style="padding-left: 27px;">
        <div class="col-xl-3 com-md-3 btn btn-info onboarding-step-btn" id="add-reference-experience">
            <span>+</span> Add Reference
        </div>
    </div>
</div>
{{-- Additional Tools Start --}}
<h4 class="mt-5" style="color:#004CBD;font-weight:500;padding-top:2rem;margin-bottom:0rem;margin-bottom:0.7rem">Tools</h4>
<div class="row">
    <div class="col-lg-11">
        <div class="row">
            <div class="col-lg-9">
                <div class="login__form">
                    <input class="common__login__input" type="text" placeholder="Any additional tools that are not already mentioned under additional certificate/license" name="additional_tools" id="additional_tools">
                </div>
                <span class="text-danger fs-6 tools_err"></span>
            </div>

            <div id="tolls-container" class="tools-container pt-5 d-lg-block d-none" style="display: flex; flex-wrap: wrap; max-width: 750px"></div>

            <div class="row tags-form mt-lg-3">
                <input type="hidden" name="tools_hidden" id="tools_hidden" value="{{ !empty($data['second']) && !empty($data['second']->tools) ? $data['second']->tools : '' }}">
            </div>
        </div>
    </div>
</div>
{{-- Additional Tools End --}}

</form>
</div>