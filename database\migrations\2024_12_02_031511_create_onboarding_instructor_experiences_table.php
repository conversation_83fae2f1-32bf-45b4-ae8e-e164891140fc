<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOnboardingInstructorExperiencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('onboarding_instructor_experiences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('new_onboarding_instructor')->onDelete('cascade');
            $table->string('certification')->nullable();
            $table->string('profile_type')->nullable();
            $table->string('specify')->nullable();
            $table->string('teaching_certification_year')->nullable();
            $table->string('teaching_certification_states')->nullable();
            $table->string('certified_special_education')->nullable();
            $table->string('teaching_since')->nullable();
            $table->text('experience_teaching_ages')->nullable();
            $table->string('highest_level_of_education')->nullable();
            $table->string('month_and_year_graduation')->nullable();
            $table->string('GPA')->nullable();
            $table->text('certified_other')->nullable();
            $table->string('resume')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('onboarding_instructor_experiences');
    }
}
