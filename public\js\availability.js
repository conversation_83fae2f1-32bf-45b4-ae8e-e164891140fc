$(document).ready(function () {
    var maxField = 200; //Input fields increment limitation

    var x = 1; //Initial field counter is 1
    if ($('#onlinesloatid').val()) {
        var qqq = $('#onlinesloatid').val();
    } else {
        var qqq = 1;
    }

    // Once add button is clicked
    $('body').on('click', '.add_button_online_slots', function (e) {

        var id = $(this).attr('data-id');
        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            qqq++;
           
            if ($(this).attr('data-day') == undefined) {
                var day = '<option value="">Select day of week</option>';
            } else {
                var day = $(this).attr('data-day');

            }


            fetchFieldsHtml({
                qqq: qqq,
                id: id,
                day: day,
                view: 'online-time-slot',
            }, function (result) {
                // $('.appendrange' + id).append(result); //Add field html
                $(e.target).closest('.m-top-avail').parent().append(result);

                var currentDate = new Date();
        var cd = (currentDate.getMonth() + 1) + '/' +
         currentDate.getDate() + '/' +
         currentDate.getFullYear();

                $(".from_date").each(function () {
                    $(this).attr('min', cd);
                    $(this).attr('data-date-start-date', cd);
                })
                initializeTimepicker();
                initializeBootstrapDatepicker();
                $(e.target).closest('.parent-add-button').hide();
            });



        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $('body').on('click', '.removerange', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');
        if ($(this).hasClass('can-not-remove')) {

            window.showAlert = function () {
                alertify.alert(changeMyAvalibilityAlert).setHeader('');
            }

            alertify.alert().setting('modal', true);

            window.showAlert();

            return true;
        }
        var $removeParent = $(this).closest('.login__form');
        var $addParent = $removeParent.next();

        var $removeParentTop = $removeParent.closest('.m-top-avail');
        if ($addParent.hasClass('parent-add-button') && $addParent.is(":visible")) {
            var $button = $removeParentTop.prevAll('.m-top-avail:first').find('.parent-add-button');
            if ($button.length > 0) {
                $button.show();
            } else {
                /* remove date range if time slots are zero */
                $removeParentTop.closest('.in-person-range-box').find('.removedate')[0].click();
            }

        }

        $removeParentTop.prev().remove();
        $removeParentTop.remove();
        // $('.removerange' + id).remove(); //Remove field html
        qqq--;
        x--; //Decrease field counter
    });
});


$(document).ready(function () {
    var maxField = 10; //Input fields increment limitation
    var addButton = $('.addonlinerange'); //Add button selector
    var wrapper = $('.repeat-box-online'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if ($('#onlinedatarange').val() != "") {
        var rang = $('#onlinedatarange').val();
    } else {
        var rang = 1;
    }


    // Once add button is clicked
    $(addButton).click(function () {
        $(wrapper).show();
        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            rang++;
            //var timezone=$('#teach_online_timezone').html();
            fetchFieldsHtml({
                rang: rang,
                view: 'online-date-range',
            }, function (result) {
                $(wrapper).append(result); //Add field html
                var currentDate = new Date();
        var cd = (currentDate.getMonth() + 1) + '/' +
         currentDate.getDate() + '/' +
         currentDate.getFullYear();

                $(".from_date").each(function () {
                    $(this).attr('min', cd);
                    $(this).attr('data-date-start-date', cd);
                });
                initializeTimepicker();
                initializeBootstrapDatepicker();
            });


            var currentDate = new Date();
        var cd = (currentDate.getMonth() + 1) + '/' +
         currentDate.getDate() + '/' +
         currentDate.getFullYear();

            $(".from_date").each(function () {
                $(this).attr('min', cd);
                $(this).attr('data-date-start-date', cd);
            });
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $('body').on('click', '.removeonline', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');


        $('.reonline' + id).remove(); //Remove field html
        x--; //Decrease field counter
        rang--;
    });
});

$(document).ready(function () {
    var maxField = 10; //Input fields increment limitation
    var addButton = $('.adddate'); //Add button selector


    var x = 1; //Initial field counter is 1
    if ($('#dateid').val() != "") {
        var ranga = $('#dateid').val();
    } else {
        var ranga = 1;
    }
    $('body').on('click', '.adddate', function (e) {

        var id = $(this).attr('data-id');
        var ids = $(this).attr('data-id');
        var step = $(this).attr('data-step');
        var arr = step.split('-');

        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            ranga++;
            var rg = parseInt($(this).attr('data-range')) + 1;

            $(this).attr('data-range', rg);
            id++;
            var r = parseInt(arr[1]) + 1;
            var dateid = ranga;

            fetchFieldsHtml({
                id: id,
                ids: ids,
                dateid: dateid,
                ranga: ranga,
                rg: rg,
                view: 'in-person-data-range',
            }, function (result) {

                //  $('.in-person-range-box:last').after(result); //Add field html
                $(e.target).closest('.addremovemargin').html(result);
                var currentDate = new Date();
        var cd = (currentDate.getMonth() + 1) + '/' +
         currentDate.getDate() + '/' +
         currentDate.getFullYear();

                $(".from_date").each(function () {
                    $(this).attr('min', cd);
                    $(this).attr('data-date-start-date', cd);
                });
                initializeTimepicker();
                initializeBootstrapDatepicker();
            });




            var currentDate = new Date();
        var cd = (currentDate.getMonth() + 1) + '/' +
         currentDate.getDate() + '/' +
         currentDate.getFullYear();

            $(".from_date").each(function () {
                $(this).attr('min', cd);
                $(this).attr('data-date-start-date', cd);
            });
        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $('body').on('click', '.removedate', function (e) {
        e.preventDefault();
        // var step=$(this).attr('data-id');
      
        
        if ($(this).hasClass('can-not-remove')) {

            window.showAlert = function () {
                alertify.alert(changeMyAvalibilityAlert).setHeader('');
            }

            alertify.alert().setting('modal', true);

            window.showAlert();

            return true;
        }

        var count = $(".rangerowcount > div").children().length;

        if(count==2){
         
            $('.check_same_as_online ').prop('checked', false);
            $('.in-person-range-box').show();
            $('.in-person-range-box').nextAll().show();
            $('.same_as_online').val(0);

        }

        const $removeBox = $(this).closest('.in-person-range-box');
        const $repeatBox = $removeBox.closest('.repeat-box-online');

        $removeBox.remove();
        if ($repeatBox.find('.in-person-range-box').length == 0) {
            $repeatBox.hide();
        }
        //$('.date'+step).remove(); //Remove field html
        x--; //Decrease field counter
        ranga--;
    });
});


$(document).ready(function () {
    var maxField = 200; //Input fields increment limitatio

    var x = 1; //Initial field counter is 1
    if ($('#inpersonslot').val()) {
        var qq = $('#inpersonslot').val();
    } else {
        var qq = 1;
    }
    var sloat = 0;
    // Once add button is clicked
    $('body').on('click', '.add_button_inperson_slots', function (e) {

        var id = $(this).attr('data-id');
        var step = $(this).attr('data-step');
        var arr = step.split('-');
        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            qq++;
            sloat++;
            // var timezone=$('.tzone').html();
            // var days=$(this).attr('data-day');
            if ($(this).attr('data-day') == undefined) {
                var days = '<option value="">Select day of week</option>';
            } else if ($(this).attr('data-day') != "") {
                var days = $(this).attr('data-day');
            } else {
                var days = '<option value="">Select day of week</option>';
            }

            fetchFieldsHtml({
                qq: qq,
                days: days,
                arr0: arr[0],
                arr1: arr[1],
                view: 'in-person-time-slot',
            }, function (result) {
                $(e.target).closest('.m-top-avail').parent().append(result);

                //$('.appendrangeinperson' + step).append(result);
                var currentDate = new Date();
        var cd = (currentDate.getMonth() + 1) + '/' +
         currentDate.getDate() + '/' +
         currentDate.getFullYear();

                $(".from_date").each(function () {
                    $(this).attr('min', cd);
                    $(this).attr('data-date-start-date', cd);
                });
                initializeTimepicker();
                initializeBootstrapDatepicker();
                $(e.target).closest('.parent-add-button').hide();

            });


        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $('body').on('click', '.removeinpersonrange', function (e) {
        e.preventDefault();
        var id = $(this).attr('data-id');
        if ($(this).hasClass('can-not-remove')) {

            window.showAlert = function () {
                alertify.alert(changeMyAvalibilityAlert).setHeader('');
            }

            alertify.alert().setting('modal', true);

            window.showAlert();

            return true;
        }
        var $removeParent = $(this).closest('.login__form');
        var $addParent = $removeParent.next();

        var $removeParentTop = $removeParent.closest('.m-top-avail');
        if ($addParent.hasClass('parent-add-button') && $addParent.is(":visible")) {
            var $button = $removeParentTop.prevAll('.m-top-avail:first').find('.parent-add-button');

            if ($button.length > 0) {
                $button.show();
            } else {
                /* remove date range if time slots are zero */
                $removeParentTop.closest('.in-person-range-box').find('.removedate')[0].click();
            }
        }

        $removeParentTop.prev().remove();
        $removeParentTop.remove();

        // $('.removeinpersonsloat' + id).remove(); //Remove field html
        x--; //Decrease field counter
        qq--;
        sloat--;
    });
});

$(document).ready(function () {
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.addinpersonrange'); //Add button selector
    var wrapper = $('.appendrangeinperson')[0]; //Input field wrapper

    var x = 1; //Initial field counter is 1
    var so = 0;

    if ($('#inpersondatarange').val()) {
        var rangg = $('#inpersondatarange').val();
    } else {
        var rangg = 1;
    }

    var time = 1;
    // Once add button is clicked
    $(addButton).click(function () {

        //Check maximum number of input fields
        if (x < maxField) {
            x++; //Increase field counter
            rangg++;
            so++;
            time++;

            var typeav = $('#avtype').val();
            var view = 'in-person-location';
            if (typeav == "both") {
                view = 'both-in-person-location';
            }

            fetchFieldsHtml({
                rangg: rangg,
                so: so,
                view: view,
            }, function (result) {

                $(wrapper).append(result);
                var currentDate = new Date();
        var cd = (currentDate.getMonth() + 1) + '/' +
         currentDate.getDate() + '/' +
         currentDate.getFullYear();

                $(".from_date").each(function () {
                    $(this).attr('min', cd);
                    $(this).attr('data-date-start-date', cd);
                });
                // let locationVal = Number($('#inpersondatarange').val());

                // $('#inpersondatarange').val((locationVal+1));
                initializeTimepicker();
                initializeBootstrapDatepicker();
            });




        } else {
            alert('A maximum of ' + maxField + ' fields are allowed to be added. ');
        }
    });

    // Once remove button is clicked
    $('body').on('click', '.removeloclat', function (e) {
        
        e.preventDefault();

        var id = $(this).attr('data-id');
        rangg--;
        $('.removelocation' + id).remove(); //Remove field html
        
        x--; //Decrease field counter
        so--;
        time--;
    });
});


$('body').on('change', '.from_date', function () {

    var fromdate = $(this).val();
    var id = $(this).attr('data-date-id');
    var type = $(this).attr('data-date-type');
    if (type == 'online') {
        $(this).parents('.select-cl-align').find('.reonline' + id).find('.to_date').val('');
        $(this).parents('.select-cl-align').find('.reonline' + id).find('.to_date').attr('min', fromdate);
        $(this).parents('.select-cl-align').find('.reonline' + id).find('.to_date').attr('data-date-start-date', fromdate);
        $(this).parents('.select-cl-align').find('.reonline' + id).find('.to_date').datepicker('destroy');
        $(this).parents('.select-cl-align').find('.reonline' + id).find('.to_date').datepicker({startDate: fromdate,autoclose: true});
        
    }
    if (type == 'inperson') {

        $(this).parent().parent().parent().next('div').next("div").eq(0).find('input').val('');
        $(this).parent().parent().parent().next('div').next("div").eq(0).find('input').attr('min', fromdate);
        $(this).parent().parent().parent().next('div').next("div").eq(0).find('input').attr('data-date-start-date', fromdate);
        $(this).parent().parent().parent().next('div').next("div").eq(0).find('input').datepicker('destroy');
        $(this).parent().parent().parent().next('div').next("div").eq(0).find('input').datepicker({startDate: fromdate,autoclose: true});
        
    }

})

$('body').on('click', '#submitAvailability', function () {

    var loading = "<i class='fa fa-spinner fa-spin'></i>Saving..";
    var teach_minimum = document.getElementById('teach_minimum').value;
    var teach_maximum = document.getElementById('teach_maximum').value;
    var teach_in_person_timezone = document.getElementById('teach_in_person_timezone').value;
    if (teach_minimum.length == "") {
        $("#teach_minimum").css("border-color", "red");
    } else {
        $("#teach_minimum").css("border-color", "");
    }

    if (teach_maximum.length == 0) {
        $("#teach_maximum").addClass('errorc');

    } else {
        $("#teach_maximum").removeClass('errorc');
        $("#teach_maximum").css("border-color", "");
    }

    if (teach_in_person_timezone.length == 0) {
        $("#teach_in_person_timezone").addClass('errorc');

    } else {
        $("#teach_in_person_timezone").removeClass('errorc');
        $("#teach_in_person_timezone").css("border-color", "");
    }
    if ($(".onlinerangeappend")[0] || $(".select-cl-align")[0]) {

        var onlinerange = true;

        $('.repeat-box input, .repeat-box select').each(function () {
            if ($(this).attr("name") && $(this).is(":visible")) {
                if ($(this).val() === "" || $(this).length === 0) {

                    onlinerange = false;
                    $(this).css("border-color", "red");
                } else {
                    $(this).css("border-color", "");
                }
            }
        });

    }
    var typeav = $('#avtype').val();
    if (typeav == "in_person") {
        var isValid = true;

        $('.appendrangeinperson').find('input,select').each(function () {

            if (this.hasAttribute("name") && $(this).is(":visible")) {

                if ($(this).val() == "" && $(this).val().length < 1) {
                    $(this).css("border-color", "red");
                    isValid = false;
                } else {
                    $(this).css("border-color", "");
                }
            }
        });

        var onlinerange2 = true;
        $('.slotrange').find('input,select').each(function () {

            if (this.hasAttribute("name") && $(this).is(":visible")) {
                if ($(this).val() == "") {
                    onlinerange2 = false;
                    $(this).css("border-color", "red");
                } else {
                    $(this).css("border-color", "");
                    onlinerange2 = true;
                }
            }
        });
    } else if (typeav == "both") {



        var isValid = true;
        if ($(".appendrangeinperson")[0]) {
            $('.appendrangeinperson').find('input,select').each(function () {

                if (this.hasAttribute("name") && $(this).is(":visible")) {


                    if ($(this).val() == "" && $(this).val().length < 1) {
                        $(this).css("border-color", "red");
                        isValid = false;
                    } else {
                        $(this).css("border-color", "");
                    }
                }
            });
        }
        var onlinerange2 = true;

        if ($(".slotrange")[0]) {

            $('.slotrange').find('input,select').each(function () {

                if (this.hasAttribute("name") && $(this).is(":visible")) {

                    if ($(this).val() == "") {
                        onlinerange2 = false;
                        $(this).css("border-color", "red");
                    } else {
                        onlinerange2 = true;
                        $(this).css("border-color", "");

                    }

                }
            });
        }


    } else {
        var isValid = true;
        var onlinerange2 = true;
    }



    var onlinerange1 = true;

    if ($(".aprange")[0]) {

        $('.aprange').find('input,select').each(function () {
            if (this.hasAttribute("name") && $(this).is(":visible")) {
                if ($(this).val() == "") {
                    // alert('345');

                    onlinerange1 = false;
                    $(this).css("border-color", "red");
                } else {
                    $(this).css("border-color", "");
                    onlinerange1 = true;
                }
            }
        });
    }



    if (parseInt(teach_maximum) < parseInt(teach_minimum)) {
        $("#teach_maximum").focus();
        $('#teach_maximum').css("border-color", "red");
        $('#teach_maximum_error').html('Maximum number should be greater than from minimun number')
        return false;
    } else {

        $('#teach_maximum').css("border-color", "");
        $('#teach_maximum_error').html('');

    }

    if (teach_in_person_timezone.length == 0) {
        $("#teach_in_person_timezone").addClass('errorc');
        return false;
    } else {
        $("#teach_in_person_timezone").removeClass('errorc');
        $("#teach_in_person_timezone").css("border-color", "");
    }
    var typeav = $('#avtype').val();
    if (typeav == "in_person") {
        var validate = onlinerange2 == true && isValid == true
    } else if (typeav == 'online') {
        var validate = onlinerange == true && onlinerange1 == true
    } else {





        var validate = onlinerange == true && onlinerange1 == true && onlinerange2 == true && isValid == true
    }
   

    if (validate) {
        var $timeInputs = $('.av-timepicker');
        var validationResults = [];
        $timeInputs.each(function () {
            var validationResult = validateTimeInputs($(this).closest('.timing'));
            validationResults.push(validationResult);
        });
        if (validationResults.some(result => !result)) {
            validate = false;
        }
      
    }



    if (validate) {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        var _this = $(this);
        var url = APP_URL + '/submitAvailability';
        $.ajax({
            type: 'POST',
            url: url,
            data: $('#avid').serialize(),
            dataType: "json",
            beforeSend: function () {
                $('.save-loader').show();
                // $('#submitAvailability').prop("disabled", true);
                // _this.innerHTML=loading;
            },
            success: function (data) {

                if (data.success == true) {
                    alertify.success(data.message);
                    // _this.innerHTML='Submit';
                    // _this.prop("disabled", false);
                    location.reload();
                } else {
                    _this.innerHTML = 'Submit';
                    // _this.prop("disabled", false);
                     alertify.error(data.message);
                }
            },
            error: function (res) {
                if (res.status === 422) {

                    var errorData = JSON.parse(res.responseText);
                    // alertify.error(errorData.message,'Error',10);
                    window.showAlert = function () {
                        alertify.alert(errorData.message).setHeader('');
                    }

                    //works with modeless too
                    alertify.alert().setting('modal', true);

                    window.showAlert();
                    $('.save-loader').hide();

                }
            }
        }).done(function () {
            // _this.innerHTML='Submit';
            // _this.prop("disabled", false);
            $('.save-loader').hide();

        });
    }
})

$('body').on('change', '.to_date', function () {

    var id = $(this).attr('data-date-id');
    var type = $(this).attr('data-date-type');
    var toDate = new Date($(this).val());
    
    if (type == 'online') {

        var fromDates = new Date($(this).parents('.select-cl-align').find('.reonline' + id).find('.from_date').val());

        var html = '<option value="">Select day of week</option>';
        // Loop through the days between the two dates
        var i = 0;
        for (var date = fromDates; date <= toDate; date.setDate(date.getDate() + 1)) {
            i++;

            var dayOfWeek = date.getDay();
            if (dayOfWeek == '0') {
                dayOfWeek = 7;
            }
            var dayName = getDayName(dayOfWeek);
            // var d = parseInt(dayOfWeek) + 1;
            if (dayName) {

                html += '<option value="' + dayOfWeek + '">' + dayName + '</option>';
            }
            if (i >= 7) {
                break;
            }
        }
        

        $(this).parents('.select-cl-align').next('div').find('select:not(.active-program-weekday)').each(function () {
            if (!$(this).val()) {
                $(this).html(html);
            }
        });

        $(this).parents('.select-cl-align').next('div').find('a').attr('data-day', html);

    }
    if (type == 'inperson') {

        var fromDates = new Date($(this).parents('.select-cl-align').find('.from_date').val());

        var html = '<option value="">Select day of week</option>';
        var i = 0;
        for (var date = fromDates; date <= toDate; date.setDate(date.getDate() + 1)) {
            i++;
            var dayOfWeek = date.getDay();
            if (dayOfWeek == '0') {
                dayOfWeek = 7;
            }
            var dayName = getDayName(dayOfWeek);
            // var d = parseInt(dayOfWeek) + 1;
            if (dayName) {
                html += '<option value="' + dayOfWeek + '">' + dayName + '</option>';

            }
            if (i >= 7) {
                break;
            }
        }
        

        $(this).parents('.select-cl-align').next('div').find('select:not(.active-program-weekday)').each(function () {
            if (!$(this).val()) {
                $(this).html(html);
            }
        });

        $(this).parents('.select-cl-align').next('div').find('a').attr('data-day', html);

    }
    removeDuplicateOptions();

})
function removeDuplicateOptions() {

    $('.form-select.date-ui option').each(function () {
        $(this).siblings('[value="' + $(this).val() + '"]').remove();
    });

}

function getDayName(dayIndex) {
    var daysOfWeek = {
        1: 'Monday',
        2: 'Tuesday',
        3: 'Wednesday',
        4: 'Thursday',
        5: 'Friday',
        6: 'Saturday',
        7: 'Sunday'
    };

    return daysOfWeek[dayIndex];
}


function fetchFieldsHtml(fData, callback) {
    var $view = '';

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        type: 'get',
        url: APP_URL + '/user/get-availability-fields',
        data: fData,
        dataType: "json",
        success: function (data) {
            if (data.status == true) {
                $view = data.view;
                callback($view);
                removeDuplicateOptions();

            }
        }
    });
}




$('body').on('change', '.check_same_as_online', function () {

    var checkParent = $(this).closest('.same_as_online_parent');

    if ($(this).is(":checked") == false) {

        $(checkParent).nextAll().show();
        $(this).next('.same_as_online').val(0);

    } else {

        $(checkParent).nextAll().hide();
        $(this).next('.same_as_online').val(1);

    }
})