<style>
    .tags-field {
        border: 1px solid;
        border-radius: 7px;
        font-size: 1rem;
        background-color: #7accd23d;
    }

    #delete_button:hover {
        color: white;
    }

    .disabled_video_upload {
        opacity: 0.6;
        pointer-events: none;
    }

    .recording-container {
        border: 1px solid #004CBD;
        padding: 10px 14px;
        border-radius: 30px;
        color: #004CBD;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }
    .video_container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1000;
    display: flex;
  }

  .video_container.hide {
    display: none;
  }

  .video_container::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
  }

  .video_container video {
    border-radius: 0.4rem;
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 50px);
  }

  .video_container span {
    right: 0;
    top: 0;
    transform: translate(60%, -60%);
    cursor: pointer;
    width: 20px;
    line-height: 20px;
  }
</style>
  
<form action="#" id="fifthid">

    {!! csrf_field() !!}
    <input id="instructor_video_url_s3" type="hidden" name="instructor_video_url_s3" value="{{(!empty($data["five"]) && !empty($data["five"]->video)) ? $data["five"]->video : ''}}">
    <input id="videoname" type="hidden" name="videoname" value="{{(!empty($data["five"]) && !empty($data["five"]->video_name)) ? $data["five"]->video_name : ''}}">
    <input type="hidden" name="video_source" id="video_source" value="{{(!empty($data["five"]) && !empty($data["five"]->video_source)) ? $data["five"]->video_source : ''}}">
    <input type="hidden" name="id" value="{{$data['id']}}">
    <div class="login__heading">
        <h5 class="login__title mt-5">Profile</h5>
    </div>
    <div class="row flex-lg-row flex-column-reverse">
        <div class="col-lg-6">
            <p class="form-heading">Profile details</p>
            <input type="text" name="profile_title" id="profile_title" class="form-control mt-5 p-2 profile_inputs"
                maxlength="100" placeholder="Profile Title* (100 characters max.)" maxlength="100"
                value="{{ !empty($data["five"]) ? $data["five"]->profile_title : '' }}">
            <textarea name="profile_desc" id="profile_desc" rows="5" class="form-control mt-3 profile_inputs"
                placeholder="Profile Overview* (500 characters max.)"
                maxlength="500">@if(!empty($data["five"])) {{ $data["five"]->description }} @endif</textarea>
            <input type="text" name="profile_tags" id="profile_tags" class="form-control mt-3 p-2 profile_inputs"
                placeholder="Profile Keywords*">
            <small style="font-size: 0.82rem; column-gap: 5px"
                class="flex-wrap d-flex px-2 py-3 text-muted text-start d-block">
                <i>Enter keywords separated by commas (e.g., Special Education, Elementary Math, Python Programming)</i>
            </small>
            <span class="text-danger fs-6 tags_err"></span>
            {{-- <p class="mt-3" style="font-style: italic;">(Any keywords using which a school can search you. E.g
                Special Education, Speech Therapy, Mental health etc.)</p> --}}
            <div id="tags-container" class="d-lg-none tags-container"
                style="display: flex; flex-wrap: wrap; max-width: 750px"></div>
        </div>
        <div class="col-lg-6 mt-lg-5">
            <div class="mt-lg-5 d-flex justify-content-center align-items-center flex-column"
                style="height:100%; max-height: 310px">
                <div class="upload_image">
                    @if (!empty($data["five"]) && !empty($data["five"]->profile_image))
                    @php
                    $profileImage = explode('/', $data["five"]->profile_image);
                    $imageName = explode('_', $profileImage[2]);
                    @endphp
                    @endif
                    <input type="hidden" name="image_data" id="image_data"
                        value="{{ !empty($data["five"]) && !empty($data["five"]->profile_image) ? $data["five"]->profile_image : '' }}">
                    <input type="file" name="profile_image" id="profile_image" accept=".png,.jpg,.jpeg" title="{{!empty($data["five"]) && !empty($data["five"]->profile_image) ? $imageName[1] : ''}}"
                        onchange="showUploadedImage(event)">
                    <span id="image_preview">
                        @if (!empty($data["five"]) && !empty($data["five"]->profile_image))
                        <img src="{{ generateSignedUrl($data["five"]->profile_image) }}" alt="image">
                        @else
                        <svg width="50px" height="50px" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="13" r="3" stroke="#1C274C" stroke-width="1.5" />
                            <path
                                d="M9.77778 21H14.2222C17.3433 21 18.9038 21 20.0248 20.2646C20.51 19.9462 20.9267 19.5371 21.251 19.0607C22 17.9601 22 16.4279 22 13.3636C22 10.2994 22 8.76721 21.251 7.6666C20.9267 7.19014 20.51 6.78104 20.0248 6.46268C19.3044 5.99013 18.4027 5.82123 17.022 5.76086C16.3631 5.76086 15.7959 5.27068 15.6667 4.63636C15.4728 3.68489 14.6219 3 13.6337 3H10.3663C9.37805 3 8.52715 3.68489 8.33333 4.63636C8.20412 5.27068 7.63685 5.76086 6.978 5.76086C5.59733 5.82123 4.69555 5.99013 3.97524 6.46268C3.48995 6.78104 3.07328 7.19014 2.74902 7.6666C2 8.76721 2 10.2994 2 13.3636C2 16.4279 2 17.9601 2.74902 19.0607C3.07328 19.5371 3.48995 19.9462 3.97524 20.2646C5.09624 21 6.65675 21 9.77778 21Z"
                                stroke="#1C274C" stroke-width="1.5" />
                            <path d="M19 10H18" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
                        </svg>
                        @endif
                        {{-- <svg width="50px" height="50px" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="13" r="3" stroke="#1C274C" stroke-width="1.5" />
                            <path
                                d="M9.77778 21H14.2222C17.3433 21 18.9038 21 20.0248 20.2646C20.51 19.9462 20.9267 19.5371 21.251 19.0607C22 17.9601 22 16.4279 22 13.3636C22 10.2994 22 8.76721 21.251 7.6666C20.9267 7.19014 20.51 6.78104 20.0248 6.46268C19.3044 5.99013 18.4027 5.82123 17.022 5.76086C16.3631 5.76086 15.7959 5.27068 15.6667 4.63636C15.4728 3.68489 14.6219 3 13.6337 3H10.3663C9.37805 3 8.52715 3.68489 8.33333 4.63636C8.20412 5.27068 7.63685 5.76086 6.978 5.76086C5.59733 5.82123 4.69555 5.99013 3.97524 6.46268C3.48995 6.78104 3.07328 7.19014 2.74902 7.6666C2 8.76721 2 10.2994 2 13.3636C2 16.4279 2 17.9601 2.74902 19.0607C3.07328 19.5371 3.48995 19.9462 3.97524 20.2646C5.09624 21 6.65675 21 9.77778 21Z"
                                stroke="#1C274C" stroke-width="1.5" />
                            <path d="M19 10H18" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
                        </svg> --}}
                    </span>
                </div>
                <p class="text-center mt-2 fs-6 text-secondary mb-0">Upload Profile Picture*</p>
                <small style="font-size: 0.9rem; column-gap: 5px"
                    class="flex-wrap d-flex py-3 text-muted justify-content-center d-block">
                    <i>Accepted file formats .jpg, .png, .jpeg</i>
                </small>
                 <small style="font-size: 0.9rem; column-gap: 5px"
                    class="flex-wrap d-flex py-1 text-muted justify-content-center d-block">
                    <i class="file_size_image_error">File size should be up to 2 MB</i>
                </small>
                <!-- <p class="text-center"  style="font-size: 0.9rem;width:57%"><i><span class="text-danger">Profile picture guidelines:</span> Your face should take up about 60% of the image. Crop from the top of your shoulders to just above your head</i></p> -->
                <p id="profile_image_error" class="text-center mt-2 fs-6 text-danger"></p>
                <span class="btn btn-danger d-none" id="btn-del">Delete</span>
                @if (!empty($data["five"]) && !empty($data["five"]->profile_image))
                <span class="btn btn-danger" id="delete_button" onclick="DeleteProfileImg(this)"
                    data-url="{{route('deleteProfileImg', ['id' => $data["five"]->id])}}">Delete</span>
                @endif
            </div>
        </div>
        <div id="tags-container" class="tags-container d-lg-block d-none"
            style="display: flex; flex-wrap: wrap; max-width: 750px"></div>

        <div class="row tags-form mt-lg-3">
            {{-- <p>(Any keywords using which a school can search you. E.g Special Education, Speech Therapy, Mental
                health etc.)</p> --}}
            <input type="hidden" name="tags_hidden" id="tags_hidden"
                value="{{ !empty($data['five']) ? $data['five']->profile_tags : '' }}">
        </div>
    </div>
</form>

<div class="video-play-container @if(!empty($data["five"]) && empty($data["five"]->video_source)) d-none @endif">
<div class="video-play-container @if(!empty($data["five"]) && empty($data["five"]->video_source)) d-none @endif">
    <section id="video_demo" class="video_container justify-content-center align-items-center hide">
        <div class="position-relative">
            <video class="video-player" @if(!empty($data["five"]) && $data["five"]->video) src="{{ generateSignedUrl($data["five"]->video) }}" @endif controls></video>
            <span class="position-absolute d-flex justify-content-center align-items-center rounded-circle card m-0" onclick="showHideVideoDemo()"> X </span>
        </div>
    </section>
</div>

 