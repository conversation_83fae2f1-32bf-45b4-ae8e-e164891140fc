<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSchoolIdColumnToNewSchoolPostRequirementTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_school_post_requirement', function (Blueprint $table) {
            $table->string('school_id')->nullable()->after('position_title');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_school_post_requirement', function (Blueprint $table) {
            $table->dropColumn(['school_id']);
        });
    }
}
