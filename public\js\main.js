document.addEventListener("DOMContentLoaded", function () {
	
	// CHART.JS START
	const chartLine = document.getElementById("chartjs-line");
	const chartBar = document.getElementById("chartjs-bar");
	const chartDoughnut = document.getElementById("chartjs-doughnut");
	const chartPie = document.getElementById("chartjs-pie");
	const chartRadar = document.getElementById("chartjs-radar");
	const chartPolar = document.getElementById("chartjs-polar-area")

	// Line chart
	if(chartLine)
	new Chart(chartLine, {
		type: "line",
		data: {
			labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
			datasets: [{
				label: "This year",
				fill: true,
				backgroundColor: "transparent",
				borderColor: window.theme.primary,
				// data: thisyearsub
			}
				, {
				label: "Last year",
				fill: true,
				backgroundColor: "transparent",
				borderColor: window.theme["primary-dark"],
				borderDash: [4, 4],
				// data: lastyearsubscriptions
			}
			]
		},
		options: {
			maintainAspectRatio: false,
			legend: {
				display: false
			},
			tooltips: {
				intersect: false
			},
			hover: {
				intersect: true
			},
			plugins: {
				filler: {
					propagate: false
				}
			},
			scales: {
				xAxes: [{
					reverse: true,
					gridLines: {
						color: "rgba(0,0,0,0.05)"
					}
				}],
				yAxes: [{
					ticks: {
						stepSize: 500
					},
					display: true,
					borderDash: [5, 5],
					gridLines: {
						color: "rgba(0,0,0,0)",
						fontColor: "#fff"
					}
				}]
			}
		}
	});

	// Bar chart
	if(chartBar)
	new Chart(chartBar, {
		type: "bar",
		data: {
			labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
			datasets: [
				{
					label: "Last year",
					backgroundColor: window.theme.primary,
					borderColor: window.theme.primary,
					hoverBackgroundColor: window.theme.primary,
					hoverBorderColor: window.theme.primary,
					// data: lastyearstudent,
					barPercentage: .75,
					categoryPercentage: .5
				},
				{
					label: "This year",
					backgroundColor: "#E8EAED",
					borderColor: "#E8EAED",
					hoverBackgroundColor: "#E8EAED",
					hoverBorderColor: "#E8EAED",
					// data: pausecontstudent,
					barPercentage: .75,
					categoryPercentage: .5
				}]
		},
		options: {
			maintainAspectRatio: false,
			legend: {
				display: false
			},
			scales: {
				yAxes: [{
					gridLines: {
						display: false
					},
					stacked: false,
					ticks: {
						stepSize: 20
					}
				}],
				xAxes: [{
					stacked: false,
					gridLines: {
						color: "transparent"
					}
				}]
			}
		}
	});

	// Doughnut chart
	if(chartDoughnut)
	new Chart(chartDoughnut, {
		type: "doughnut",
		data: {
			labels: ["Social", "Search Engines", "Direct", "Other"],
			datasets: [{
				data: [260, 125, 54, 146],
				backgroundColor: [
					window.theme.primary,
					window.theme.success,
					window.theme.warning,
					"#E8EAED"
				],
				borderColor: "transparent"
			}]
		},
		options: {
			maintainAspectRatio: false,
			cutoutPercentage: 65,
			legend: {
				display: false
			}
		}
	});

	// Pie chart
	if(chartPie)
	new Chart(chartPie, {
		type: "pie",
		data: {
			labels: ["Social", "Search Engines", "Direct", "Other"],
			datasets: [{
				data: [260, 125, 54, 146],
				backgroundColor: [
					window.theme.primary,
					window.theme.warning,
					window.theme.danger,
					"#E8EAED"
				],
				borderColor: "transparent"
			}]
		},
		options: {
			maintainAspectRatio: false,
			legend: {
				display: false
			}
		}
	});

	// Radar chart
	if(chartRadar)
	new Chart(chartRadar, {
		type: "radar",
		data: {
			labels: ["Speed", "Reliability", "Comfort", "Safety", "Efficiency"],
			datasets: [{
				label: "Model X",
				backgroundColor: "rgba(0, 123, 255, 0.2)",
				borderColor: window.theme.primary,
				pointBackgroundColor: window.theme.primary,
				pointBorderColor: "#fff",
				pointHoverBackgroundColor: "#fff",
				pointHoverBorderColor: window.theme.primary,
				data: [70, 53, 82, 60, 33]
			}, {
				label: "Model S",
				backgroundColor: "rgba(220, 53, 69, 0.2)",
				borderColor: window.theme.danger,
				pointBackgroundColor: window.theme.danger,
				pointBorderColor: "#fff",
				pointHoverBackgroundColor: "#fff",
				pointHoverBorderColor: window.theme.danger,
				data: [35, 38, 65, 85, 84]
			}]
		},
		options: {
			maintainAspectRatio: false
		}
	});

	// Polar Area chart
	if(chartPolar)
	new Chart(chartPolar, {
		type: "polarArea",
		data: {
			labels: ["Speed", "Reliability", "Comfort", "Safety", "Efficiency"],
			datasets: [{
				label: "Model S",
				data: [35, 38, 65, 70, 24],
				backgroundColor: [
					window.theme.primary,
					window.theme.success,
					window.theme.danger,
					window.theme.warning,
					window.theme.info
				]
			}]
		},
		options: {
			maintainAspectRatio: false
		}
	});
// CHART.JS END





// APEX CHARTS START

	const apexLineChart = document.querySelector("#apexcharts-line")
	// Line chart
	if(apexLineChart){
		const options = {
			chart: {
				height: 350,
				type: "line",
				zoom: {
					enabled: false
				},
			},
			dataLabels: {
				enabled: false
			},
			stroke: {
				width: [5, 7, 5],
				curve: "straight",
				dashArray: [0, 8, 5]
			},
			series: [{
				name: "Session Duration",
				data: [45, 52, 38, 24, 33, 26, 21, 20, 6, 8, 15, 10]
			},
			{
				name: "Page Views",
				data: [35, 41, 62, 42, 13, 18, 29, 37, 36, 51, 32, 35]
			},
			{
				name: "Total Visits",
				data: [87, 57, 74, 99, 75, 38, 62, 47, 82, 56, 45, 47]
			}
			],
			markers: {
				size: 0,
				style: "hollow", // full, hollow, inverted
			},
			xaxis: {
				categories: ["01 Jan", "02 Jan", "03 Jan", "04 Jan", "05 Jan", "06 Jan", "07 Jan", "08 Jan", "09 Jan", "10 Jan", "11 Jan", "12 Jan"],
			},
			tooltip: {
				y: [{
					title: {
						formatter: function (val) {
							return val + " (mins)"
						}
					}
				}, {
					title: {
						formatter: function (val) {
							return val + " per session"
						}
					}
				}, {
					title: {
						formatter: function (val) {
							return val;
						}
					}
				}]
			},
			grid: {
				borderColor: "#f1f1f1",
			}
		}
		const chart = new ApexCharts(
			apexLineChart,
			options
		);
		chart.render();
	}




	const apexAreaChart = document.querySelector("#apexcharts-area");
	// Area chart
	if(apexAreaChart){
		const options = {
			chart: {
				height: 350,
				type: "area",
			},
			dataLabels: {
				enabled: false
			},
			stroke: {
				curve: "smooth"
			},
			series: [{
				name: "series1",
				data: [31, 40, 28, 51, 42, 109, 100]
			}, {
				name: "series2",
				data: [11, 32, 45, 32, 34, 52, 41]
			}],
			xaxis: {
				type: "datetime",
				categories: ["2018-09-19T00:00:00", "2018-09-19T01:30:00", "2018-09-19T02:30:00", "2018-09-19T03:30:00", "2018-09-19T04:30:00", "2018-09-19T05:30:00",
					"2018-09-19T06:30:00"
				],
			},
			tooltip: {
				x: {
					format: "dd/MM/yy HH:mm"
				},
			}
		}
		const chart = new ApexCharts(
			apexAreaChart,
			options
		);
		chart.render();
	}





	// Bar chart
	const apexBarChart = document.querySelector("#apexcharts-bar");
	if(apexBarChart){
		const options = {
			chart: {
				height: 350,
				type: "bar",
				stacked: true,
			},
			plotOptions: {
				bar: {
					horizontal: true,
				},
			},
			stroke: {
				width: 1,
				colors: ["#fff"]
			},
			series: [{
				name: "Marine Sprite",
				data: [44, 55, 41, 37, 22, 43, 21]
			}, {
				name: "Striking Calf",
				data: [53, 32, 33, 52, 13, 43, 32]
			}, {
				name: "Tank Picture",
				data: [12, 17, 11, 9, 15, 11, 20]
			}, {
				name: "Bucket Slope",
				data: [9, 7, 5, 8, 6, 9, 4]
			}, {
				name: "Reborn Kid",
				data: [25, 12, 19, 32, 25, 24, 10]
			}],
			xaxis: {
				categories: [2008, 2009, 2010, 2011, 2012, 2013, 2014],
				labels: {
					formatter: function (val) {
						return val + "K"
					}
				}
			},
			yaxis: {
				title: {
					text: undefined
				},
			},
			tooltip: {
				y: {
					formatter: function (val) {
						return val + "K"
					}
				}
			},
			fill: {
				opacity: 1
			},
			legend: {
				position: "top",
				horizontalAlign: "left",
				offsetX: 40
			}
		}
		const chart = new ApexCharts(
			apexBarChart,
			options
		);
		chart.render();
	}





	// Column chart
	const apexColumnChart = document.querySelector("#apexcharts-column");
	if(apexColumnChart){
		const options = {
			chart: {
				height: 350,
				type: "bar",
			},
			plotOptions: {
				bar: {
					horizontal: false,
					endingShape: "rounded",
					columnWidth: "55%",
				},
			},
			dataLabels: {
				enabled: false
			},
			stroke: {
				show: true,
				width: 2,
				colors: ["transparent"]
			},
			series: [{
				name: "Net Profit",
				data: [44, 55, 57, 56, 61, 58, 63, 60, 66]
			}, {
				name: "Revenue",
				data: [76, 85, 101, 98, 87, 105, 91, 114, 94]
			}, {
				name: "Free Cash Flow",
				data: [35, 41, 36, 26, 45, 48, 52, 53, 41]
			}],
			xaxis: {
				categories: ["Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct"],
			},
			yaxis: {
				title: {
					text: "$ (thousands)"
				}
			},
			fill: {
				opacity: 1
			},
			tooltip: {
				y: {
					formatter: function (val) {
						return "$ " + val + " thousands"
					}
				}
			}
		}
		const chart = new ApexCharts(
			apexColumnChart,
			options
		);
		chart.render();
	}





	// Pie chart
	const apexPieChart = document.querySelector("#apexcharts-pie")
	if(apexPieChart){
		const options = {
			chart: {
				height: 350,
				type: "donut",
			},
			dataLabels: {
				enabled: false
			},
			series: [44, 55, 13, 33]
		}
		const chart = new ApexCharts(
			apexPieChart,
			options
		);
		chart.render();
	}





	// Heatmap chart
	function generateData(count, yrange) {
		var i = 0;
		var series = [];
		while (i < count) {
			var x = (i + 1).toString();
			var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
			series.push({
				x: x,
				y: y
			});
			i++;
		}
		return series;
	}
	const apexHeatChart = document.querySelector("#apexcharts-heatmap");
	if(apexHeatChart){
		const options = {
			chart: {
				height: 350,
				type: "heatmap",
			},
			dataLabels: {
				enabled: false
			},
			colors: ["#008FFB"],
			series: [{
				name: "Metric1",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric2",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric3",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric4",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric5",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric6",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric7",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric8",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			},
			{
				name: "Metric9",
				data: generateData(20, {
					min: 0,
					max: 90
				})
			}
			],
			xaxis: {
				type: "category",
			}
		}
		const chart = new ApexCharts(
			apexHeatChart,
			options
		);
		chart.render();
	}





	// Mixed chart
	const apexMixedChart = document.querySelector("#apexcharts-mixed");
	if(apexMixedChart){
		const options = {
			chart: {
				height: 350,
				type: "line",
				stacked: false,
			},
			stroke: {
				width: [0, 2, 5],
				curve: "smooth"
			},
			plotOptions: {
				bar: {
					columnWidth: "50%"
				}
			},
			series: [{
				name: "TEAM A",
				type: "column",
				data: [23, 11, 22, 27, 13, 22, 37, 21, 44, 22, 30]
			}, {
				name: "TEAM B",
				type: "area",
				data: [44, 55, 41, 67, 22, 43, 21, 41, 56, 27, 43]
			}, {
				name: "TEAM C",
				type: "line",
				data: [30, 25, 36, 30, 45, 35, 64, 52, 59, 36, 39]
			}],
			fill: {
				opacity: [0.85, 0.25, 1],
				gradient: {
					inverseColors: false,
					shade: "light",
					type: "vertical",
					opacityFrom: 0.85,
					opacityTo: 0.55,
					stops: [0, 100, 100, 100]
				}
			},
			labels: ["01/01/2003", "02/01/2003", "03/01/2003", "04/01/2003", "05/01/2003", "06/01/2003", "07/01/2003", "08/01/2003", "09/01/2003", "10/01/2003",
				"11/01/2003"
			],
			markers: {
				size: 0
			},
			xaxis: {
				type: "datetime"
			},
			yaxis: {
				title: {
					text: "Points",
				},
				min: 0
			},
			tooltip: {
				shared: true,
				intersect: false,
				y: {
					formatter: function (y) {
						if (typeof y !== "undefined") {
							return y.toFixed(0) + " points";
						}
						return y;
					}
				}
			}
		}
		const chart = new ApexCharts(
			apexMixedChart, {
			chart: {
				height: 350,
				type: "line",
				stacked: false,
			},
			stroke: {
				width: [0, 2, 5],
				curve: "smooth"
			},
			plotOptions: {
				bar: {
					columnWidth: "50%"
				}
			},
			series: [{
				name: "TEAM A",
				type: "column",
				data: [23, 11, 22, 27, 13, 22, 37, 21, 44, 22, 30]
			}, {
				name: "TEAM B",
				type: "area",
				data: [44, 55, 41, 67, 22, 43, 21, 41, 56, 27, 43]
			}, {
				name: "TEAM C",
				type: "line",
				data: [30, 25, 36, 30, 45, 35, 64, 52, 59, 36, 39]
			}],
			fill: {
				opacity: [0.85, 0.25, 1],
				gradient: {
					inverseColors: false,
					shade: "light",
					type: "vertical",
					opacityFrom: 0.85,
					opacityTo: 0.55,
					stops: [0, 100, 100, 100]
				}
			},
			labels: ["01/01/2003", "02/01/2003", "03/01/2003", "04/01/2003", "05/01/2003", "06/01/2003", "07/01/2003", "08/01/2003", "09/01/2003", "10/01/2003",
				"11/01/2003"
			],
			markers: {
				size: 0
			},
			xaxis: {
				type: "datetime"
			},
			yaxis: {
				title: {
					text: "Points",
				},
				min: 0
			},
			tooltip: {
				shared: true,
				intersect: false,
				y: {
					formatter: function (y) {
						if (typeof y !== "undefined") {
							return y.toFixed(0) + " points";
						}
						return y;
					}
				}
			}
		}
		);
		chart.render();
	}





	// Candlestick chart
	var seriesData = [{
		x: new Date(2016, 1, 1),
		y: [51.98, 56.29, 51.59, 53.85]
	},
	{
		x: new Date(2016, 2, 1),
		y: [53.66, 54.99, 51.35, 52.95]
	},
	{
		x: new Date(2016, 3, 1),
		y: [52.96, 53.78, 51.54, 52.48]
	},
	{
		x: new Date(2016, 4, 1),
		y: [52.54, 52.79, 47.88, 49.24]
	},
	{
		x: new Date(2016, 5, 1),
		y: [49.10, 52.86, 47.70, 52.78]
	},
	{
		x: new Date(2016, 6, 1),
		y: [52.83, 53.48, 50.32, 52.29]
	},
	{
		x: new Date(2016, 7, 1),
		y: [52.20, 54.48, 51.64, 52.58]
	},
	{
		x: new Date(2016, 8, 1),
		y: [52.76, 57.35, 52.15, 57.03]
	},
	{
		x: new Date(2016, 9, 1),
		y: [57.04, 58.15, 48.88, 56.19]
	},
	{
		x: new Date(2016, 10, 1),
		y: [56.09, 58.85, 55.48, 58.79]
	},
	{
		x: new Date(2016, 11, 1),
		y: [58.78, 59.65, 58.23, 59.05]
	},
	{
		x: new Date(2017, 0, 1),
		y: [59.37, 61.11, 59.35, 60.34]
	},
	{
		x: new Date(2017, 1, 1),
		y: [60.40, 60.52, 56.71, 56.93]
	},
	{
		x: new Date(2017, 2, 1),
		y: [57.02, 59.71, 56.04, 56.82]
	},
	{
		x: new Date(2017, 3, 1),
		y: [56.97, 59.62, 54.77, 59.30]
	},
	{
		x: new Date(2017, 4, 1),
		y: [59.11, 62.29, 59.10, 59.85]
	},
	{
		x: new Date(2017, 5, 1),
		y: [59.97, 60.11, 55.66, 58.42]
	},
	{
		x: new Date(2017, 6,),
		y: [58.34, 60.93, 56.75, 57.42]
	},
	{
		x: new Date(2017, 7, 1),
		y: [57.76, 58.08, 51.18, 54.71]
	},
	{
		x: new Date(2017, 8, 1),
		y: [54.80, 61.42, 53.18, 57.35]
	},
	{
		x: new Date(2017, 9, 1),
		y: [57.56, 63.09, 57.00, 62.99]
	},
	{
		x: new Date(2017, 10, 1),
		y: [62.89, 63.42, 59.72, 61.76]
	},
	{
		x: new Date(2017, 11, 1),
		y: [61.71, 64.15, 61.29, 63.04]
	}
	];
	const apexCandlesChart = document.querySelector("#apexcharts-candlestick");
	if(apexCandlesChart){
		const options = {
			chart: {
				height: 350,
				type: "candlestick",
			},
			series: [{
				data: seriesData
			}],
			stroke: {
				width: 1
			},
			xaxis: {
				type: "datetime"
			}
		};
		const chart = new ApexCharts(
			document.querySelector("#apexcharts-candlestick"),
			options
		);
		chart.render();
	}
});
// APEX CHARTS END





// PROFILE IMAGE UPLOAD START
function readURL(input) {
	if (input.files && input.files[0]) {
		var reader = new FileReader();
		reader.onload = function (e) {
			$("#imagePreview").css("background-image", "url(" + e.target.result + ")");
			$("#imagePreview").hide();
			$("#imagePreview").fadeIn(650);
		};
		reader.readAsDataURL(input.files[0]);
	}
}
$("#imageUpload").change(function () {
	readURL(this);
});
// PROFILE IMAGE UPLOAD END





// ADD SKILLS START
$("#addSkillsBtn").click(function () {
	let $txt = $("#addSkillsTxt");
	let skills = $txt.val();
	$txt.val('');
	$(`<div class="chip" tabindex="-1">
        <span>
          	${skills}
        </span>
        <span class="" title="Remove chip" aria-label="Remove chip" type="button" onclick="$(this).parent().remove()">
          	<i class="fas fa-fw fa-times-circle align-middle text-danger ml-1"></i>
        </span>
  	</div>`).appendTo("#skillsChipsContainer");;
});
// ADD SKILLS END





// DATE PICKER START
document.addEventListener("DOMContentLoaded", function () {
	$('#dateOfBirth, #expJoiningDate_1, #expLeavingDate_1, #eduJoiningDate_1, #eduLeavingDate_1, #addNewCourseStartDate, #editCourseStartDate').datetimepicker({
		format: 'L'
	});
});
// DATE PICKER END





// ADD & REMOVE ELEMENTS START
$('.experience_card').each(function () {
	var $wrapper = $('.experience_body', this);
	$(".addMoreExpBtn", $(this)).click(function (e) {
		$('.experience_row:first-child', $wrapper).clone(true).prependTo($wrapper)
	});

	$('.experience_row .removeMoreExpBtn', $wrapper).click(function () {
		if ($('.experience_row', $wrapper).length > 1)
			$(this).parents('.experience_row').remove();
	});

});

$('.education_card1').each(function () {
	var $wrapper = $('.education_body', this);
	$(".addMoreEduBtn", $(this)).click(function (e) {
		$('.education_row:first-child', $wrapper).clone(true).prependTo($wrapper)
	});
	$('.education_row .removeMoreEduBtn', $wrapper).click(function () {
		if ($('.education_row', $wrapper).length > 1)
			$(this).parents('.education_row').remove();
	});
});
// ADD & REMOVE ELEMENTS END





// IMAGE GALLERY START
$(document).ready(function () {
	$(".gallery").magnificPopup({
		delegate: "a",
		type: "image",
		tLoading: "Loading image #%curr%...",
		mainClass: "mfp-img-mobile",
		gallery: {
			enabled: true,
			navigateByImgClick: true,
			preload: [0, 1] // Will preload 0 - before current, and 1 after the current image
		},
		image: {
			tError: '<a href="%url%">The image #%curr%</a> could not be loaded.'
		}
	});
});
// IMAGE GALLERY END



// MONTHLY INVOICES TABLE START
document.addEventListener("DOMContentLoaded", function () {
	// Setup - add a text input to each footer cell
	$('#datatables-column-search-text-inputs tfoot th').each(function () {
		var title = $(this).text();
		
		$(this).html('<input type="text" class="form-control" placeholder="Search ' + title + '" />');
	});
	// DataTables
	var table = $('#datatables-column-search-text-inputs').DataTable();
	// Apply the search
	table.columns().every(function () {
		var that = this;
		$('input', this.footer()).on('keyup change clear', function () {
			if (that.search() !== this.value) {
				that
					.search(this.value)
					.draw();
			}
		});
	});


	// Apply the search
	table.columns().every(function () {
		var that = this;
		$('input', this.footer()).on('keyup change clear', function () {
			if (that.search() !== this.value) {
				that
					.search(this.value)
					.draw();
			}
		});
	});

	
});
// MONTHLY INVOICES TABLE END




// STUDENT PROFILE TABLE START
document.addEventListener("DOMContentLoaded", function () {
	// Datatables Responsive
	$("#datatables-reponsive").DataTable({
		responsive: true
	});
});
// STUDENT PROFILE TABLE END