<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnToNewSchoolPostRequirementTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_school_post_requirement', function (Blueprint $table) {
            $table->string('status')->nullable()->after('benefits');
            $table->softDeletes()->after('status');;
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_school_post_requirement', function (Blueprint $table) {
            $table->dropColumn(['status']);
        });
    }
}
