<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolManagementSettingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_management_setting', function (Blueprint $table) {
            $table->id();
            $table->string('type')->nullable();
            $table->longText('value')->nullable();
            $table->timestamps();
        });

        DB::table('school_management_setting')->insert([
            ['type' => 'requirement_for', 'value' => null],
            ['type' => 'position_type', 'value' => null],
            ['type' => 'certificate', 'value' => null],
            ['type' => 'language', 'value' => null],
            ['type' => 'per_hour_range', 'value' => null],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_management_setting');
    }
}
