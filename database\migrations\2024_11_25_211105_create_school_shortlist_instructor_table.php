<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolShortlistInstructorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_shortlist_instructor', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('requirement_id')->nullable();
            $table->bigInteger('school_id')->nullable();
            $table->bigInteger('user_id')->nullable();
            $table->integer('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_shortlist_instructor');
    }
}
