$(document).ready(function(){
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.add_button_online_slots'); //Add button selector
    var wrapper = $('.appendrange'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if($('#onlinesloatid').val()){
        var qqq=$('#onlinesloatid').val();
    }else{
        var qqq=1;
    }
    
    // Once add button is clicked
    $('body').on('click','.add_button_online_slots',function(){
   
     var id=$(this).attr('data-id');
        //Check maximum number of input fields
        if(x < maxField){ 
            x++; //Increase field counter
            qqq++;
            console.log($(this).attr('data-day'))
            if($(this).attr('data-day')==undefined ){
                var day='<option value="">Select Days</option>';
            }else{
                var day= $(this).attr('data-day');
                
            }

            
            
         
             var fieldHTML = '<div class="login__form removerange'+qqq+' calendar col-md-6 col-xl-6 form-title"><div class="login__form select" style="position:relative"><h4 class="form-heading"></h4><select aria-label="My time zone" class="date-ui form-select entervalue" id="day" name="day[online]['+id+'][]">'+day+'</select></div></div><div class="removerange'+qqq+' col-md-6 col-xl-6 m-top-avail"><h4 class="form-heading"></h4><div class="align-items-center d-flex timing"><div class="login__form time-slot"><div class="cs-form"><input class="form-control time-zone entervalue" name="from_time[online]['+id+'][]" type="time" value="10:05 AM"></div></div><div class="time-select-to"><p>To</div><div class="login__form time-slot position" style="margin-top:0"><div class="cs-form"><input class="form-control time-zone entervalue" name="to_time[online]['+id+'][]" type="time" value="10:05 AM"></div></div><div class="login__form time-slot position removerange'+qqq+'" style="margin-left:10px"><div class="cs-form addremovemargin"><a class="removerange" data-id="'+qqq+'" href="javascript:void(0);"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"/><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"/></svg></a></div></div></div></div>'; //New input field html 
            $('.appendrange'+id).append(fieldHTML); //Add field html
        }else{
            alert('A maximum of '+maxField+' fields are allowed to be added. ');
        }
    });
 
    // Once remove button is clicked
    $('body').on('click', '.removerange', function(e){
        e.preventDefault();
        var id=$(this).attr('data-id');
        
       $('.removerange'+id).remove(); //Remove field html
       qqq--;
        x--; //Decrease field counter
    });
});


$(document).ready(function(){
    var maxField = 10; //Input fields increment limitation
    var addButton = $('.addonlinerange'); //Add button selector
    var wrapper = $('.onlinerangeappend'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if($('#onlinedatarange').val()!=""){
        var rang=$('#onlinedatarange').val();
    }else{
        var rang=1;
    }

   
    // Once add button is clicked
    $(addButton).click(function(){
     
        //Check maximum number of input fields
        if(x < maxField){ 
            x++; //Increase field counter
            rang++;
            //var timezone=$('#teach_online_timezone').html();
           
             var fieldHTML = '<div class="select-cl-align col-xl-12 col-md-12"><div class="login__form form-title calendar reonline'+rang+'" style="margin-top:0"><div class="login__form form-title" style="margin-top:0"><div class="login__form" style="margin-top:0"><label for="date"></label><input class="date-ui date-ui entervalue from_date" data-date-id="'+rang+'" data-date-type="online" name="from_date[online][]" type="date" id="from_date"></div></div></div><div class="time-select-to reonline'+rang+'"><p>To</p></div><div class="login__form form-title calendar reonline'+rang+'" style="margin-top:0"><div class="login__form form-title" style="margin-top:0"><div class="login__form" style="margin-top:0"><label for="date"></label><input class="date-ui entervalue to_date" data-date-id="'+rang+'" data-date-type="online" name="to_date[online][]" type="date" id="to_date"></div></div></div></div><div class="reonline'+rang+' appendrange'+rang+' row" style="margin-top:0"><div class="login__form form-title calendar col-xl-6 reonline'+rang+' col-md-6"><div class="login__form select" style="position:relative;margin-top:0"><h4 class="form-heading"></h4><select aria-label="My time zone" class="date-ui form-select entervalue" id="day" name="day[online]['+rang+'][]"><option value="">Select<option value="1">Monday<option value="2">Tuesday<option value="3">Wednesday<option value="4">Thursday<option value="5">Friday<option value="6">Saturday</select></div></div><div class="reonline'+rang+' col-xl-6 col-md-6 m-top-avail" data-id="'+rang+'"><h4 class="form-heading"></h4><div class="d-flex align-items-center timing"><div class="login__form time-slot"><div class="cs-form"><input class="form-control time-zone entervalue" name="from_time[online]['+rang+'][]" type="time" value="10:05 AM"></div></div><div class="time-select-to"><p>To</div><div class="login__form time-slot position" style="margin-top:0"><div class="cs-form"><input class="form-control time-zone entervalue" name="to_time[online]['+rang+'][]" type="time" value="10:05 AM"></div></div><div class="login__form time-slot position" style="margin-left:10px"><div class="cs-form addremovemargin"><a class="add_button_online_slots" data-id="'+rang+'" href="javascript:void(0);" title="Add field"><svg fill="none" height="31" viewBox="0 0 31 31" width="31" xmlns="http://www.w3.org/2000/svg" class="plusclass"><g clip-path="url(#clip0_1511_1023)"><path d="M15.5 0C6.944 0 0 6.944 0 15.5C0 24.056 6.944 31 15.5 31C24.056 31 31 24.056 31 15.5C31 6.944 24.056 0 15.5 0ZM21.7 17.05H17.05V21.7C17.05 22.5525 16.3525 23.25 15.5 23.25C14.6475 23.25 13.95 22.5525 13.95 21.7V17.05H9.3C8.4475 17.05 7.75 16.3525 7.75 15.5C7.75 14.6475 8.4475 13.95 9.3 13.95H13.95V9.3C13.95 8.4475 14.6475 7.75 15.5 7.75C16.3525 7.75 17.05 8.4475 17.05 9.3V13.95H21.7C22.5525 13.95 23.25 14.6475 23.25 15.5C23.25 16.3525 22.5525 17.05 21.7 17.05Z" fill="#004CBD" clip-rule="evenodd" fill-rule="evenodd"/></g><defs><clipPath id="clip0_1511_1023"><rect fill="white" height="31" width="31"/></clipPath></defs></svg></a></div></div></div></div></div><div class="reonline'+rang+' add-svg d-flex"><svg class="removeonline" data-id="'+rang+'" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"/><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"/></svg></div>'; //New input field html 
            $(wrapper).append(fieldHTML); //Add field html
            var cd = (new Date()).toISOString().split('T')[0];

            $(".from_date").each(function(){
             $(this).attr('min',cd);
            })
        }else{
            alert('A maximum of '+maxField+' fields are allowed to be added. ');
        }
    });
    
    // Once remove button is clicked
    $('body').on('click', '.removeonline', function(e){
        e.preventDefault();
        var id=$(this).attr('data-id');
      
        
       $('.reonline'+id).remove(); //Remove field html
        x--; //Decrease field counter
        rang--;
    });
});

$(document).ready(function(){
    var maxField = 10; //Input fields increment limitation
    var addButton = $('.adddate'); //Add button selector
   

    var x = 1; //Initial field counter is 1
    if($('#dateid').val()!=""){
        var ranga=$('#dateid').val();
     }else{
        var ranga=1;
    }
    $('body').on('click','.adddate',function(){

     var id=$(this).attr('data-id');
     var ids=$(this).attr('data-id');
     var step=$(this).attr('data-step');
     var arr = step.split('-');
  
        //Check maximum number of input fields
        if(x < maxField){ 
            x++; //Increase field counter
            ranga++;
            var rg=parseInt($(this).attr('data-range'))+1;
            $(this).attr('data-range',rg);
            id++;
            var r=parseInt(arr[1])+1;
            var dateid=ranga;
             var fieldHTML = '<div class="row appenddate'+ids+'-'+dateid+' appenddaterow'+ids+' sameas date'+dateid+' removelocation'+ids+'"><hr class="section-bootom-line date'+ranga+'"><div class="select-cl-align col-xl-12 col-md-12"><div class="login__form form-title calendar reinperson1 date'+ranga+'" style="margin-top:0"><div class="login__form form-title" style="margin-top:0"><div class="login__form" style="margin-top:0"><label for="date"></label><input type="date" data-date-id="1" data-date-type="inperson" class="date-ui inpersonpluse from_date" id="from_date" name="from_date[inperson]['+ids+']['+rg+'][]"></div></div></div><div class="time-select-to date'+ranga+'"><p>To</p></div><div class="login__form form-title calendar reinperson1 date'+ranga+'" style="margin-top:0"><div class="login__form form-title" style="margin-top:0"><div class="login__form" style="margin-top:0"><label for="date"></label><input type="date" data-date-id="1" data-date-type="inperson" class="date-ui inpersonpluse to_date" id="to_date" name="to_date[inperson]['+ids+']['+rg+'][]"></div></div></div><div class="login__form position time-slot date'+ranga+'"><div class="cs-form addremovemargin"><label for="date"></label><a class="removedate" data-id="'+ranga+'" data-step="'+ids+'-'+id+'" href="javascript:void(0);"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></a></div></div></div><div class="row appendrangeinperson'+ids+'-'+ranga+' slotrange sameas date'+ranga+'" style="margin-top:0"><div class="col-xl-6 col-md-6 login__form form-title calendar" style=""><div class="login__form select" style="position:relative;margin-top:0"><h4 class="form-heading">Select Days</h4><select class="form-select date-ui" aria-label="My time zone" name="day[inperson]['+ids+']['+rg+'][]" id="day"><option value="">Select Days</option><option value="1">Monday</option><option value="2">Tuesday</option><option value="3">Wednesday</option><option value="4">Thursday</option><option value="5">Friday</option><option value="6">Saturday</option></select></div></div><div class="col-xl-6 col-md-6 m-top-avail date'+ranga+'"><h4 class="form-heading">Select time slots</h4><div class="timing d-flex align-items-center"><div class="login__form time-slot"><div class="cs-form"><input type="time" class="form-control common__login__input time-zones" value="10:05 AM" name="from_time[inperson]['+ids+']['+rg+'][]"></div></div><div class="time-select-to"><p>To</p></div><div class="login__form position time-slot" style="margin-top:0"><div class="cs-form"><input type="time" class="form-control common__login__input time-zones" value="10:05 AM" name="to_time[inperson]['+ids+']['+rg+'][]"></div></div><div class="login__form position time-slot" style="margin-left:10px"><div class="cs-form addremovemargin"><a href="javascript:void(0);" class="add_button_inperson_slots" data-id="'+ranga+'" data-step="'+ids+'-'+ranga+'" title="Add field"><svg class="plusclass" width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1511_1023)"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 0C6.944 0 0 6.944 0 15.5C0 24.056 6.944 31 15.5 31C24.056 31 31 24.056 31 15.5C31 6.944 24.056 0 15.5 0ZM21.7 17.05H17.05V21.7C17.05 22.5525 16.3525 23.25 15.5 23.25C14.6475 23.25 13.95 22.5525 13.95 21.7V17.05H9.3C8.4475 17.05 7.75 16.3525 7.75 15.5C7.75 14.6475 8.4475 13.95 9.3 13.95H13.95V9.3C13.95 8.4475 14.6475 7.75 15.5 7.75C16.3525 7.75 17.05 8.4475 17.05 9.3V13.95H21.7C22.5525 13.95 23.25 14.6475 23.25 15.5C23.25 16.3525 22.5525 17.05 21.7 17.05Z" fill="#004CBD"/></g><defs><clipPath id="clip0_1511_1023"><rect width="31" height="31" fill="white"/></clipPath></defs></svg></a></div></div></div></div></div></div>'; //New input field html 
            $('.appenddaterow'+ids+':last').after(fieldHTML); //Add field html
            var cd = (new Date()).toISOString().split('T')[0];

            $(".from_date").each(function(){
             $(this).attr('min',cd);
            })
        }else{
            alert('A maximum of '+maxField+' fields are allowed to be added. ');
        }
    });
    
    // Once remove button is clicked
    $('body').on('click', '.removedate', function(e){
        e.preventDefault();
        var step=$(this).attr('data-id');
        
       $('.date'+step).remove(); //Remove field html
        x--; //Decrease field counter
        ranga--;
    });
});


$(document).ready(function(){
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.add_button_inperson_slots'); //Add button selector
    var wrapper = $('.appendrangeinperson'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    if($('#inpersonslot').val()){
        var qq=$('#inpersonslot').val();
    }else{
        var qq=1;
    }
    var sloat = 0;
    // Once add button is clicked
    $('body').on('click','.add_button_inperson_slots',function(){
 
     var id=$(this).attr('data-id');
     var step=$(this).attr('data-step');
     var arr = step.split('-');
        //Check maximum number of input fields
        if(x < maxField){ 
            x++; //Increase field counter
            qq++;
            sloat++;
            // var timezone=$('.tzone').html();
            // var days=$(this).attr('data-day');
            if($(this).attr('data-day')==undefined){
                var days='<option value="">Select Days</option>';
            }else if($(this).attr('data-day')!=""){
                var days=$(this).attr('data-day');
            }else{
                var days='<option value="">Select Days</option>';
            }
             var fieldHTML = '<div class="login__form removeinpersonsloat'+qq+' calendar col-md-6 col-xl-6 form-title"><div class="login__form select" style="position:relative"><h4 class="form-heading"></h4><select aria-label="My time zone" class="date-ui form-select entervalue" id="day" name="day[inperson]['+arr[0]+']['+arr[1]+'][]">'+days+'</select></div></div><div class="removeinpersonsloat'+qq+' col-xl-6 col-md-6 m-top-avail"><h4 class="form-heading"></h4><div class="align-items-center d-flex timing"><div class="login__form time-slot"><div class="cs-form"><input class="form-control time-zones entervalue common__login__input" name="from_time[inperson]['+arr[0]+']['+arr[1]+'][]" type="time" value="10:05 AM"></div></div><div class="time-select-to"><p>To</div><div class="login__form time-slot position" style="margin-top:0"><div class="cs-form"><input class="form-control time-zones common__login__input entervalue" name="to_time[inperson]['+arr[0]+']['+arr[1]+'][]" type="time" value="10:05 AM"></div></div><div class="login__form time-slot position removerange'+qq+'" style="margin-left:10px"><div class="cs-form addremovemargin"><a class="removeinpersonrange" data-id="'+qq+'" href="javascript:void(0);"><svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"/><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"/></svg></a></div></div></div></div>'; //New input field html 
            $('.appendrangeinperson'+step).append(fieldHTML); //Add field html
          
        }else{
            alert('A maximum of '+maxField+' fields are allowed to be added. ');
        }
    });
 
    // Once remove button is clicked
    $('body').on('click', '.removeinpersonrange', function(e){
        e.preventDefault();
        var id=$(this).attr('data-id');
        
       $('.removeinpersonsloat'+id).remove(); //Remove field html
        x--; //Decrease field counter
        qq--;
        sloat--;
    });
});


$(document).ready(function(){
    var maxField = 200; //Input fields increment limitation
    var addButton = $('.addinpersonrange'); //Add button selector
    var wrapper = $('.appendrangeinperson'); //Input field wrapper

    var x = 1; //Initial field counter is 1
    var so=0;

    // if($('#inpersondatarange').val() ){
    //     // var rangg=$('#inpersondatarange').val();
    //  }else{
        var rangg=1;
    //}
    
    var time=1;
    // Once add button is clicked
    $(addButton).click(function(){
 
        //Check maximum number of input fields
        if(x < maxField){ 
            x++; //Increase field counter
            rangg++;
            so++;
            time++;
            
             var fieldHTML = '<hr class="section-bootom-line removelocation'+rangg+'"><div class="col-xl-6 col-md-6 login__form form-title calendar removelocation'+rangg+'"><div class="login__form select" style="position:relative;margin-top:0"><h4 class="form-heading">Location</h4><div class="Location"><input class="common__login__input search from_teach_in_person_location entervalue" data-id="'+rangg+'" type="text" placeholder="Select Location" name="from_teach_in_person_location[]" id="from_teach_in_person_location'+rangg+'"><div id="map" class="map"></div><span class="svg-item"><svg width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.125 7C13.125 12.3333 7.125 17 7.125 17C7.125 17 1.125 12.3333 1.125 7C1.125 3.66667 3.79167 1 7.125 1C10.4583 1 13.125 3.66667 13.125 7Z" stroke="#A7A9B7" stroke-width="1.5" stroke-miterlimit="10" stroke-linejoin="round"></path><circle cx="7.05103" cy="6.92506" r="2.37037" fill="#A7A9B7"></circle></svg></span></div></div></div><div class="col-xl-6 col-md-6 login__form form-title calendar removelocation'+rangg+'"><div class="login__form select" style="position:relative;margin-top:0"><h4 class="form-heading">Radius(Miles)</h4><div class="Location"><input class="common__login__input search to_teach_in_person_location entervalue" data-id="0" min="0" type="number" placeholder="Radius(Miles)" name="to_teach_in_person_location[]" id="to_teach_in_person_location0"></div></div></div><h4 class="form-heading removelocation'+rangg+'" style="margin-bottom:0">Select Date rangge</h4><div class="row appenddate'+rangg+'-'+so+' appenddaterow'+rangg+' removelocation'+rangg+' sameas date'+rangg+'"><div class="select-cl-align col-xl-12 col-md-12"><div class="login__form form-title calendar reinperson'+rangg+'" style="margin-top:0"><div class="login__form form-title" style="margin-top:0"><div class="login__form" style="margin-top:0"><label for="date"></label><input type="date" data-date-id="'+rangg+'" data-date-type="inperson" class="date-ui inpersonpluse from_date" id="from_date" name="from_date[inperson]['+rangg+'][1][]"></div></div></div><div class="time-select-to"><p>To</p></div><div class="login__form form-title calendar reinperson'+rangg+'" style="margin-top:0"><div class="login__form form-title" style="margin-top:0"><div class="login__form" style="margin-top:0"><label for="date"></label><input type="date" data-date-id="1" data-date-type="inperson" class="date-ui inpersonpluse to_date" id="to_date" name="to_date[inperson]['+rangg+'][1][]"></div></div></div><div class="col-md-2 login__form position time-slot sameas"><div class="cs-form addremovemargin"><label for="date"></label><a href="javascript:void(0);" class="adddate" data-range="1" data-id="'+rangg+'" data-step="'+rangg+'-'+so+'" title="Add field"><svg class="plusclass" width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1511_1023)"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 0C6.944 0 0 6.944 0 15.5C0 24.056 6.944 31 15.5 31C24.056 31 31 24.056 31 15.5C31 6.944 24.056 0 15.5 0ZM21.7 17.05H17.05V21.7C17.05 22.5525 16.3525 23.25 15.5 23.25C14.6475 23.25 13.95 22.5525 13.95 21.7V17.05H9.3C8.4475 17.05 7.75 16.3525 7.75 15.5C7.75 14.6475 8.4475 13.95 9.3 13.95H13.95V9.3C13.95 8.4475 14.6475 7.75 15.5 7.75C16.3525 7.75 17.05 8.4475 17.05 9.3V13.95H21.7C22.5525 13.95 23.25 14.6475 23.25 15.5C23.25 16.3525 22.5525 17.05 21.7 17.05Z" fill="#004CBD"/></g><defs><clipPath id="clip0_1511_1023"><rect width="31" height="31" fill="white"/></clipPath></defs></svg></a></div></div></div><div class="row appendrangeinperson'+rangg+'-'+so+' slotrangge sameas removelocation'+rangg+'" style="margin-top:0"><div class="col-xl-6 col-md-6 login__form form-title calendar" style=""><div class="login__form select" style="position:relative;margin-top:0"><h4 class="form-heading">Select Days</h4><select class="form-select date-ui" aria-label="My time zone" name="day[inperson]['+rangg+'][1][]" id="day"><option value="">Select Days</option><option value="1">Monday</option><option value="2">Tuesday</option><option value="3">Wednesday</option><option value="4">Thursday</option><option value="5">Friday</option><option value="6">Saturday</option></select></div></div><div class="col-xl-6 col-md-6 m-top-avail"><h4 class="form-heading">Select time slots</h4><div class="timing d-flex align-items-center"><div class="login__form time-slot"><div class="cs-form"><input type="time" class="form-control common__login__input time-zones" value="10:05 AM" name="from_time[inperson]['+rangg+'][1][]"></div></div><div class="time-select-to"><p>To</p></div><div class="login__form position time-slot" style="margin-top:0"><div class="cs-form"><input type="time" class="form-control common__login__input time-zones" value="10:05 AM" name="to_time[inperson]['+rangg+'][1][]"></div></div><div class="login__form position time-slot" style="margin-left:10px"><div class="cs-form addremovemargin"><a href="javascript:void(0);" class="add_button_inperson_slots" data-id="'+rangg+'" data-step="'+rangg+'-1" title="Add field"><svg class="plusclass" width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1511_1023)"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 0C6.944 0 0 6.944 0 15.5C0 24.056 6.944 31 15.5 31C24.056 31 31 24.056 31 15.5C31 6.944 24.056 0 15.5 0ZM21.7 17.05H17.05V21.7C17.05 22.5525 16.3525 23.25 15.5 23.25C14.6475 23.25 13.95 22.5525 13.95 21.7V17.05H9.3C8.4475 17.05 7.75 16.3525 7.75 15.5C7.75 14.6475 8.4475 13.95 9.3 13.95H13.95V9.3C13.95 8.4475 14.6475 7.75 15.5 7.75C16.3525 7.75 17.05 8.4475 17.05 9.3V13.95H21.7C22.5525 13.95 23.25 14.6475 23.25 15.5C23.25 16.3525 22.5525 17.05 21.7 17.05Z" fill="#004CBD"/></g><defs><clipPath id="clip0_1511_1023"><rect width="31" height="31" fill="white"/></clipPath></defs></svg></a></div></div></div></div></div></div><div class="add-svg d-flex removelocation'+rangg+'"><svg class="removeloclat" data-id="2" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle><rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white"></rect></svg></div>'; //New input field html 
            $(wrapper).append(fieldHTML); //Add field html
            var cd = (new Date()).toISOString().split('T')[0];

            $(".from_date").each(function(){
             $(this).attr('min',cd);
            })
        }else{
            alert('A maximum of '+maxField+' fields are allowed to be added. ');
        }
    });
    
    // Once remove button is clicked
    $('body').on('click', '.removeloclat', function(e){
        console.log('yes')
        e.preventDefault();

        var id=$(this).attr('data-id');
        rangg--;
       $('.removelocation'+id).remove(); //Remove field html
       console.log('range'+rangg)
        x--; //Decrease field counter
        so--;
        time--;
    });
});


$('body').on('change','.from_date',function(){
   
    var fromdate=$(this).val();
    var id=$(this).attr('data-date-id');
    var type=$(this).attr('data-date-type');
    if(type=='online'){
    $(this).parents('.select-cl-align').find('.reonline'+id).find('.to_date').val('');
   //  console.log($(this).parents('#avid').find('.reonline'+id).find('.to_date').attr('class'))
    $(this).parents('.select-cl-align').find('.reonline'+id).find('.to_date').attr('min',fromdate);
   // alert($(this).parent('.onlinerangeappend').find('.reonline'+id).find('.to_date').val());
    }
    if(type=='inperson'){
   
       $(this).parent().parent().parent().next('div').next("div").eq(0).find('input').val('');
      //  console.log($(this).parents('#avid').find('.reonline'+id).find('.to_date').attr('class'))
       $(this).parent().parent().parent().next('div').next("div").eq(0).find('input').attr('min',fromdate);
      // alert($(this).parent('.onlinerangeappend').find('.reonline'+id).find('.to_date').val());
       }

    
})

$('body').on('click','#submitAvailability',function(){
    var loading="<i class='fa fa-spinner fa-spin'></i>Saving..";
    var teach_minimum = document.getElementById('teach_minimum').value;
    var teach_maximum = document.getElementById('teach_maximum').value;
    var teach_in_person_timezone = document.getElementById('teach_in_person_timezone').value;
    if (teach_minimum.length == ""){
      $("#teach_minimum").css("border-color", "red");
    }else{
      $("#teach_minimum").css("border-color", "");
    }

    if (teach_maximum.length == 0){
    $("#teach_maximum").addClass('errorc');
     
    }else{
        $("#teach_maximum").removeClass('errorc');
      $("#teach_maximum").css("border-color", "");
    }

    if (teach_in_person_timezone.length == 0){
        $("#teach_in_person_timezone").addClass('errorc');
         
        }else{
            $("#teach_in_person_timezone").removeClass('errorc');
          $("#teach_in_person_timezone").css("border-color", "");
        }
    if ($(".onlinerangeappend")[0]){
    var onlinerange=true;
    $('.onlinerangeappend , .select-cl-align').find('input,select').each(function() {
 
        if(this.hasAttribute("name")&& $(this).is(":visible")){

           
        if($(this).val()=="" || $(this).length==""){
            onlinerange = false;
            $(this).css("border-color", "red");
            return false;
        }else{
            $(this).css("border-color", "");
            onlinerange = true;
        }
    
       }
      }); 

       
    }
    var typeav=$('#avtype').val();
    if(typeav=="in_person"){
        var isValid = true;
        
        $('.appendrangeinperson').find('input,select').each(function() {
           
            if(this.hasAttribute("name")&& $(this).is(":visible")){
          
            if($(this).val() == "" && $(this).val().length < 1) {
                $(this).css("border-color", "red");
                isValid = false;
            } else {
                $(this).css("border-color", "");
            }
            }
          });

          var onlinerange2=true;
          $('.slotrange').find('input,select').each(function() {
         
            if(this.hasAttribute("name")&& $(this).is(":visible")){
            if($(this).val()=="") {
                
                onlinerange2 = false;
                $(this).css("border-color", "red");
            }else{
                $(this).css("border-color", "");
                onlinerange2 = true;
            }
           }
          });
    }else if(typeav=="both"){
        if ($("#forgot")[0]){
            if ($('#forgot').not(':checked').length) {
                var isValid = true;
              if ($(".appendrangeinperson")[0]){
              $('.appendrangeinperson').find('input,select').each(function() {
                
                if(this.hasAttribute("name")&& $(this).is(":visible")){
              
                
                if($(this).val() == "" && $(this).val().length < 1) {
                    $(this).css("border-color", "red");
                    isValid = false;
                } else {
                    $(this).css("border-color", "");
                }
                }
              });
            }
          
              var onlinerange2=false;
              if($(".slotrange")[0]){
              $('.slotrange').find('input,select').each(function() {
               
                if(this.hasAttribute("name")&& $(this).is(":visible")){

                if($(this).val()=="") {
                    console.log(1);
                    onlinerange2 = false;
                    $(this).css("border-color", "red");
                }else{
                    onlinerange2 = true;
                    $(this).css("border-color", "");
                    console.log(2);
                   
                }

               }
              });
            }
            }else{
        
        
                var isValid=true;
                var onlinerange2=true;
        
        
            }
        }
    }else{
        var isValid=true;
        var onlinerange2=true;
    }


   
  
      if($(".aprange")[0]){
     
        var onlinerange1=true;
      $('.aprange').find('input,select').each(function() {
        if(this.hasAttribute("name") && $(this).is(":visible")){
        if($(this).val()==""){
          
            onlinerange1 = false;
            $(this).css("border-color", "red");
        }else{
            $(this).css("border-color", "");
            onlinerange1 = true;
        }
        }
      });
    }

   

      if(parseInt(teach_maximum) <= parseInt(teach_minimum)){
        $("#teach_maximum").focus();
        $('#teach_maximum').css("border-color", "red");
        $('#teach_maximum_error').html('Maximum number should be greater than from minimun number')
        return false;
      }else{
       
        $('#teach_maximum').css("border-color", "");
        $('#teach_maximum_error').html('');
       
      }
  
  
      if($("#forgot")[0]){
        console.log(1)
      if ($("#forgot").is(":checked") == true) {
      var teach_in_person_location = document.getElementById('from_teach_in_person_locationa').value;
      var to_teach_in_person_location = document.getElementById('to_teach_in_person_locationaa').value;
         
      if (teach_in_person_location.length == ""){
        $("#from_teach_in_person_locationa").css("border-color", "red");
       
      }else{
        $("#from_teach_in_person_locationa").css("border-color", "");
      }
      if (to_teach_in_person_location.length == ""){
        $("#to_teach_in_person_locationaa").css("border-color", "red");
       
      }else{
        $("#to_teach_in_person_locationaa").css("border-color", "");
      }
        
      if (teach_in_person_location.length == ""){
        $("#from_teach_in_person_locationa").css("border-color", "red");
        return false;
      }else{
        $("#from_teach_in_person_locationa").css("border-color", "");
      }
      if (to_teach_in_person_location.length == ""){
        $("#to_teach_in_person_locationaa").css("border-color", "red");
        return false;
      }else{
        $("#to_teach_in_person_locationaa").css("border-color", "");
      }
    }
   }
   if (teach_in_person_timezone.length == 0){
    $("#teach_in_person_timezone").addClass('errorc');
    return false;
    }else{
        $("#teach_in_person_timezone").removeClass('errorc');
      $("#teach_in_person_timezone").css("border-color", "");
    }
   var typeav=$('#avtype').val();
   if(typeav=="in_person"){
      var validate=onlinerange2==true && isValid==true
   }else if(typeav=='online'){
    var validate=onlinerange==true && onlinerange1==true
   }else{


    var validate=onlinerange==true && onlinerange1==true && onlinerange2==true && isValid==true
   }
   console.log(validate);
if(validate){
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    var  _this=$(this);
    var url = APP_URL + '/submitAvailability';
    $.ajax({
        type: 'POST',
        url: url,
        data: $('#avid').serialize(),
        dataType: "json",
        beforeSend: function () {
            // $('#submitAvailability').prop("disabled", true);
            // _this.innerHTML=loading;
        },
        success: function (data) {
           
            if (data.success == true) {
                alertify.success(data.message);
                // _this.innerHTML='Submit';
                // _this.prop("disabled", false);

            } else {
                _this.innerHTML='Submit';
                // _this.prop("disabled", false);
                //  alertify.error(data.message);
            }
        },
    }).done(function () {
        // _this.innerHTML='Submit';
        // _this.prop("disabled", false);
    });
 }
})

$('body').on('change','.to_date',function(){

    var fromdate=$(this).val();
    var id=$(this).attr('data-date-id');
    var type=$(this).attr('data-date-type');
    var toDate =   new Date($(this).val());  
    
    if(type=='online'){
 
    var fromDates = new Date($(this).parents('.select-cl-align').find('.reonline'+id).find('.from_date').val());

    var html='<option value="">Select Days</option>';
    // Loop through the days between the two dates
     for (var date = fromDates; date <= toDate; date.setDate(date.getDate()+1 )) {
        var dayOfWeek = (date.getDay()-1);
        var dayName = getDayName(dayOfWeek);
        
        var d=parseInt(dayOfWeek)+1;
        if(dayName){

        html+='<option value="'+d+'">'+dayName+'</option>';
        }
      }

      $(this).parents('.select-cl-align').next('div').find('select').html(html);
      $(this).parents('.select-cl-align').next('div').find('a').attr('data-day',html);
   
    }
    if(type=='inperson'){
    
       // console.log($(this).parents('.select-cl-align').next('div').find('select').attr('class'))
        var fromDates = new Date($(this).parents('.select-cl-align').find('.from_date').val());
   
        var html='<option value="">Select Days</option>';
        var i=0;
        for (var date = fromDates; date <= toDate; date.setDate(date.getDate()+1 )) {
            i++;
            var dayOfWeek = (date.getDay()-1);
            var dayName = getDayName(dayOfWeek);
            var d=parseInt(dayOfWeek)+1;
            if(dayName){
                html+='<option value="'+d+'">'+dayName+'</option>';

            }
          }
        
          $(this).parents('.select-cl-align').next('div').find('select').html(html);
          $(this).parents('.select-cl-align').next('div').find('a').attr('data-day',html);
         
    }
    removeDuplicateOptions();
    
})
function removeDuplicateOptions() {

    $('.form-select.date-ui option').each(function() {
      $(this).siblings('[value="'+$(this).val()+'"]').remove();
    });

  }

    function getDayName(dayIndex) {
      var daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      return daysOfWeek[dayIndex];
    }

   

    // $('.to_date').each(function(k,v){
    //     var fromdate=$(v).val();
    //     var id=$(v).attr('data-date-id');
    //     var type=$(v).attr('data-date-type');
    //     var toDate =   new Date($(v).val());  
        
    //     if(type=='online'){
     
    //     var fromDates = new Date($(v).parents('.onlinerangeappend').find('.reonline'+id).find('.from_date').val());
    //     var html='<option value="">Select Days</option>';
    //     // Loop through the days between the two dates
    //      for (var date = fromDates; date <= toDate; date.setDate(date.getDate() + 1)) {
    //         var dayOfWeek = date.getDay();
    //         var dayName = getDayName(dayOfWeek);
            
    //         var d=parseInt(dayOfWeek)+1;
    //         html+='<option value="'+d+'">'+dayName+'</option>';
    //       }
    
    //       $(v).parents('.select-cl-align').next('div').find('select').html(html);
    //       $(v).parents('.select-cl-align').next('div').find('a').attr('data-day',html);
       
    //     }
    //     if(type=='inperson'){
    //        // console.log($(this).parents('.select-cl-align').next('div').find('select').attr('class'))
    //         var fromDates = new Date($(v).parents('.select-cl-align').find('.from_date').val());
    //         var html='<option value="">Select Days</option>';
    //         var i=0;
    //         for (var date = fromDates; date <= toDate; date.setDate(date.getDate() + 1)) {
    //             i++;
    //             var dayOfWeek = date.getDay();
    //             var dayName = getDayName(dayOfWeek);
    //             var d=parseInt(dayOfWeek)+1;
    //             html+='<option value="'+d+'">'+dayName+'</option>';
    //           }
    //           $(v).parents('.select-cl-align').next('div').find('select').html(html);
        
    //     }
    // })



    