class DateConverterElement extends HTMLElement {
  constructor() {
    super();
    const timezone = this.getAttribute('convert-to');
    if (timezone && timezone !== 'local') {
      this.style.fontStyle = 'italic';
    }
  }

  connectedCallback() {
    const format = this.getAttribute('format');
    convertTimeAndSetContent(this, format, 'MM-DD-YYYY');
  }
}

class TimeConverterElement extends HTMLElement {
  constructor() {
    super();
    const timezone = this.getAttribute('convert-to');
    if (timezone && timezone !== 'local') {
      this.style.fontStyle = 'italic';
    }
  }

  connectedCallback() {
    const format = this.getAttribute('format');
    convertTimeAndSetContent(this, format, 'hh:mm A');
  }
}
class DateTimeConverterElement extends HTMLElement {
  constructor() {
    super();
    const timezone = this.getAttribute('convert-to');
    if (timezone && timezone !== 'local') {
      this.style.fontStyle = 'italic';
    }
  }

  connectedCallback() {
    const format = this.getAttribute('format');
    convertTimeAndSetContent(this, format, 'MM-DD-YYYY hh:mm A');
  }
}

customElements.define('whiz-datetime', DateTimeConverterElement);

customElements.define('whiz-time', TimeConverterElement);

customElements.define('whiz-date', DateConverterElement);

function convertTimeAndSetContent(element, format, defaultFormat) {
  const timezone = element.getAttribute('convert-to');
  const serverTimezone = element.getAttribute('convert-from') || window.serverTimezone;
  const userTimezone = !timezone || timezone === 'local'
    ? Intl.DateTimeFormat().resolvedOptions().timeZone
    : timezone;
  const serverTime = element.getAttribute('date');

  if (serverTime) {
    const serverTimeMoment = moment.tz(serverTime, serverTimezone);
    const setTime = element.getAttribute('set-time');
    if (setTime) {
      const [hour, minute, second] = setTime.split(":");
      serverTimeMoment.set({ hour, minute, second, millisecond: 0 });
    }
    const localTimeMoment = serverTimeMoment.clone().tz(userTimezone);
    const outputFormat = format || defaultFormat;
    const localTimeFormatted = localTimeMoment.format(outputFormat);

    element.textContent = localTimeFormatted;

    if (timezone && timezone !== 'local') {
      const localAbbreviation = getTimeZoneAbbreviation(Intl.DateTimeFormat().resolvedOptions().timeZone);
      const localTimeInLocalTimezone = localTimeMoment.clone().tz(Intl.DateTimeFormat().resolvedOptions().timeZone);
      element.title = `${localTimeInLocalTimezone.format(outputFormat)} (${localAbbreviation})`;
    }
  } else {
    console.warn('No server time found in the "date" attribute');
  }
}

function getTimeZoneAbbreviation(timezone) {
  const options = { timeZone: timezone, timeZoneName: 'short' };
  const formatter = new Intl.DateTimeFormat('en-US', options);
  const parts = formatter.formatToParts(new Date());
  const timeZoneName = parts.find(part => part.type === 'timeZoneName');
  return timeZoneName ? timeZoneName.value : timezone;
}

const timezoneElement = document.getElementById('timezone-element');
if(timezoneElement){
  const localAbbreviation = getTimeZoneAbbreviation(Intl.DateTimeFormat().resolvedOptions().timeZone);
  timezoneElement.innerHTML = `${Intl.DateTimeFormat().resolvedOptions().timeZone} (${localAbbreviation})`;
}